import pyautogui
import keyboard
import os
import time

# Set up the storage path and initial counter
storage_path = r"G:\captu"
counter = 101

# Ensure the storage directory exists
os.makedirs(storage_path, exist_ok=True)

def capture_and_save():
  global counter
  # Capture the primary monitor
  screenshot = pyautogui.screenshot()
  
  # Save the image
  filename = f"{counter:03d}.png"  # Format: 101.png, 102.png, etc.
  full_path = os.path.join(storage_path, filename)
  screenshot.save(full_path)
  print(f"Screenshot saved: {full_path}")
  
  counter += 1

print("Press 'p' to capture a screenshot of the primary monitor. Press 'q' to quit.")
while True:
  if keyboard.is_pressed('p'):
      capture_and_save()
      # Wait for key release to avoid multiple captures
      keyboard.wait('p', suppress=True, trigger_on_release=True)
      # Add a small delay to prevent accidental double-captures
      time.sleep(0.5)
  elif keyboard.is_pressed('q'):
      print("Quitting...")
      break