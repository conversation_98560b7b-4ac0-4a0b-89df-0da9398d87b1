import multiprocessing
import os
import time
import signal
import sys

# Get the current directory where all.py is located
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))

# Define the scripts to run with their paths
SCRIPTS = [
    {
        "name": "Code Typer",
        "path": os.path.join(CURRENT_DIR, "code_typer.py"),
        "delay": 0
    },
    {
        "name": "Voice AI Application",
        "path": os.path.join(CURRENT_DIR, "main.py"),
        "delay": 3
    }
]

def run_script(script_info):
    script_path = script_info["path"]
    script_name = script_info["name"]
    try:
        print(f"🚀 Starting {script_name}: {script_path}")
        if os.path.exists(script_path):
            os.system(f'py "{script_path}"')
        else:
            print(f"❌ Error: File not found - {script_path}")
    except Exception as e:
        print(f"❌ Error running {script_name}: {e}")

def signal_handler(signum, frame):
    print("\n🛑 Stopping all processes...")
    for process in processes:
        if process.is_alive():
            print(f"🔄 Terminating process: {process.name}")
            process.terminate()
            process.join(timeout=2)
    print("✅ All processes stopped")
    sys.exit(0)

if __name__ == '__main__':
    print("🎯 Voice AI + Code Typer Launcher")
    print("=" * 50)

    # Print current directory and file locations for debugging
    print(f"📁 Current directory: {CURRENT_DIR}")
    for script in SCRIPTS:
        print(f"🔍 Looking for {script['name']}: {script['path']}")
        exists = os.path.exists(script['path'])
        print(f"   {'✅ Found' if exists else '❌ Not Found'}")

    # Register signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)

    processes = []

    try:
        print("\n🚀 Starting applications...")

        # Start each script in a separate process
        for script in SCRIPTS:
            print(f"⏳ Starting {script['name']}...")
            p = multiprocessing.Process(target=run_script, args=(script,), name=script['name'])
            processes.append(p)
            p.start()

            if script["delay"] > 0:
                print(f"⏱️  Waiting {script['delay']} seconds before starting next...")
                time.sleep(script["delay"])

        print("\n✅ All applications started successfully!")
        print("\n📋 Applications running:")
        for i, script in enumerate(SCRIPTS):
            print(f"   {i+1}. {script['name']}")

        print("\n🎮 Controls:")
        print("   • Code Typer: Insert (start/pause), Delete (stop), F10 (toggle monitoring)")
        print("   • Voice AI: Left Arrow (listen), Down Arrow (stop & send), Right Arrow (stop AI)")
        print("   • Press Ctrl+C to stop all applications")

        # Wait for all processes to complete
        for p in processes:
            p.join()

    except KeyboardInterrupt:
        print("\n🛑 Received interrupt signal")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        print(f"❌ Error in main process: {e}")
        # Cleanup on error
        for p in processes:
            if p.is_alive():
                p.terminate()
        sys.exit(1)
