from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import openai
import queue
import threading
import assemblyai as aai
import time
import json
import logging
from concurrent.futures import ThreadPoolExecutor
import signal
import sys
import asyncio
from collections import deque
import os
from pydantic import BaseModel
import datetime
import uvicorn

app = FastAPI()
app.add_middleware(
  CORSMiddleware,
  allow_origins=["*"],
  allow_credentials=True,
  allow_methods=["*"],
  allow_headers=["*"],
)

# Disable logging for specific modules
logging.getLogger('websockets').setLevel(logging.CRITICAL)
logging.getLogger('httpx').setLevel(logging.CRITICAL)
logging.getLogger('assemblyai').setLevel(logging.CRITICAL)
logging.getLogger('openai').setLevel(logging.CRITICAL)

# Set the global logging level to CRITICAL
logging.basicConfig(level=logging.DEBUG)

# Setup API keys (Replace with your actual keys)
aai.settings.api_key = "73384b4e35614fd5998acbb43324e822"
openai.api_key = "********************************************************"

transcript_queue = queue.Queue()
response_queue = queue.Queue()
transcriber = None
stop_event = threading.Event()
response_thread = None
buffered_transcript = ""
last_transcript = ""
project_details = None
is_transcribing = False

executor = ThreadPoolExecutor(max_workers=4)

# In-memory storage for transcripts and responses using deque
transcripts = deque(maxlen=100)  # Keep only the latest 100 transcripts
responses = deque(maxlen=100)  # Keep only the latest 100 responses
last_questions = deque(maxlen=2)  # Keep the last 2 questions

class ProjectDetails(BaseModel):
  details: str

class ManualInput(BaseModel):
  input: str

def get_session_filename():
  now = datetime.datetime.now()
  filename = f"session_{now.strftime('%Y%m%d_%H%M%S')}.txt"
  filepath = os.path.join("session_logs", filename)
  return filename, filepath

# Create global variables to store the session filename and filepath
session_filename, session_filepath = get_session_filename()

# Create a global variable to store the session filename
session_filename = get_session_filename()

def write_question_to_file(question):
  global session_filepath
  os.makedirs("session_logs", exist_ok=True)
  with open(session_filepath, "a") as f:
      f.write(f"{datetime.datetime.now()}: {question}\n")

@app.post('/set_project_details')
async def set_project_details(details: ProjectDetails):
  global project_details
  project_details = details.details
  return JSONResponse(content={'status': 'project details set'})

@app.post('/manual_input')
async def manual_input(input_data: ManualInput):
  global transcript_queue, last_questions
  user_input = input_data.input
  transcript_queue.put({"role": "user", "content": user_input})
  last_questions.append(user_input)
  write_question_to_file(user_input)
  return JSONResponse(content={'status': 'manual input received'})

def on_data(transcript: aai.RealtimeTranscript):
  global buffered_transcript, last_transcript
  if not transcript.text or transcript.text == last_transcript:
      return
  last_transcript = transcript.text
  buffered_transcript += " " + transcript.text.strip()  # Append the transcript text to the buffer

def on_error(error: aai.RealtimeError):
  logging.error(f"An error occurred: {error}")

def handle_transcription():
  global transcriber, is_transcribing
  try:
      transcriber = aai.RealtimeTranscriber(
          on_data=on_data,
          on_error=on_error,
          sample_rate=44_100,
      )
      transcriber.connect()
      microphone_stream = aai.extras.MicrophoneStream()
      while not stop_event.is_set():
          if is_transcribing:
              transcriber.stream(microphone_stream)
          else:
              time.sleep(0.1)  # Small delay to prevent busy-waiting
  except Exception as e:
      logging.error(f"Failed to connect to real-time service: {e}")

def handle_responses():
  global stop_event, buffered_transcript, project_details, last_questions
  prompt =  """You are participating in an interview for a Senior Data Scientist position with MLOPS deployment experience. During this interview, you will be provided with "project_details" and asked questions based on them. Your task is to determine which "project_details" are relevant to the question
    and provide an appropriate response. If the "project_details" are not relevant, answer generally, showcasing your skills and expertise as a Senior Data Scientist.

    **Part 1: Concise Answer**

    - Provide a direct and crisp answer to the question.showcasing your skills and expertise as a Senior Data Scientist.
    - If the question involves coding, respond with the code only, using Python or SQL. The code should be practical, using realistic names and data types, as if it were for a production environment.
    - Ensure that the code is executable in Google Colab, including a print statement to display the expected output. Do not include comments or explanations within the code.

    **Part 2: Detailed Explanation**
    - explain answer in detail way as in interview. explain with python libraries and metrics, Dont use code until its asked, explain more in words, moe techicnal way,  end to end with in detail minor steps also. 
    - showcasing your skills and expertise as a Senior Data Scientist, Offer a detailed explanation of the steps you took, especially in relation to the "project_details."
    - If the question involved code, explain each line and its purpose."""

  messages = [
        {"role": "system", "content": prompt},
        {"role": "system", "content": f"Project Details: {project_details}"}
    ]    
  while not stop_event.is_set():
      try:
          transcript = transcript_queue.get(timeout=0.1)
          response_queue.put(transcript)  # Display the user message first
          
          # Add the new question to the last_questions deque
          if transcript["role"] == "user":
              last_questions.append(transcript["content"])
          
          # Include the last 2 questions in the context
          context = list(last_questions)
          context_message = {"role": "system", "content": f"Previous questions: {' | '.join(context)}"}
          messages.append(context_message)
          messages.append(transcript)
          
          # Save the question to file
          if transcript["role"] == "user":
              write_question_to_file(transcript["content"])
          
          response = openai.ChatCompletion.create(
              model='gpt-4o-2024-08-06',
              messages=messages,
              stream=True
          )
          collected_text = ""
          for chunk in response:
              if stop_event.is_set():
                  break  # Exit the loop immediately if stop_event is set
              text = chunk['choices'][0]['delta'].get('content', '')
              if text:
                  collected_text += text
                  response_queue.put({"role": "ai", "content": collected_text})
          response_queue.put({"role": "ai", "content": "END_OF_RESPONSE"})
          messages.append({"role": "assistant", "content": collected_text})
          if len(messages) > 12:  # Limit context to last 12 messages (including the 2 context messages)
              messages = messages[-12:]
      except queue.Empty:
          continue
  buffered_transcript = ""  # Clear the buffer after handling responses

@app.get('/')
async def index():
  try:
      with open(os.path.join(os.path.dirname(__file__), 'templates', 'index.html'), 'r') as file:
          html_code = file.read()
      return Response(content=html_code, media_type="text/html")
  except Exception as e:
      logging.error(f"Error reading frontend.html: {e}")
      return JSONResponse(content={'error': 'Failed to load the front-end'}, status_code=500)

@app.post('/start_transcription')
async def start_transcription():
  global transcriber, is_transcribing
  is_transcribing = True
  if transcriber is None:
      executor.submit(handle_transcription)
  return JSONResponse(content={'status': 'transcription started'})

@app.post('/stop_transcription')
async def stop_transcription():
  global transcriber, buffered_transcript, is_transcribing, last_questions
  is_transcribing = False
  if buffered_transcript.strip():
      transcript_queue.put({"role": "user", "content": buffered_transcript.strip()})
      transcripts.append(buffered_transcript.strip())
      last_questions.append(buffered_transcript.strip())
      write_question_to_file(buffered_transcript.strip())
  buffered_transcript = ""  # Clear the buffer after stopping transcription
  return JSONResponse(content={'status': 'transcription stopped'})

@app.post('/send_to_ai')
async def send_to_ai():
  global stop_event, response_thread
  stop_event.clear()
  response_thread = threading.Thread(target=handle_responses)
  response_thread.daemon = True
  response_thread.start()
  return JSONResponse(content={'status': 'sent to AI'})

@app.post('/stop_ai_response')
async def stop_ai_response():
  global stop_event, response_thread, is_transcribing
  stop_event.set()
  if response_thread is not None:
      response_thread.join(timeout=0.1)  # Use a shorter timeout to avoid blocking for too long
  is_transcribing = True  # Start transcription after stopping AI response
  if transcriber is None:
      executor.submit(handle_transcription)
  return JSONResponse(content={'status': 'AI response stopped and transcription started'})

@app.get('/get_response')
async def get_response():
  def generate():
      while True:
          try:
              response_text = response_queue.get(timeout=0.1)
              yield f"data: {json.dumps(response_text)}\n\n"
          except queue.Empty:
              continue
  return StreamingResponse(generate(), media_type='text/event-stream')

def signal_handler(sig, frame):
  global stop_event, response_thread
  print('Interrupt received, stopping...')
  stop_event.set()
  if response_thread is not None:
      response_thread.join(timeout=0.1)  # Use a shorter timeout to avoid blocking for too long
  sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

if __name__ == '__main__':
  uvicorn.run(app, host="0.0.0.0", port=5001)