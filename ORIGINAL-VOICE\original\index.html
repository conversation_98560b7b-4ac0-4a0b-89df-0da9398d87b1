<!DOCTYPE html>
<html>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <style>
        body, html {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 100%;
            height: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            box-sizing: border-box;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-top: 0;
        }
        .button-container {
            display: flex;
            height: 10%;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }
        .button-container, .intro-container {
            padding: 8px 12px;
            font-size: 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary, .btn-success, .btn-danger {
            padding: 8px 12px;
            font-size: 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-primary:hover, .btn-success:hover, .btn-danger:hover {
            opacity: 0.8;
        }
        #projectDetails {
            flex-grow: 1;
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .listening-indicator {
            display: none;
            color: #28a745;
            font-weight: bold;
            margin-left: 10px;
        }
        .chat-container {
            border: 1px solid #ccc;
            border-radius: 5px;
            height: 90%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .chat-box {
            flex-grow: 1;
            overflow-y: auto;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .user-message, .bot-message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            max-width: 100%;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .bot-message {
            background-color: #007bff;
            color: white;
            max-width: 75%;
            margin-left: 25%;
            align-self: flex-start;
            
        }
        .user-message {
            background-color: #e9ecef;
            color: #333;
            align-self: flex-start;
            font-family: Calibri, sans-serif;
            font-size: 12pt

        }
        .input-container {
            display: flex;
            padding: 10px;
            background-color: #f0f0f0;
        }
        #manualInput {
            flex-grow: 1;
            padding: 8px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 5px 0 0 5px;
        }
        .input-container button {
            padding: 8px 15px;
            font-size: 14px;
            border: none;
            background-color: #007bff;
            color: white;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="intro-container">
            <input type="text" id="introMessage" placeholder="Enter intro message">
            <button type="button" class="btn-primary" onclick="setIntroMessage()">Set Intro Message</button>
        </div>
        <div class="button-container">
            <input type="text" id="projectDetails" placeholder="Enter project details">
            <button type="button" class="btn-primary" onclick="setProjectDetails()">Set Project Details</button>
            <button type="button" class="btn-primary" onclick="startListening()">Start Listening (Press Left Arrow)</button>
            <button type="button" class="btn-success" onclick="stopListeningAndSend()">Stop and Send to AI (Press Down Arrow)</button>
            <button type="button" class="btn-danger" onclick="stopAIResponse()">Stop AI Response (Press Right Arrow)</button>
            <span id="listeningIndicator" class="listening-indicator">Listening...</span>
        </div>
        <div class="chat-container">
            <div id="result" class="chat-box"></div>
            <div class="input-container">
                <input type="text" id="manualInput" placeholder="Type your message...">
                <button type="button" class="btn-primary" onclick="sendManualInput()">Send</button>
            </div>
        </div>
    </div>
    <script>
        var isListening = false;
        var eventSource;
        var currentUserMessage = null;
        var currentAIMessage = null;
        var latestUserMessage = '';
        var baseUrl = window.location.origin;
        var introMessage = '';
        var introMessageSet = false;

        document.body.onkeyup = function(e) {
            if (e.keyCode == 37) { // Left arrow key
                startListening();
            } else if (e.keyCode == 40) { // Down arrow key
                stopListeningAndSend();
            } else if (e.keyCode == 39) { // Right arrow key
                stopAIResponse();
            }
        };

        function setIntroMessage() {
            introMessage = document.getElementById('introMessage').value;
            if (introMessage.trim() !== '') {
                introMessageSet = true;
                document.getElementById('introMessage').disabled = true;
                displayIntroMessage();
            }
        }

        function displayIntroMessage() {
            if (introMessageSet) {
                var resultDiv = document.getElementById('result');
                var introDiv = document.createElement('div');
                introDiv.className = 'bot-message';
                introDiv.textContent = introMessage;
                resultDiv.insertBefore(introDiv, resultDiv.firstChild);
                scrollToBottom();
            }
        }

        function setProjectDetails() {
            var projectDetails = document.getElementById('projectDetails').value;
            fetch(baseUrl + '/set_project_details', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ details: projectDetails })
            })
            .then(response => response.json())
            .then(data => console.log(data))
            .catch(error => console.error('Error:', error));
        }

        function startListening() {
            isListening = true;
            document.getElementById('listeningIndicator').style.display = 'inline';
            console.log('Voice recognition started. Speak now.');
            fetch(baseUrl + '/start_transcription', { method: 'POST' })
                .then(response => response.json())
                .then(data => console.log(data))
                .catch(error => console.error('Error:', error));
            startEventSource();
        }

        function stopListeningAndSend() {
            if (isListening) {
                isListening = false;
                document.getElementById('listeningIndicator').style.display = 'none';
                console.log('Voice recognition stopped.');
                fetch(baseUrl + '/stop_transcription', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => console.log(data))
                    .catch(error => console.error('Error:', error));
            }
            sendToAI();
            scrollToBottom();
        }

        function stopAIResponse() {
            fetch(baseUrl + '/stop_ai_response', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    console.log(data);
                    startListening(); // Automatically start listening again
                })
                .catch(error => console.error('Error:', error));
        }

        function sendToAI() {
            fetch(baseUrl + '/send_to_ai', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                console.log(data);
            })
            .catch(error => console.error('Error:', error));
        }

        function sendManualInput() {
            var manualInput = document.getElementById('manualInput');
            var inputText = manualInput.value.trim();
            if (inputText !== '') {
                latestUserMessage = inputText; // Update latest user message
                fetch(baseUrl + '/manual_input', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ input: inputText })
                })
                .then(response => response.json())
                .then(data => {
                    console.log(data);
                    manualInput.value = ''; // Clear the input field
                    sendToAI(); // Send the manual input to AI for processing
                })
                .catch(error => console.error('Error:', error));
            }
        }

        function startEventSource() {
            if (eventSource) {
                eventSource.close(); // Close any existing EventSource connection
                eventSource = null; // Ensure eventSource is set to null
            }
            eventSource = new EventSource(baseUrl + '/get_response');
            eventSource.onmessage = function(event) {
                var data = JSON.parse(event.data);
                var resultDiv = document.getElementById('result');
                
                // Display intro message if it hasn't been displayed yet
                if (!introMessageSet) {
                    displayIntroMessage();
                }

                if (data.role === 'user') {
                    if (!currentUserMessage) {
                        currentUserMessage = document.createElement('div');
                        currentUserMessage.className = 'user-message';
                        resultDiv.appendChild(currentUserMessage);
                    }
                    currentUserMessage.textContent = data.content;
                    latestUserMessage = data.content; // Store the latest user message
                } else if (data.role === 'ai') {
                    if (data.content === "END_OF_RESPONSE") {
                        currentUserMessage = null;
                        currentAIMessage = null;
                    } else {
                        if (!currentAIMessage) {
                            currentAIMessage = document.createElement('div');
                            currentAIMessage.className = 'bot-message';
                            resultDiv.appendChild(currentAIMessage);
                        }
                        currentAIMessage.textContent = data.content;
                    }
                }
                limitMessages(); // Call the function to limit messages
                if (shouldScrollToBottom()) {
                    scrollToBottom();
                }
            };
            eventSource.onerror = function(event) {
                console.error('EventSource error:', event);
            };
        }

        function limitMessages() {
            var chatBox = document.getElementById('result');
            var messages = chatBox.children;
            while (messages.length > 6) {
                chatBox.removeChild(messages[0]);
            }
        }

        function scrollToBottom() {
            var chatBox = document.getElementById('result');
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        function shouldScrollToBottom() {
            var chatBox = document.getElementById('result');
            return chatBox.scrollHeight - chatBox.clientHeight <= chatBox.scrollTop + 1;
        }

        // Call displayIntroMessage when the page loads
        window.onload = function() {
            displayIntroMessage();
        };
    </script>
</body>
</html>