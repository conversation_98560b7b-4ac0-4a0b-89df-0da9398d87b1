2024-09-04 17:58:19.738799: what what's the what's the difference what's the difference i mean what's the difference i mean where what's the difference i mean where did what's the difference i mean where did you what's the difference i mean where did you use What's the difference? I mean, where did you use. hadoop hadoop in your hadoop in your projects Hadoop in your projects. and how and how did you connect And how did you connect? with your
2024-09-04 17:58:19.742817: what what's the what's the difference what's the difference i mean what's the difference i mean where what's the difference i mean where did what's the difference i mean where did you what's the difference i mean where did you use What's the difference? I mean, where did you use. hadoop hadoop in your hadoop in your projects Hadoop in your projects. and how and how did you connect And how did you connect? with your
2024-09-04 17:59:40.234667: in in which format In which format? generally Generally. if you have if you have in your if you have in your first project If you have in your first project. i have i have the data i have the data in i have the data in videos i have the data in videos that i have the data in videos that's stored in i have the data in videos that's stored in hadoop i have the data in videos that's stored in hadoop so i have the data in videos that's stored in hadoop so how did you i have the data in videos that's stored in hadoop so how did you get i have the data in videos that's stored in hadoop so how did you get that i have the data in videos that's stored in hadoop so how did you get that hadoop i have the data in videos that's stored in hadoop so how did you get that hadoop two i have the data in videos that's stored in hadoop so how did you get that hadoop two in our i have the data in videos that's stored in hadoop so how did you get that hadoop two in our local I have the data in videos that's stored in Hadoop. So how did you get. That hadoop two in our local. azure Azure. data database
2024-09-04 17:59:40.240943: in in which format In which format? generally Generally. if you have if you have in your if you have in your first project If you have in your first project. i have i have the data i have the data in i have the data in videos i have the data in videos that i have the data in videos that's stored in i have the data in videos that's stored in hadoop i have the data in videos that's stored in hadoop so i have the data in videos that's stored in hadoop so how did you i have the data in videos that's stored in hadoop so how did you get i have the data in videos that's stored in hadoop so how did you get that i have the data in videos that's stored in hadoop so how did you get that hadoop i have the data in videos that's stored in hadoop so how did you get that hadoop two i have the data in videos that's stored in hadoop so how did you get that hadoop two in our i have the data in videos that's stored in hadoop so how did you get that hadoop two in our local I have the data in videos that's stored in Hadoop. So how did you get. That hadoop two in our local. azure Azure. data database
2024-09-04 18:00:06.333423: i i'm asking I'm asking. which form which format which format it had which format it had actually which format it had actually in hadoop which format it had actually in hadoop in which which format it had actually in hadoop in which format which format it had actually in hadoop in which format they which format it had actually in hadoop in which format they stored Which format it had actually in Hadoop, in which format they stored. those video those video files Those video files. in
2024-09-04 18:00:06.342081: i i'm asking I'm asking. which form which format which format it had which format it had actually which format it had actually in hadoop which format it had actually in hadoop in which which format it had actually in hadoop in which format which format it had actually in hadoop in which format they which format it had actually in hadoop in which format they stored Which format it had actually in Hadoop, in which format they stored. those video those video files Those video files. in
2024-09-04 18:03:54.673888: connect connectors Connectors. hi hi son hi sony Hi, sony. hey hey bro Hey, bro. just give me just give me a moment Just give me a moment. hi somb hi sombia hi hi sombia hi amada hi sombia hi amada thank you for hi sombia hi amada thank you for joining Hi, Sombia. Hi, Amada. Thank you for joining. yeah Yeah. all right All right. i'm going to i'm going to start I'm going to start. okay okay this is okay this is for the okay this is for the power okay this is for the power gear Okay. This is for the power gear. you can start you can start by giving you can start by giving an introduct you can start by giving an introduction you can start by giving an introduction about You can start by giving an introduction about. your your current ro your current roles and resp your current roles and responsibilities your current roles and responsibilities in your current roles and responsibilities in the present organ your current roles and responsibilities in the present organization your current roles and responsibilities in the present organization and also your current roles and responsibilities in the present organization and also from your current roles and responsibilities in the present organization and also from a report your current roles and responsibilities in the present organization and also from a reporting stand your current roles and responsibilities in the present organization and also from a reporting standpoint Your current roles and responsibilities in the present organization and also from a reporting standpoint. at what level at what level do you at what level do you oper at what level do you operate at what level do you operate when comes at what level do you operate when comes to ssr at what level do you operate when comes to ssrs and power at what level do you operate when comes to ssrs and power bi at what level do you operate when comes to ssrs and power bi so that at what level do you operate when comes to ssrs and power bi so that i have an idea at what level do you operate when comes to ssrs and power bi so that i have an idea as to at what level do you operate when comes to ssrs and power bi so that i have an idea as to proced at what level do you operate when comes to ssrs and power bi so that i have an idea as to procedure that at what level do you operate when comes to ssrs and power bi so that i have an idea as to procedure that too At what level do you operate when comes to ssrs and power, bi? So that I have an idea as to procedure. That too. okay Okay. yeah yeah myself yeah myself amana and then yeah myself amana and then i'm yeah myself amana and then i'm having total yeah myself amana and then i'm having totally eight point one yeah myself amana and then i'm having totally eight point one years of yeah myself amana and then i'm having totally eight point one years of experience yeah myself amana and then i'm having totally eight point one years of experience in it yeah myself amana and then i'm having totally eight point one years of experience in it and then yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come around yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come around four yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come around four years Yeah, myself, Amana. And then I'm having totally 8.1 years of experience in it and then a relevant experience with the data science and AML and everything's come around four years. most mostly i mostly i worked with mostly i worked with python mostly i worked with python libraries mostly i worked with python libraries like mostly i worked with python libraries like i have mostly i worked with python libraries like i have used like numpy mostly i worked with python libraries like i have used like numpy pan mostly i worked with python libraries like i have used like numpy pandas sci mostly i worked with python libraries like i have used like numpy pandas sci fi mostly i worked with python libraries like i have used like numpy pandas sci fi nltk mostly i worked with python libraries like i have used like numpy pandas sci fi nltk and soft mostly i worked with python libraries like i have used like numpy pandas sci fi nltk and softco space mostly i worked with python libraries like i have used like numpy pandas sci fi nltk and softco spacey Mostly I worked with python libraries. Like I have used like numpy pandas Sci-Fi nltk and softco spacey. and and cycling and cycling those kind of and cycling those kind of things and cycling those kind of things mostly i have used and cycling those kind of things mostly i have used for data and cycling those kind of things mostly i have used for data visualization and cycling those kind of things mostly i have used for data visualization part i have used and cycling those kind of things mostly i have used for data visualization part i have used like and cycling those kind of things mostly i have used for data visualization part i have used like mat probably and cycling those kind of things mostly i have used for data visualization part i have used like mat probably c and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plot and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm experienced with and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm experienced with cycle analysis and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm experienced with cycle analysis as well And cycling, those kind of things. Mostly I have used for data visualization. Part I have used like Mat, probably cborn plotly and power ba I'm experienced with cycle analysis as well. including including like a hypoth including like a hypothesis test including like a hypothesis testing and prob including like a hypothesis testing and probability the including like a hypothesis testing and probability theory and including like a hypothesis testing and probability theory and regression including like a hypothesis testing and probability theory and regression anal including like a hypothesis testing and probability theory and regression analysis including like a hypothesis testing and probability theory and regression analysis and including like a hypothesis testing and probability theory and regression analysis and risk including like a hypothesis testing and probability theory and regression analysis and risk assess including like a hypothesis testing and probability theory and regression analysis and risk assessment including like a hypothesis testing and probability theory and regression analysis and risk assessment those things including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in mach including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with reg including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and cluster including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommend including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation system including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things have including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things have all gone including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things have all gone okay Including like a hypothesis testing and probability theory and regression analysis and risk assessment. Those things have worked. On in machine learning. I worked mostly with regression class, cage and clustering and recommendation systems. Those things have all gone okay. and and it'll be and it'll be mostly and it'll be mostly about with and it'll be mostly about with tenso and it'll be mostly about with tensorflow and it'll be mostly about with tensorflow spacey and it'll be mostly about with tensorflow spacey bird and it'll be mostly about with tensorflow spacey bird and pyt and it'll be mostly about with tensorflow spacey bird and pytorch and it'll be mostly about with tensorflow spacey bird and pytorch openc and it'll be mostly about with tensorflow spacey bird and pytorch opencv those and it'll be mostly about with tensorflow spacey bird and pytorch opencv those things and it'll be mostly about with tensorflow spacey bird and pytorch opencv those things have worked and it'll be mostly about with tensorflow spacey bird and pytorch opencv those things have worked on And it'll be mostly about with Tensorflow, Spacey Bird and pytorch. Opencv those things have worked on. i i worked with i worked with the dv i worked with the dv section I worked with the DV section. sorry sorry for sorry for cutting you off sorry for cutting you off so with sorry for cutting you off so with regards sorry for cutting you off so with regards to sorry for cutting you off so with regards to your current sorry for cutting you off so with regards to your current work experience sorry for cutting you off so with regards to your current work experience as a data scientist sorry for cutting you off so with regards to your current work experience as a data scientist right can you sorry for cutting you off so with regards to your current work experience as a data scientist right can you talk sorry for cutting you off so with regards to your current work experience as a data scientist right can you talk to me about Sorry for cutting you off. So, with regards to your current work experience as a data scientist, right. Can you talk to me about? a current project a current project that a current project that you are a current project that you are involved in A current project that you are involved in. and And. what was what was the what was the scope What was the scope? what was the what was the use case What was the use case? how how to go about how to go about imple how to go about implementing it how to go about implementing it how do you how to go about implementing it how do you optimize how to go about implementing it how do you optimize the model how to go about implementing it how do you optimize the model performance How to go about implementing it? How do you optimize the model performance? so that So that. just to get it just to get it you can just to get it you can have to go just to get it you can have to go in detail
2024-09-04 18:03:54.679020: connect connectors Connectors. hi hi son hi sony Hi, sony. hey hey bro Hey, bro. just give me just give me a moment Just give me a moment. hi somb hi sombia hi hi sombia hi amada hi sombia hi amada thank you for hi sombia hi amada thank you for joining Hi, Sombia. Hi, Amada. Thank you for joining. yeah Yeah. all right All right. i'm going to i'm going to start I'm going to start. okay okay this is okay this is for the okay this is for the power okay this is for the power gear Okay. This is for the power gear. you can start you can start by giving you can start by giving an introduct you can start by giving an introduction you can start by giving an introduction about You can start by giving an introduction about. your your current ro your current roles and resp your current roles and responsibilities your current roles and responsibilities in your current roles and responsibilities in the present organ your current roles and responsibilities in the present organization your current roles and responsibilities in the present organization and also your current roles and responsibilities in the present organization and also from your current roles and responsibilities in the present organization and also from a report your current roles and responsibilities in the present organization and also from a reporting stand your current roles and responsibilities in the present organization and also from a reporting standpoint Your current roles and responsibilities in the present organization and also from a reporting standpoint. at what level at what level do you at what level do you oper at what level do you operate at what level do you operate when comes at what level do you operate when comes to ssr at what level do you operate when comes to ssrs and power at what level do you operate when comes to ssrs and power bi at what level do you operate when comes to ssrs and power bi so that at what level do you operate when comes to ssrs and power bi so that i have an idea at what level do you operate when comes to ssrs and power bi so that i have an idea as to at what level do you operate when comes to ssrs and power bi so that i have an idea as to proced at what level do you operate when comes to ssrs and power bi so that i have an idea as to procedure that at what level do you operate when comes to ssrs and power bi so that i have an idea as to procedure that too At what level do you operate when comes to ssrs and power, bi? So that I have an idea as to procedure. That too. okay Okay. yeah yeah myself yeah myself amana and then yeah myself amana and then i'm yeah myself amana and then i'm having total yeah myself amana and then i'm having totally eight point one yeah myself amana and then i'm having totally eight point one years of yeah myself amana and then i'm having totally eight point one years of experience yeah myself amana and then i'm having totally eight point one years of experience in it yeah myself amana and then i'm having totally eight point one years of experience in it and then yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come around yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come around four yeah myself amana and then i'm having totally eight point one years of experience in it and then a relevant experience with the data science and aml and everything's come around four years Yeah, myself, Amana. And then I'm having totally 8.1 years of experience in it and then a relevant experience with the data science and AML and everything's come around four years. most mostly i mostly i worked with mostly i worked with python mostly i worked with python libraries mostly i worked with python libraries like mostly i worked with python libraries like i have mostly i worked with python libraries like i have used like numpy mostly i worked with python libraries like i have used like numpy pan mostly i worked with python libraries like i have used like numpy pandas sci mostly i worked with python libraries like i have used like numpy pandas sci fi mostly i worked with python libraries like i have used like numpy pandas sci fi nltk mostly i worked with python libraries like i have used like numpy pandas sci fi nltk and soft mostly i worked with python libraries like i have used like numpy pandas sci fi nltk and softco space mostly i worked with python libraries like i have used like numpy pandas sci fi nltk and softco spacey Mostly I worked with python libraries. Like I have used like numpy pandas Sci-Fi nltk and softco spacey. and and cycling and cycling those kind of and cycling those kind of things and cycling those kind of things mostly i have used and cycling those kind of things mostly i have used for data and cycling those kind of things mostly i have used for data visualization and cycling those kind of things mostly i have used for data visualization part i have used and cycling those kind of things mostly i have used for data visualization part i have used like and cycling those kind of things mostly i have used for data visualization part i have used like mat probably and cycling those kind of things mostly i have used for data visualization part i have used like mat probably c and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plot and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm experienced with and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm experienced with cycle analysis and cycling those kind of things mostly i have used for data visualization part i have used like mat probably cborn plotly and power ba i'm experienced with cycle analysis as well And cycling, those kind of things. Mostly I have used for data visualization. Part I have used like Mat, probably cborn plotly and power ba I'm experienced with cycle analysis as well. including including like a hypoth including like a hypothesis test including like a hypothesis testing and prob including like a hypothesis testing and probability the including like a hypothesis testing and probability theory and including like a hypothesis testing and probability theory and regression including like a hypothesis testing and probability theory and regression anal including like a hypothesis testing and probability theory and regression analysis including like a hypothesis testing and probability theory and regression analysis and including like a hypothesis testing and probability theory and regression analysis and risk including like a hypothesis testing and probability theory and regression analysis and risk assess including like a hypothesis testing and probability theory and regression analysis and risk assessment including like a hypothesis testing and probability theory and regression analysis and risk assessment those things including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in mach including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with reg including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and cluster including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommend including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation system including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things have including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things have all gone including like a hypothesis testing and probability theory and regression analysis and risk assessment those things have worked on in machine learning i worked mostly with regression class cage and clustering and recommendation systems those things have all gone okay Including like a hypothesis testing and probability theory and regression analysis and risk assessment. Those things have worked. On in machine learning. I worked mostly with regression class, cage and clustering and recommendation systems. Those things have all gone okay. and and it'll be and it'll be mostly and it'll be mostly about with and it'll be mostly about with tenso and it'll be mostly about with tensorflow and it'll be mostly about with tensorflow spacey and it'll be mostly about with tensorflow spacey bird and it'll be mostly about with tensorflow spacey bird and pyt and it'll be mostly about with tensorflow spacey bird and pytorch and it'll be mostly about with tensorflow spacey bird and pytorch openc and it'll be mostly about with tensorflow spacey bird and pytorch opencv those and it'll be mostly about with tensorflow spacey bird and pytorch opencv those things and it'll be mostly about with tensorflow spacey bird and pytorch opencv those things have worked and it'll be mostly about with tensorflow spacey bird and pytorch opencv those things have worked on And it'll be mostly about with Tensorflow, Spacey Bird and pytorch. Opencv those things have worked on. i i worked with i worked with the dv i worked with the dv section I worked with the DV section. sorry sorry for sorry for cutting you off sorry for cutting you off so with sorry for cutting you off so with regards sorry for cutting you off so with regards to sorry for cutting you off so with regards to your current sorry for cutting you off so with regards to your current work experience sorry for cutting you off so with regards to your current work experience as a data scientist sorry for cutting you off so with regards to your current work experience as a data scientist right can you sorry for cutting you off so with regards to your current work experience as a data scientist right can you talk sorry for cutting you off so with regards to your current work experience as a data scientist right can you talk to me about Sorry for cutting you off. So, with regards to your current work experience as a data scientist, right. Can you talk to me about? a current project a current project that a current project that you are a current project that you are involved in A current project that you are involved in. and And. what was what was the what was the scope What was the scope? what was the what was the use case What was the use case? how how to go about how to go about imple how to go about implementing it how to go about implementing it how do you how to go about implementing it how do you optimize how to go about implementing it how do you optimize the model how to go about implementing it how do you optimize the model performance How to go about implementing it? How do you optimize the model performance? so that So that. just to get it just to get it you can just to get it you can have to go just to get it you can have to go in detail
2024-09-04 18:08:57.046834: to to our delphi to our delphi system to our delphi system that accurately to our delphi system that accurately identify to our delphi system that accurately identify and separate to our delphi system that accurately identify and separate the different to our delphi system that accurately identify and separate the different plastic to our delphi system that accurately identify and separate the different plastic types to our delphi system that accurately identify and separate the different plastic types so to our delphi system that accurately identify and separate the different plastic types so before going to our delphi system that accurately identify and separate the different plastic types so before going to the wast to our delphi system that accurately identify and separate the different plastic types so before going to the waste manage to our delphi system that accurately identify and separate the different plastic types so before going to the waste management to our delphi system that accurately identify and separate the different plastic types so before going to the waste management i mean To our Delphi system that accurately identify and separate the different plastic types. So before going to the waste management, I mean. when they are mix when they are mixed together When they are mixed together. before going before going to the before going to the recycling before going to the recycling process before going to the recycling process in the same before going to the recycling process in the same pl before going to the recycling process in the same plastic before going to the recycling process in the same plastic there are before going to the recycling process in the same plastic there are eight type of before going to the recycling process in the same plastic there are eight type of plastics before going to the recycling process in the same plastic there are eight type of plastics will be there so before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the rec before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plast before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pv before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ld before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ldpc before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ldpc different before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ldpc different kind of Before going to the recycling process. In the same plastic, there are eight type of plastics will be there. So we need to identify based on the images, actually. So what you do is like it. Will decrease the operation cost and while doing the recycling. So there are kind of eight kind of. Plastics related like PT, HTTP, PVC, LDPC, different kind of. plastics plastics will be plastics will be there so plastics will be there so what we have plastics will be there so what we have to do is plastics will be there so what we have to do is we need plastics will be there so what we have to do is we need to plastics will be there so what we have to do is we need to sub plastics will be there so what we have to do is we need to subtract plastics will be there so what we have to do is we need to subtract them and plastics will be there so what we have to do is we need to subtract them and for plastics will be there so what we have to do is we need to subtract them and for the data colle plastics will be there so what we have to do is we need to subtract them and for the data collection part plastics will be there so what we have to do is we need to subtract them and for the data collection part we plastics will be there so what we have to do is we need to subtract them and for the data collection part we gone with Plastics will be there. So what we have to do is we need to subtract them. And for the data collection part, we gone with. image image we have to cap image we have to capture the imag image we have to capture the images Image. We have to capture the images. we will get kind of we will get kind of videos we will get kind of videos format we will get kind of videos format like twenty we will get kind of videos format like twenty seconds of we will get kind of videos format like twenty seconds of videos we will get kind of videos format like twenty seconds of videos we'll get we will get kind of videos format like twenty seconds of videos we'll get we need to we will get kind of videos format like twenty seconds of videos we'll get we need to identify we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of rand we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in open we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in openc we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so to identify we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so to identify the we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so to identify the frames We will get kind of videos format, like 20 seconds of videos we'll get. We need. To identify particular images. Actually, instead of randomly, we use kind of a deduction algorithm, like such. As background subtraction, which is available in opencd. So to identify the frames. which which you kind of which you kind of frame which you kind of frame is useful which you kind of frame is useful for which you kind of frame is useful for most which you kind of frame is useful for most for that which you kind of frame is useful for most for that to identify which you kind of frame is useful for most for that to identify the objects which you kind of frame is useful for most for that to identify the objects and which you kind of frame is useful for most for that to identify the objects and we've given the which you kind of frame is useful for most for that to identify the objects and we've given the frame which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overl which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or an which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go most which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classification which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classifications which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classifications like for classification which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classifications like for classification have used Which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object, like clarity, like overlap or angle coverage, those kind of things where we can go mostly with that. After that, we gone with the object detection and classifications like for classification have used implement. like like mahkar like mahkar cn like mahkar cnn model like mahkar cnn model because particular like mahkar cnn model because particularly like mahkar cnn model because particularly it like mahkar cnn model because particularly it will give like mahkar cnn model because particularly it will give the boning like mahkar cnn model because particularly it will give the boning boxes like mahkar cnn model because particularly it will give the boning boxes and then like mahkar cnn model because particularly it will give the boning boxes and then pixel like mahkar cnn model because particularly it will give the boning boxes and then pixel wise ma like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detect like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use kind of like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use kind of pretrained like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use kind of pretrained modes like Like Mahkar CNN model because particularly it will give the boning boxes and then pixel wise mask for each detected object. So that's why we use that thing, and then we. Use kind of pretrained modes like. if shint if shint net those kind of things If shint net, those kind of things. to better to better capture to better capture the features to better capture the features and to better capture the features and specific to better capture the features and specific things to better capture the features and specific things after that To better capture the features and specific things after that. there there are different there are different cases there are different cases we face there are different cases we face like there are different cases we face like an image there are different cases we face like an image enhance there are different cases we face like an image enhancements there are different cases we face like an image enhancements do there are different cases we face like an image enhancements do find the there are different cases we face like an image enhancements do find the tow players there are different cases we face like an image enhancements do find the tow players of the data there are different cases we face like an image enhancements do find the tow players of the data to there are different cases we face like an image enhancements do find the tow players of the data to given that there are different cases we face like an image enhancements do find the tow players of the data to given that kind of there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the imag there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that debl there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the imag there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner fil there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion debl there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techn there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in open there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things after that there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things after that we there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things after that we have There are different cases we face like an image enhancements do find the tow players of the data to, given that kind of blurring the images. So we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques, which is available in OpenCV. So those things after that we have done. residual net residual networks also residual networks also we residual networks also we have to done to residual networks also we have to done to upscale residual networks also we have to done to upscale that residual networks also we have to done to upscale that pixel residual networks also we have to done to upscale that pixel images residual networks also we have to done to upscale that pixel images like residual networks also we have to done to upscale that pixel images like some pixel Residual networks. Also we have to done to upscale that pixel images like some pixel. we we have set we have set kind of we have set kind of image we have set kind of image should have the we have set kind of image should have the minimum pix we have set kind of image should have the minimum pixel size we have set kind of image should have the minimum pixel size so we have set kind of image should have the minimum pixel size so if we have set kind of image should have the minimum pixel size so if this we have set kind of image should have the minimum pixel size so if this increase We have set kind of image should have the minimum pixel size. So if this increase. low in low in pixel size low in pixel size it will low in pixel size it will automatically low in pixel size it will automatically incre low in pixel size it will automatically increase low in pixel size it will automatically increase the size low in pixel size it will automatically increase the size of the pix low in pixel size it will automatically increase the size of the pixels and low in pixel size it will automatically increase the size of the pixels and everything so low in pixel size it will automatically increase the size of the pixels and everything so that's low in pixel size it will automatically increase the size of the pixels and everything so that's what we low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the over low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non ma low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum separation low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum separation technique low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum separation technique so that Low in pixel size, it will automatically increase the size of the pixels and everything. So that's. What we have done after that, handling the overlapping images. So we have used like soft NMS. Like a non maximum separation technique, so that. these will be used these will be used so These will be used, so. based on that based on that we have based on that we have identified based on that we have identified our based on that we have identified our objects based on that we have identified our objects and based on that we have identified our objects and after that we have based on that we have identified our objects and after that we have done based on that we have identified our objects and after that we have done that based on that we have identified our objects and after that we have done that chemical based on that we have identified our objects and after that we have done that chemical detect based on that we have identified our objects and after that we have done that chemical detection based on that we have identified our objects and after that we have done that chemical detection is there based on that we have identified our objects and after that we have done that chemical detection is there or not based on that we have identified our objects and after that we have done that chemical detection is there or not that's also based on that we have identified our objects and after that we have done that chemical detection is there or not that's also what we have done Based on that, we have identified our objects, and after that we have done that. Chemical detection is there or not. That's also what we have done. of course of course there is of course there is a kind of of course there is a kind of sensors of course there is a kind of sensors will be there of course there is a kind of sensors will be there to ident of course there is a kind of sensors will be there to identify of course there is a kind of sensors will be there to identify that skin of course there is a kind of sensors will be there to identify that skin we have of course there is a kind of sensors will be there to identify that skin we have to of course there is a kind of sensors will be there to identify that skin we have to just Of course there is. A kind of sensors will be there to identify that skin. We have to. Just. identify identify the image Identify the image. with with the sensor with the sensor data with the sensor data so we have with the sensor data so we have used kind with the sensor data so we have used kind of with the sensor data so we have used kind of mq with the sensor data so we have used kind of mqll li with the sensor data so we have used kind of mqll libraries with the sensor data so we have used kind of mqll libraries we have with the sensor data so we have used kind of mqll libraries we have used With the sensor data. So we have used kind of MqlL libraries. We have used. whenever whenever that whenever that alarm whenever that alarm scheme so whenever that alarm scheme so we have to cap whenever that alarm scheme so we have to capture this imag whenever that alarm scheme so we have to capture this image and we have whenever that alarm scheme so we have to capture this image and we have to confirm whenever that alarm scheme so we have to capture this image and we have to confirm it if there is whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamina whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that we have to ident whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that we have to identify whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that we have to identify after that Whenever that alarm scheme. So we have to capture this image and we have to confirm it. If there is a chemical contamination, is there not like that. We have to identify after that. we have done we have done manual we have done manual labeling first we have done manual labeling first in the begin we have done manual labeling first in the beginning we have done manual labeling first in the beginning of the thing we have done manual labeling first in the beginning of the thing after that we have done manual labeling first in the beginning of the thing after that we have we have done manual labeling first in the beginning of the thing after that we have to eval we have done manual labeling first in the beginning of the thing after that we have to evaluate that we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling like by using we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling like by using the class we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling like by using the class valuation We have done manual labeling first in the beginning of the thing. After that, we have to evaluate that modeling like by using the class valuation. and And. appreciation like appreciation like those kind of appreciation like those kind of things we have appreciation like those kind of things we have done that appreciation like those kind of things we have done that based on appreciation like those kind of things we have done that based on the images appreciation like those kind of things we have done that based on the images like most appreciation like those kind of things we have done that based on the images like mostly we have done appreciation like those kind of things we have done that based on the images like mostly we have done with appreciation like those kind of things we have done that based on the images like mostly we have done with the batch appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred imag appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data prepr appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preproc appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning days appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning days we don't know appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning days we don't know the Appreciation, like those kind of things. We have done that based on the images, like, mostly we have. Done with the batch wise, like, we have taken like, 400 images at a time, and then after trying then and everything like that. Which happened. We have done that. Data preprocessing how? The images are coming and everything. So in the beginning days, we don't know the accuracy. also Also. that particular that particular point That particular point. if if it is if it is count we if it is count we need to count like if it is count we need to count like one hundred if it is count we need to count like one hundred images we if it is count we need to count like one hundred images we are getting like if it is count we need to count like one hundred images we are getting like seventy five if it is count we need to count like one hundred images we are getting like seventy five percent if it is count we need to count like one hundred images we are getting like seventy five percent of accur if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standard if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized imag if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values and then if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values and then we have if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values and then we have tested and fine things If it is count, we need to count like 100 images. We are getting like 75% of accuracy, but that 75%. Also, we are unable to identify it because of we don't. Have a kind of standardized images, so it should be had like that. So we keep on trying. Them. And then we made the best official values and then we have tested and fine tune those. things and things and we improve things and we improve that acc things and we improve that accuracy and things and we improve that accuracy and everything things and we improve that accuracy and everything those specific things and we improve that accuracy and everything those specific things things and we improve that accuracy and everything those specific things and Things and we improve that accuracy and everything, those specific things and. we have we have done mostly we have done mostly that thing that we have done mostly that thing that part we have done mostly that thing that part and then we have done mostly that thing that part and then for modal we have done mostly that thing that part and then for modal dep we have done mostly that thing that part and then for modal deployment part we have done mostly that thing that part and then for modal deployment part we have we have done mostly that thing that part and then for modal deployment part we have done we have done mostly that thing that part and then for modal deployment part we have done that We have done mostly that thing, that part. And then for modal deployment part, we have done that. data pipel data pipelines we have data pipelines we have done data pipelines we have done by data pipelines we have done by using the data data pipelines we have done by using the data bricks actually data pipelines we have done by using the data bricks actually so data pipelines we have done by using the data bricks actually so we write data pipelines we have done by using the data bricks actually so we write on autom data pipelines we have done by using the data bricks actually so we write on automation part data pipelines we have done by using the data bricks actually so we write on automation part in data pipelines we have done by using the data bricks actually so we write on automation part in the notes Data pipelines. We have done by using the data bricks, actually. So we write on automation part. In the notes. and and where the and where the images are coming and where the images are coming and and where the images are coming and everything and where the images are coming and everything we have and where the images are coming and everything we have to automate and where the images are coming and everything we have to automate it and and where the images are coming and everything we have to automate it and then it and where the images are coming and everything we have to automate it and then it automatically and where the images are coming and everything we have to automate it and then it automatically enhance and where the images are coming and everything we have to automate it and then it automatically enhance and enhance and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct part and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys is taken care and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys is taken care so most and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys is taken care so mostly And where the images are coming and everything, we have to automate it and then it automatically. Enhance and enhance images and everything and it will automatically delete CCT parts we have done. And the deployment part. ML ops guys is taken care. So mostly. okay Okay. what i'm trying what i'm trying to understand what i'm trying to understand is that what i'm trying to understand is that in this What I'm trying to understand is that in this. particular particular project particular project right particular project right you particular project right you have to understand Particular project, right? You have to understand. what kind of what kind of garbage what kind of garbage we collect what kind of garbage we collected what kind of garbage we collected is that
2024-09-04 18:08:57.053276: to to our delphi to our delphi system to our delphi system that accurately to our delphi system that accurately identify to our delphi system that accurately identify and separate to our delphi system that accurately identify and separate the different to our delphi system that accurately identify and separate the different plastic to our delphi system that accurately identify and separate the different plastic types to our delphi system that accurately identify and separate the different plastic types so to our delphi system that accurately identify and separate the different plastic types so before going to our delphi system that accurately identify and separate the different plastic types so before going to the wast to our delphi system that accurately identify and separate the different plastic types so before going to the waste manage to our delphi system that accurately identify and separate the different plastic types so before going to the waste management to our delphi system that accurately identify and separate the different plastic types so before going to the waste management i mean To our Delphi system that accurately identify and separate the different plastic types. So before going to the waste management, I mean. when they are mix when they are mixed together When they are mixed together. before going before going to the before going to the recycling before going to the recycling process before going to the recycling process in the same before going to the recycling process in the same pl before going to the recycling process in the same plastic before going to the recycling process in the same plastic there are before going to the recycling process in the same plastic there are eight type of before going to the recycling process in the same plastic there are eight type of plastics before going to the recycling process in the same plastic there are eight type of plastics will be there so before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the rec before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plast before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pv before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ld before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ldpc before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ldpc different before going to the recycling process in the same plastic there are eight type of plastics will be there so we need to identify based on the images actually so what you do is like it will decrease the operation cost and while doing the recycling so there are kind of eight kind of plastics related like pt http pvc ldpc different kind of Before going to the recycling process. In the same plastic, there are eight type of plastics will be there. So we need to identify based on the images, actually. So what you do is like it. Will decrease the operation cost and while doing the recycling. So there are kind of eight kind of. Plastics related like PT, HTTP, PVC, LDPC, different kind of. plastics plastics will be plastics will be there so plastics will be there so what we have plastics will be there so what we have to do is plastics will be there so what we have to do is we need plastics will be there so what we have to do is we need to plastics will be there so what we have to do is we need to sub plastics will be there so what we have to do is we need to subtract plastics will be there so what we have to do is we need to subtract them and plastics will be there so what we have to do is we need to subtract them and for plastics will be there so what we have to do is we need to subtract them and for the data colle plastics will be there so what we have to do is we need to subtract them and for the data collection part plastics will be there so what we have to do is we need to subtract them and for the data collection part we plastics will be there so what we have to do is we need to subtract them and for the data collection part we gone with Plastics will be there. So what we have to do is we need to subtract them. And for the data collection part, we gone with. image image we have to cap image we have to capture the imag image we have to capture the images Image. We have to capture the images. we will get kind of we will get kind of videos we will get kind of videos format we will get kind of videos format like twenty we will get kind of videos format like twenty seconds of we will get kind of videos format like twenty seconds of videos we will get kind of videos format like twenty seconds of videos we'll get we will get kind of videos format like twenty seconds of videos we'll get we need to we will get kind of videos format like twenty seconds of videos we'll get we need to identify we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of rand we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in open we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in openc we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so to identify we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so to identify the we will get kind of videos format like twenty seconds of videos we'll get we need to identify particular images actually instead of randomly we use kind of a deduction algorithm like such as background subtraction which is available in opencd so to identify the frames We will get kind of videos format, like 20 seconds of videos we'll get. We need. To identify particular images. Actually, instead of randomly, we use kind of a deduction algorithm, like such. As background subtraction, which is available in opencd. So to identify the frames. which which you kind of which you kind of frame which you kind of frame is useful which you kind of frame is useful for which you kind of frame is useful for most which you kind of frame is useful for most for that which you kind of frame is useful for most for that to identify which you kind of frame is useful for most for that to identify the objects which you kind of frame is useful for most for that to identify the objects and which you kind of frame is useful for most for that to identify the objects and we've given the which you kind of frame is useful for most for that to identify the objects and we've given the frame which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overl which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or an which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go most which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classification which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classifications which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classifications like for classification which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object like clarity like overlap or angle coverage those kind of things where we can go mostly with that after that we gone with the object detection and classifications like for classification have used Which you kind of frame is useful for most for that to identify the objects and we've given the frame scoring system we have given based on the object, like clarity, like overlap or angle coverage, those kind of things where we can go mostly with that. After that, we gone with the object detection and classifications like for classification have used implement. like like mahkar like mahkar cn like mahkar cnn model like mahkar cnn model because particular like mahkar cnn model because particularly like mahkar cnn model because particularly it like mahkar cnn model because particularly it will give like mahkar cnn model because particularly it will give the boning like mahkar cnn model because particularly it will give the boning boxes like mahkar cnn model because particularly it will give the boning boxes and then like mahkar cnn model because particularly it will give the boning boxes and then pixel like mahkar cnn model because particularly it will give the boning boxes and then pixel wise ma like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detect like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use kind of like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use kind of pretrained like mahkar cnn model because particularly it will give the boning boxes and then pixel wise mask for each detected object so that's why we use that thing and then we use kind of pretrained modes like Like Mahkar CNN model because particularly it will give the boning boxes and then pixel wise mask for each detected object. So that's why we use that thing, and then we. Use kind of pretrained modes like. if shint if shint net those kind of things If shint net, those kind of things. to better to better capture to better capture the features to better capture the features and to better capture the features and specific to better capture the features and specific things to better capture the features and specific things after that To better capture the features and specific things after that. there there are different there are different cases there are different cases we face there are different cases we face like there are different cases we face like an image there are different cases we face like an image enhance there are different cases we face like an image enhancements there are different cases we face like an image enhancements do there are different cases we face like an image enhancements do find the there are different cases we face like an image enhancements do find the tow players there are different cases we face like an image enhancements do find the tow players of the data there are different cases we face like an image enhancements do find the tow players of the data to there are different cases we face like an image enhancements do find the tow players of the data to given that there are different cases we face like an image enhancements do find the tow players of the data to given that kind of there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the imag there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that debl there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the imag there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner fil there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion debl there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techn there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in open there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things after that there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things after that we there are different cases we face like an image enhancements do find the tow players of the data to given that kind of blurring the images so we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques which is available in opencv so those things after that we have There are different cases we face like an image enhancements do find the tow players of the data to, given that kind of blurring the images. So we have to do that deblurring the images like by using a winner filtering and motion deblaring techniques, which is available in OpenCV. So those things after that we have done. residual net residual networks also residual networks also we residual networks also we have to done to residual networks also we have to done to upscale residual networks also we have to done to upscale that residual networks also we have to done to upscale that pixel residual networks also we have to done to upscale that pixel images residual networks also we have to done to upscale that pixel images like residual networks also we have to done to upscale that pixel images like some pixel Residual networks. Also we have to done to upscale that pixel images like some pixel. we we have set we have set kind of we have set kind of image we have set kind of image should have the we have set kind of image should have the minimum pix we have set kind of image should have the minimum pixel size we have set kind of image should have the minimum pixel size so we have set kind of image should have the minimum pixel size so if we have set kind of image should have the minimum pixel size so if this we have set kind of image should have the minimum pixel size so if this increase We have set kind of image should have the minimum pixel size. So if this increase. low in low in pixel size low in pixel size it will low in pixel size it will automatically low in pixel size it will automatically incre low in pixel size it will automatically increase low in pixel size it will automatically increase the size low in pixel size it will automatically increase the size of the pix low in pixel size it will automatically increase the size of the pixels and low in pixel size it will automatically increase the size of the pixels and everything so low in pixel size it will automatically increase the size of the pixels and everything so that's low in pixel size it will automatically increase the size of the pixels and everything so that's what we low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the over low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non ma low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum separation low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum separation technique low in pixel size it will automatically increase the size of the pixels and everything so that's what we have done after that handling the overlapping images so we have used like soft nms like a non maximum separation technique so that Low in pixel size, it will automatically increase the size of the pixels and everything. So that's. What we have done after that, handling the overlapping images. So we have used like soft NMS. Like a non maximum separation technique, so that. these will be used these will be used so These will be used, so. based on that based on that we have based on that we have identified based on that we have identified our based on that we have identified our objects based on that we have identified our objects and based on that we have identified our objects and after that we have based on that we have identified our objects and after that we have done based on that we have identified our objects and after that we have done that based on that we have identified our objects and after that we have done that chemical based on that we have identified our objects and after that we have done that chemical detect based on that we have identified our objects and after that we have done that chemical detection based on that we have identified our objects and after that we have done that chemical detection is there based on that we have identified our objects and after that we have done that chemical detection is there or not based on that we have identified our objects and after that we have done that chemical detection is there or not that's also based on that we have identified our objects and after that we have done that chemical detection is there or not that's also what we have done Based on that, we have identified our objects, and after that we have done that. Chemical detection is there or not. That's also what we have done. of course of course there is of course there is a kind of of course there is a kind of sensors of course there is a kind of sensors will be there of course there is a kind of sensors will be there to ident of course there is a kind of sensors will be there to identify of course there is a kind of sensors will be there to identify that skin of course there is a kind of sensors will be there to identify that skin we have of course there is a kind of sensors will be there to identify that skin we have to of course there is a kind of sensors will be there to identify that skin we have to just Of course there is. A kind of sensors will be there to identify that skin. We have to. Just. identify identify the image Identify the image. with with the sensor with the sensor data with the sensor data so we have with the sensor data so we have used kind with the sensor data so we have used kind of with the sensor data so we have used kind of mq with the sensor data so we have used kind of mqll li with the sensor data so we have used kind of mqll libraries with the sensor data so we have used kind of mqll libraries we have with the sensor data so we have used kind of mqll libraries we have used With the sensor data. So we have used kind of MqlL libraries. We have used. whenever whenever that whenever that alarm whenever that alarm scheme so whenever that alarm scheme so we have to cap whenever that alarm scheme so we have to capture this imag whenever that alarm scheme so we have to capture this image and we have whenever that alarm scheme so we have to capture this image and we have to confirm whenever that alarm scheme so we have to capture this image and we have to confirm it if there is whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamina whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that we have to ident whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that we have to identify whenever that alarm scheme so we have to capture this image and we have to confirm it if there is a chemical contamination is there not like that we have to identify after that Whenever that alarm scheme. So we have to capture this image and we have to confirm it. If there is a chemical contamination, is there not like that. We have to identify after that. we have done we have done manual we have done manual labeling first we have done manual labeling first in the begin we have done manual labeling first in the beginning we have done manual labeling first in the beginning of the thing we have done manual labeling first in the beginning of the thing after that we have done manual labeling first in the beginning of the thing after that we have we have done manual labeling first in the beginning of the thing after that we have to eval we have done manual labeling first in the beginning of the thing after that we have to evaluate that we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling like by using we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling like by using the class we have done manual labeling first in the beginning of the thing after that we have to evaluate that modeling like by using the class valuation We have done manual labeling first in the beginning of the thing. After that, we have to evaluate that modeling like by using the class valuation. and And. appreciation like appreciation like those kind of appreciation like those kind of things we have appreciation like those kind of things we have done that appreciation like those kind of things we have done that based on appreciation like those kind of things we have done that based on the images appreciation like those kind of things we have done that based on the images like most appreciation like those kind of things we have done that based on the images like mostly we have done appreciation like those kind of things we have done that based on the images like mostly we have done with appreciation like those kind of things we have done that based on the images like mostly we have done with the batch appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred imag appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data prepr appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preproc appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning days appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning days we don't know appreciation like those kind of things we have done that based on the images like mostly we have done with the batch wise like we have taken like four hundred images at a time and then after trying then and everything like that which happened we have done that data preprocessing how the images are coming and everything so in the beginning days we don't know the Appreciation, like those kind of things. We have done that based on the images, like, mostly we have. Done with the batch wise, like, we have taken like, 400 images at a time, and then after trying then and everything like that. Which happened. We have done that. Data preprocessing how? The images are coming and everything. So in the beginning days, we don't know the accuracy. also Also. that particular that particular point That particular point. if if it is if it is count we if it is count we need to count like if it is count we need to count like one hundred if it is count we need to count like one hundred images we if it is count we need to count like one hundred images we are getting like if it is count we need to count like one hundred images we are getting like seventy five if it is count we need to count like one hundred images we are getting like seventy five percent if it is count we need to count like one hundred images we are getting like seventy five percent of accur if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standard if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized imag if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values and then if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values and then we have if it is count we need to count like one hundred images we are getting like seventy five percent of accuracy but that seventy five percent also we are unable to identify it because of we don't have a kind of standardized images so it should be had like that so we keep on trying them and then we made the best official values and then we have tested and fine things If it is count, we need to count like 100 images. We are getting like 75% of accuracy, but that 75%. Also, we are unable to identify it because of we don't. Have a kind of standardized images, so it should be had like that. So we keep on trying. Them. And then we made the best official values and then we have tested and fine tune those. things and things and we improve things and we improve that acc things and we improve that accuracy and things and we improve that accuracy and everything things and we improve that accuracy and everything those specific things and we improve that accuracy and everything those specific things things and we improve that accuracy and everything those specific things and Things and we improve that accuracy and everything, those specific things and. we have we have done mostly we have done mostly that thing that we have done mostly that thing that part we have done mostly that thing that part and then we have done mostly that thing that part and then for modal we have done mostly that thing that part and then for modal dep we have done mostly that thing that part and then for modal deployment part we have done mostly that thing that part and then for modal deployment part we have we have done mostly that thing that part and then for modal deployment part we have done we have done mostly that thing that part and then for modal deployment part we have done that We have done mostly that thing, that part. And then for modal deployment part, we have done that. data pipel data pipelines we have data pipelines we have done data pipelines we have done by data pipelines we have done by using the data data pipelines we have done by using the data bricks actually data pipelines we have done by using the data bricks actually so data pipelines we have done by using the data bricks actually so we write data pipelines we have done by using the data bricks actually so we write on autom data pipelines we have done by using the data bricks actually so we write on automation part data pipelines we have done by using the data bricks actually so we write on automation part in data pipelines we have done by using the data bricks actually so we write on automation part in the notes Data pipelines. We have done by using the data bricks, actually. So we write on automation part. In the notes. and and where the and where the images are coming and where the images are coming and and where the images are coming and everything and where the images are coming and everything we have and where the images are coming and everything we have to automate and where the images are coming and everything we have to automate it and and where the images are coming and everything we have to automate it and then it and where the images are coming and everything we have to automate it and then it automatically and where the images are coming and everything we have to automate it and then it automatically enhance and where the images are coming and everything we have to automate it and then it automatically enhance and enhance and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct part and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys is taken care and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys is taken care so most and where the images are coming and everything we have to automate it and then it automatically enhance and enhance images and everything and it will automatically delete cct parts we have done and the deployment part ml ops guys is taken care so mostly And where the images are coming and everything, we have to automate it and then it automatically. Enhance and enhance images and everything and it will automatically delete CCT parts we have done. And the deployment part. ML ops guys is taken care. So mostly. okay Okay. what i'm trying what i'm trying to understand what i'm trying to understand is that what i'm trying to understand is that in this What I'm trying to understand is that in this. particular particular project particular project right particular project right you particular project right you have to understand Particular project, right? You have to understand. what kind of what kind of garbage what kind of garbage we collect what kind of garbage we collected what kind of garbage we collected is that
2024-09-04 18:10:21.226274: they already collected the garbage but the problem they already collected the garbage but the problem is they already collected the garbage but the problem is like They already collected the garbage. But the problem is, like. they already they already identified they already identified they they already identified they separated they already identified they separated the plastic they already identified they separated the plastic actually they already identified they separated the plastic actually so in they already identified they separated the plastic actually so in that plastic they already identified they separated the plastic actually so in that plastic they want they already identified they separated the plastic actually so in that plastic they want to they already identified they separated the plastic actually so in that plastic they want to there are some they already identified they separated the plastic actually so in that plastic they want to there are some plastics they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be rec they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plast they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chem they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separ they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it in that they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it in that plastic they they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it in that plastic they want to separate They already identified. They separated the plastic, actually. So in that plastic, they want to. There are some. Plastics which can't be recycling. There are some plastics which already had chemicals in it, so they want to separate it in that plastic. They want to separate. like Like. eight type eight types of classics eight types of classics will be eight types of classics will be there so eight types of classics will be there so they want eight types of classics will be there so they want to separate eight types of classics will be there so they want to separate each eight types of classics will be there so they want to separate each classic eight types of classics will be there so they want to separate each classic it seems eight types of classics will be there so they want to separate each classic it seems that's the main Eight types of classics will be there, so they want to separate each classic. It seems that's the Main. customer customer what would customer what would the customer customer what would the customer do with customer what would the customer do with this data customer what would the customer do with this data do you provide customer what would the customer do with this data do you provide this table customer what would the customer do with this data do you provide this table like customer what would the customer do with this data do you provide this table like the number customer what would the customer do with this data do you provide this table like the number of Customer. What would the customer do with this data? Do you provide this table like the number of. basically basically say the basically say the occurrences basically say the occurrences of basically say the occurrences of particular basically say the occurrences of particular type of pl basically say the occurrences of particular type of plastics basically say the occurrences of particular type of plastics and how basically say the occurrences of particular type of plastics and how will they utili basically say the occurrences of particular type of plastics and how will they utilize basically say the occurrences of particular type of plastics and how will they utilize this data Basically say, the occurrences of particular type of plastics. And how will they utilize this data? this data this data they are using this data they are using to separate this data they are using to separate the plastics this data they are using to separate the plastics actually This data they are using to separate the plastics, actually. like as like as i said like as i said type of pl like as i said type of plastics like as i said type of plastics will be there like as i said type of plastics will be there as a standard Like, as I said, type of plastics will be there as a standard. so yeah So, yeah. each side each side may be each side may be mech each side may be mechanical each side may be mechanical in the conve each side may be mechanical in the conveyor bel each side may be mechanical in the conveyor belt each side may be mechanical in the conveyor belt they are ident each side may be mechanical in the conveyor belt they are identifying once each side may be mechanical in the conveyor belt they are identifying once they identify each side may be mechanical in the conveyor belt they are identifying once they identify and then each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each plastic each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each plastic type each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each plastic type and then Each side may be mechanical in the conveyor belt they are identifying. Once they identify, and then they want to separate it, each plastic type, and then. which which had which had chemical which had chemical contamination which had chemical contamination and everything which had chemical contamination and everything they want to which had chemical contamination and everything they want to separate it which had chemical contamination and everything they want to separate it completely which had chemical contamination and everything they want to separate it completely actually which had chemical contamination and everything they want to separate it completely actually so like which had chemical contamination and everything they want to separate it completely actually so like anomaly Which had chemical contamination and everything. They want to separate it completely, actually. So, like, anomaly. so so is this so is this model being so is this model being fed so is this model being fed to so is this model being fed to some so is this model being fed to some other system So is this model being fed to some other system? like like say like say machine which like say machine which is like say machine which is doing this like say machine which is doing this using like say machine which is doing this using computer like say machine which is doing this using computer vers
2024-09-04 18:10:21.233485: they already collected the garbage but the problem they already collected the garbage but the problem is they already collected the garbage but the problem is like They already collected the garbage. But the problem is, like. they already they already identified they already identified they they already identified they separated they already identified they separated the plastic they already identified they separated the plastic actually they already identified they separated the plastic actually so in they already identified they separated the plastic actually so in that plastic they already identified they separated the plastic actually so in that plastic they want they already identified they separated the plastic actually so in that plastic they want to they already identified they separated the plastic actually so in that plastic they want to there are some they already identified they separated the plastic actually so in that plastic they want to there are some plastics they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be rec they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plast they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chem they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separ they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it in that they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it in that plastic they they already identified they separated the plastic actually so in that plastic they want to there are some plastics which can't be recycling there are some plastics which already had chemicals in it so they want to separate it in that plastic they want to separate They already identified. They separated the plastic, actually. So in that plastic, they want to. There are some. Plastics which can't be recycling. There are some plastics which already had chemicals in it, so they want to separate it in that plastic. They want to separate. like Like. eight type eight types of classics eight types of classics will be eight types of classics will be there so eight types of classics will be there so they want eight types of classics will be there so they want to separate eight types of classics will be there so they want to separate each eight types of classics will be there so they want to separate each classic eight types of classics will be there so they want to separate each classic it seems eight types of classics will be there so they want to separate each classic it seems that's the main Eight types of classics will be there, so they want to separate each classic. It seems that's the Main. customer customer what would customer what would the customer customer what would the customer do with customer what would the customer do with this data customer what would the customer do with this data do you provide customer what would the customer do with this data do you provide this table customer what would the customer do with this data do you provide this table like customer what would the customer do with this data do you provide this table like the number customer what would the customer do with this data do you provide this table like the number of Customer. What would the customer do with this data? Do you provide this table like the number of. basically basically say the basically say the occurrences basically say the occurrences of basically say the occurrences of particular basically say the occurrences of particular type of pl basically say the occurrences of particular type of plastics basically say the occurrences of particular type of plastics and how basically say the occurrences of particular type of plastics and how will they utili basically say the occurrences of particular type of plastics and how will they utilize basically say the occurrences of particular type of plastics and how will they utilize this data Basically say, the occurrences of particular type of plastics. And how will they utilize this data? this data this data they are using this data they are using to separate this data they are using to separate the plastics this data they are using to separate the plastics actually This data they are using to separate the plastics, actually. like as like as i said like as i said type of pl like as i said type of plastics like as i said type of plastics will be there like as i said type of plastics will be there as a standard Like, as I said, type of plastics will be there as a standard. so yeah So, yeah. each side each side may be each side may be mech each side may be mechanical each side may be mechanical in the conve each side may be mechanical in the conveyor bel each side may be mechanical in the conveyor belt each side may be mechanical in the conveyor belt they are ident each side may be mechanical in the conveyor belt they are identifying once each side may be mechanical in the conveyor belt they are identifying once they identify each side may be mechanical in the conveyor belt they are identifying once they identify and then each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each plastic each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each plastic type each side may be mechanical in the conveyor belt they are identifying once they identify and then they want to separate it each plastic type and then Each side may be mechanical in the conveyor belt they are identifying. Once they identify, and then they want to separate it, each plastic type, and then. which which had which had chemical which had chemical contamination which had chemical contamination and everything which had chemical contamination and everything they want to which had chemical contamination and everything they want to separate it which had chemical contamination and everything they want to separate it completely which had chemical contamination and everything they want to separate it completely actually which had chemical contamination and everything they want to separate it completely actually so like which had chemical contamination and everything they want to separate it completely actually so like anomaly Which had chemical contamination and everything. They want to separate it completely, actually. So, like, anomaly. so so is this so is this model being so is this model being fed so is this model being fed to so is this model being fed to some so is this model being fed to some other system So is this model being fed to some other system? like like say like say machine which like say machine which is like say machine which is doing this like say machine which is doing this using like say machine which is doing this using computer like say machine which is doing this using computer vers
2024-09-04 18:10:48.412671: all right All right. so so other than so other than this project So other than this project. have you have you worked on Have you worked on? say in say in the manu say in the manufacturing do say in the manufacturing domain say in the manufacturing domain when it comes say in the manufacturing domain when it comes to say in the manufacturing domain when it comes to demand say in the manufacturing domain when it comes to demand fore say in the manufacturing domain when it comes to demand forecasting say in the manufacturing domain when it comes to demand forecasting or say in the manufacturing domain when it comes to demand forecasting or basically time ser say in the manufacturing domain when it comes to demand forecasting or basically time series forecast say in the manufacturing domain when it comes to demand forecasting or basically time series forecasting Say, in the manufacturing domain when it comes to demand forecasting or basically time series forecasting. pre predicting Predicting. what what would be What would be. the the behavior The behavior. in In. a future a future deal a future deal have you a future deal have you done pred a future deal have you done predictive a future deal have you done predictive anal a future deal have you done predictive analysis a future deal have you done predictive analysis in the past
2024-09-04 18:10:48.417810: all right All right. so so other than so other than this project So other than this project. have you have you worked on Have you worked on? say in say in the manu say in the manufacturing do say in the manufacturing domain say in the manufacturing domain when it comes say in the manufacturing domain when it comes to say in the manufacturing domain when it comes to demand say in the manufacturing domain when it comes to demand fore say in the manufacturing domain when it comes to demand forecasting say in the manufacturing domain when it comes to demand forecasting or say in the manufacturing domain when it comes to demand forecasting or basically time ser say in the manufacturing domain when it comes to demand forecasting or basically time series forecast say in the manufacturing domain when it comes to demand forecasting or basically time series forecasting Say, in the manufacturing domain when it comes to demand forecasting or basically time series forecasting. pre predicting Predicting. what what would be What would be. the the behavior The behavior. in In. a future a future deal a future deal have you a future deal have you done pred a future deal have you done predictive a future deal have you done predictive anal a future deal have you done predictive analysis a future deal have you done predictive analysis in the past
2024-09-04 18:11:38.167796: which kind of which kind of product which kind of product should be which kind of product should be targeted actually Which kind of product should be targeted, actually. so so that's where so that's where mostly so that's where mostly i've used so that's where mostly i've used that so that's where mostly i've used that time series so that's where mostly i've used that time series path so that's where mostly i've used that time series path that's only so that's where mostly i've used that time series path that's only one time so that's where mostly i've used that time series path that's only one time i have used so that's where mostly i've used that time series path that's only one time i have used after that so that's where mostly i've used that time series path that's only one time i have used after that i so that's where mostly i've used that time series path that's only one time i have used after that i never so that's where mostly i've used that time series path that's only one time i have used after that i never used So that's where mostly I've used that time series path. That's only. One time I have used. After that I never used. basically basically majority basically majority of your work basically majority of your work has been basically majority of your work has been around Basically, majority of your work has been around. tensorf tensorflow tensorflow i'm assuming tensorflow i'm assuming basically tensorflow i'm assuming basically using tensorflow i'm assuming basically using deep tensorflow i'm assuming basically using deep learning tensorflow i'm assuming basically using deep learning yeah nlp tensorflow i'm assuming basically using deep learning yeah nlp works
2024-09-04 18:11:38.173337: which kind of which kind of product which kind of product should be which kind of product should be targeted actually Which kind of product should be targeted, actually. so so that's where so that's where mostly so that's where mostly i've used so that's where mostly i've used that so that's where mostly i've used that time series so that's where mostly i've used that time series path so that's where mostly i've used that time series path that's only so that's where mostly i've used that time series path that's only one time so that's where mostly i've used that time series path that's only one time i have used so that's where mostly i've used that time series path that's only one time i have used after that so that's where mostly i've used that time series path that's only one time i have used after that i so that's where mostly i've used that time series path that's only one time i have used after that i never so that's where mostly i've used that time series path that's only one time i have used after that i never used So that's where mostly I've used that time series path. That's only. One time I have used. After that I never used. basically basically majority basically majority of your work basically majority of your work has been basically majority of your work has been around Basically, majority of your work has been around. tensorf tensorflow tensorflow i'm assuming tensorflow i'm assuming basically tensorflow i'm assuming basically using tensorflow i'm assuming basically using deep tensorflow i'm assuming basically using deep learning tensorflow i'm assuming basically using deep learning yeah nlp tensorflow i'm assuming basically using deep learning yeah nlp works
2024-09-04 18:11:56.206476: and and what and what are the coding and what are the coding languages and what are the coding languages that and what are the coding languages that you're very comfortable and what are the coding languages that you're very comfortable with And what are the coding languages that you're very comfortable with? i'm i'm assuming i'm assuming it's five i'm assuming it's five star i'm assuming it's five star greater i'm assuming it's five star greater bricks I'm assuming it's five star greater bricks. can can tell me Can tell me. yeah
2024-09-04 18:11:56.212791: and and what and what are the coding and what are the coding languages and what are the coding languages that and what are the coding languages that you're very comfortable and what are the coding languages that you're very comfortable with And what are the coding languages that you're very comfortable with? i'm i'm assuming i'm assuming it's five i'm assuming it's five star i'm assuming it's five star greater i'm assuming it's five star greater bricks I'm assuming it's five star greater bricks. can can tell me Can tell me. yeah
2024-09-04 18:13:37.232288: yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrict yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images max yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video and again yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video and again we have yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video and again we have to Yeah. Scale. I know. Python. Python py, Spark, mostly. Even the current project. Which have they have? Stored the data in hadoop, actually. So they're given the videos like each video had. Like 20 seconds of video. There is kind of restrictions will be there. We have to take only ten to 16 images maximum from the video. And again, we have to process. those those things so those things so we have connect those things so we have connected with those things so we have connected with pi those things so we have connected with pi spark those things so we have connected with pi spark and then those things so we have connected with pi spark and then we have to collect those things so we have connected with pi spark and then we have to collect the those things so we have connected with pi spark and then we have to collect the data Those things. So we have connected with PI Spark, and then we have to collect the data. they they store they store the data they store the data the problem they store the data the problem is like they store the data the problem is like they don't they store the data the problem is like they don't have they store the data the problem is like they don't have they they store the data the problem is like they don't have they simply have they store the data the problem is like they don't have they simply have any single they store the data the problem is like they don't have they simply have any single storage They store the data. The problem is, like, they don't have, they simply have any single storage. they have They have. a single a single camera a single camera vision a single camera vision will be there right a single camera vision will be there right so A single camera, vision will be there. Right? So. they don't have they don't have any they don't have any kind of they don't have any kind of summary they don't have any kind of summary will be there they don't have any kind of summary will be there in a single they don't have any kind of summary will be there in a single time They don't have any kind of summary will be there in a single time. multiple lay multiple layers multiple layers will be Multiple layers will be. conveyor conveyor belts conveyor belts will be recorded conveyor belts will be recorded so conveyor belts will be recorded so we have to conveyor belts will be recorded so we have to identify conveyor belts will be recorded so we have to identify which thing conveyor belts will be recorded so we have to identify which thing and then Conveyor belts will be recorded. So we have to identify which thing, and then. we have to we have to pick the we have to pick the things We have to pick the things. and then and then that's and then that's where and then that's where mostly we have and then that's where mostly we have used and then that's where mostly we have used that and then that's where mostly we have used that pi spark and then that's where mostly we have used that pi spark and graph and then that's where mostly we have used that pi spark and graph to connect the data And then that's where mostly we have used that PI, spark and graph to connect the data. then then i'm not very then i'm not very expertized then i'm not very expertized in then i'm not very expertized in haroop then i'm not very expertized in haroop but then i'm not very expertized in haroop but i know how to then i'm not very expertized in haroop but i know how to connect the data then i'm not very expertized in haroop but i know how to connect the data and then i'm not very expertized in haroop but i know how to connect the data and then then i'm not very expertized in haroop but i know how to connect the data and then by spark tool then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and everything then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and everything and then then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and everything and then by using Then. I'm not very expertized in Haroop, but I know how to connect. The data, and then by spark tools and everything, and then by using. the the most the mostly we have the mostly we have to get the mostly we have to get the data the mostly we have to get the data to the mostly we have to get the data to our azure the mostly we have to get the data to our azure most the mostly we have to get the data to our azure mostly the mostly we have to get the data to our azure mostly our the mostly we have to get the data to our azure mostly our blob the mostly we have to get the data to our azure mostly our blob storage the mostly we have to get the data to our azure mostly our blob storage we have used the mostly we have to get the data to our azure mostly our blob storage we have used so two the mostly we have to get the data to our azure mostly our blob storage we have used so two store the data the mostly we have to get the data to our azure mostly our blob storage we have used so two store the data and then after the mostly we have to get the data to our azure mostly our blob storage we have used so two store the data and then after that The mostly we have to get the data to our azure, mostly our blob storage. We have used. So two store the data, and then after that. we have we have done we have done the we have done the processing and we have done the processing and preproc we have done the processing and preprocessing techn we have done the processing and preprocessing techniques and everything we have done the processing and preprocessing techniques and everything so we have done the processing and preprocessing techniques and everything so after that We have done the processing and preprocessing techniques and everything, so after that, the problem the problem is here The problem is here. there is kind of a bunch of there is kind of a bunch of data there is kind of a bunch of data will be there like there is kind of a bunch of data will be there like forty g there is kind of a bunch of data will be there like forty gb fifty there is kind of a bunch of data will be there like forty gb fifty gb there is kind of a bunch of data will be there like forty gb fifty gb we can't there is kind of a bunch of data will be there like forty gb fifty gb we can't train there is kind of a bunch of data will be there like forty gb fifty gb we can't train that There is kind of a bunch of data will be there, like 40 gb, 50 GB. We can't train that. so so again So again. we have to we have to segreg we have to segregate them We have to segregate them. that's a cost that's a costing thing i guess That's a costing thing, I guess. they don't want to use they don't want to use all the data they don't want to use all the data they want to they don't want to use all the data they want to okay they don't want to use all the data they want to okay yeah They don't want to use all the data. They want to. Okay, yeah. that's that's where that's where that That's where that.
2024-09-04 18:13:37.242554: yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrict yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images max yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video and again yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video and again we have yeah scale i know python python py spark mostly even the current project which have they have stored the data in hadoop actually so they're given the videos like each video had like twenty seconds of video there is kind of restrictions will be there we have to take only ten to sixteen images maximum from the video and again we have to Yeah. Scale. I know. Python. Python py, Spark, mostly. Even the current project. Which have they have? Stored the data in hadoop, actually. So they're given the videos like each video had. Like 20 seconds of video. There is kind of restrictions will be there. We have to take only ten to 16 images maximum from the video. And again, we have to process. those those things so those things so we have connect those things so we have connected with those things so we have connected with pi those things so we have connected with pi spark those things so we have connected with pi spark and then those things so we have connected with pi spark and then we have to collect those things so we have connected with pi spark and then we have to collect the those things so we have connected with pi spark and then we have to collect the data Those things. So we have connected with PI Spark, and then we have to collect the data. they they store they store the data they store the data the problem they store the data the problem is like they store the data the problem is like they don't they store the data the problem is like they don't have they store the data the problem is like they don't have they they store the data the problem is like they don't have they simply have they store the data the problem is like they don't have they simply have any single they store the data the problem is like they don't have they simply have any single storage They store the data. The problem is, like, they don't have, they simply have any single storage. they have They have. a single a single camera a single camera vision a single camera vision will be there right a single camera vision will be there right so A single camera, vision will be there. Right? So. they don't have they don't have any they don't have any kind of they don't have any kind of summary they don't have any kind of summary will be there they don't have any kind of summary will be there in a single they don't have any kind of summary will be there in a single time They don't have any kind of summary will be there in a single time. multiple lay multiple layers multiple layers will be Multiple layers will be. conveyor conveyor belts conveyor belts will be recorded conveyor belts will be recorded so conveyor belts will be recorded so we have to conveyor belts will be recorded so we have to identify conveyor belts will be recorded so we have to identify which thing conveyor belts will be recorded so we have to identify which thing and then Conveyor belts will be recorded. So we have to identify which thing, and then. we have to we have to pick the we have to pick the things We have to pick the things. and then and then that's and then that's where and then that's where mostly we have and then that's where mostly we have used and then that's where mostly we have used that and then that's where mostly we have used that pi spark and then that's where mostly we have used that pi spark and graph and then that's where mostly we have used that pi spark and graph to connect the data And then that's where mostly we have used that PI, spark and graph to connect the data. then then i'm not very then i'm not very expertized then i'm not very expertized in then i'm not very expertized in haroop then i'm not very expertized in haroop but then i'm not very expertized in haroop but i know how to then i'm not very expertized in haroop but i know how to connect the data then i'm not very expertized in haroop but i know how to connect the data and then i'm not very expertized in haroop but i know how to connect the data and then then i'm not very expertized in haroop but i know how to connect the data and then by spark tool then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and everything then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and everything and then then i'm not very expertized in haroop but i know how to connect the data and then by spark tools and everything and then by using Then. I'm not very expertized in Haroop, but I know how to connect. The data, and then by spark tools and everything, and then by using. the the most the mostly we have the mostly we have to get the mostly we have to get the data the mostly we have to get the data to the mostly we have to get the data to our azure the mostly we have to get the data to our azure most the mostly we have to get the data to our azure mostly the mostly we have to get the data to our azure mostly our the mostly we have to get the data to our azure mostly our blob the mostly we have to get the data to our azure mostly our blob storage the mostly we have to get the data to our azure mostly our blob storage we have used the mostly we have to get the data to our azure mostly our blob storage we have used so two the mostly we have to get the data to our azure mostly our blob storage we have used so two store the data the mostly we have to get the data to our azure mostly our blob storage we have used so two store the data and then after the mostly we have to get the data to our azure mostly our blob storage we have used so two store the data and then after that The mostly we have to get the data to our azure, mostly our blob storage. We have used. So two store the data, and then after that. we have we have done we have done the we have done the processing and we have done the processing and preproc we have done the processing and preprocessing techn we have done the processing and preprocessing techniques and everything we have done the processing and preprocessing techniques and everything so we have done the processing and preprocessing techniques and everything so after that We have done the processing and preprocessing techniques and everything, so after that, the problem the problem is here The problem is here. there is kind of a bunch of there is kind of a bunch of data there is kind of a bunch of data will be there like there is kind of a bunch of data will be there like forty g there is kind of a bunch of data will be there like forty gb fifty there is kind of a bunch of data will be there like forty gb fifty gb there is kind of a bunch of data will be there like forty gb fifty gb we can't there is kind of a bunch of data will be there like forty gb fifty gb we can't train there is kind of a bunch of data will be there like forty gb fifty gb we can't train that There is kind of a bunch of data will be there, like 40 gb, 50 GB. We can't train that. so so again So again. we have to we have to segreg we have to segregate them We have to segregate them. that's a cost that's a costing thing i guess That's a costing thing, I guess. they don't want to use they don't want to use all the data they don't want to use all the data they want to they don't want to use all the data they want to okay they don't want to use all the data they want to okay yeah They don't want to use all the data. They want to. Okay, yeah. that's that's where that's where that That's where that.
2024-09-04 18:13:48.092446: how do you select how do you select the input how do you select the input data to how do you select the input data to run your model how do you select the input data to run your model then how do you select the input data to run your model then do you how do you select the input data to run your model then do you go how do you select the input data to run your model then do you go through the data how do you select the input data to run your model then do you go through the data manually how do you select the input data to run your model then do you go through the data manually and then how do you select the input data to run your model then do you go through the data manually and then you how do you select the input data to run your model then do you go through the data manually and then you select how do you select the input data to run your model then do you go through the data manually and then you select what how do you select the input data to run your model then do you go through the data manually and then you select what to cut how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste no how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste no that's why how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste no that's why we implement
2024-09-04 18:13:48.097653: how do you select how do you select the input how do you select the input data to how do you select the input data to run your model how do you select the input data to run your model then how do you select the input data to run your model then do you how do you select the input data to run your model then do you go how do you select the input data to run your model then do you go through the data how do you select the input data to run your model then do you go through the data manually how do you select the input data to run your model then do you go through the data manually and then how do you select the input data to run your model then do you go through the data manually and then you how do you select the input data to run your model then do you go through the data manually and then you select how do you select the input data to run your model then do you go through the data manually and then you select what how do you select the input data to run your model then do you go through the data manually and then you select what to cut how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste no how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste no that's why how do you select the input data to run your model then do you go through the data manually and then you select what to cut what to paste no that's why we implement
2024-09-04 18:16:20.003147: pitch the based on actually they have pitch the based on actually they have some kind pitch the based on actually they have some kind of pitch the based on actually they have some kind of certain kind of pitch the based on actually they have some kind of certain kind of format pitch the based on actually they have some kind of certain kind of format will be there pitch the based on actually they have some kind of certain kind of format will be there for each pitch the based on actually they have some kind of certain kind of format will be there for each video pitch the based on actually they have some kind of certain kind of format will be there for each video so we have pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once it is pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once it is available the pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once it is available the video will Pitch the based on actually they have some kind of certain kind of format will be there for each video. So we have written python code whenever that date is changed, like every 1 hour, it will go and then it will fetch the data. Whenever the video is available, it will keep on. Check, and then once it is available, the video will autom. automatically automatically pull up automatically pull up and automatically pull up and you take automatically pull up and you take the necessary automatically pull up and you take the necessary images only automatically pull up and you take the necessary images only like automatically pull up and you take the necessary images only like as i said pre automatically pull up and you take the necessary images only like as i said previously we have automatically pull up and you take the necessary images only like as i said previously we have used Automatically pull up and you take the necessary images only. Like as I said previously, we have used. open opencv Opencv. different kind of techn different kind of techniques different kind of techniques we have used different kind of techniques we have used like different kind of techniques we have used like to different kind of techniques we have used like to frame fr different kind of techniques we have used like to frame framing fr different kind of techniques we have used like to frame framing frame error al different kind of techniques we have used like to frame framing frame error algorithms different kind of techniques we have used like to frame framing frame error algorithms or those things different kind of techniques we have used like to frame framing frame error algorithms or those things so it will different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also sometimes different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also sometimes we'll of different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also sometimes we'll of course Different kind of techniques we have used, like to frame framing, frame error algorithms, or those things, so it will automatically take the thing and then automatically it will delete. Also, sometimes we'll of course. in the beginning in the beginning stage actually in the beginning stage actually we de in the beginning stage actually we delete it in the beginning stage actually we delete it in the later in the beginning stage actually we delete it in the later stages in the beginning stage actually we delete it in the later stages some like two in the beginning stage actually we delete it in the later stages some like two to three days in the beginning stage actually we delete it in the later stages some like two to three days of in the beginning stage actually we delete it in the later stages some like two to three days of data in the beginning stage actually we delete it in the later stages some like two to three days of data will store in the beginning stage actually we delete it in the later stages some like two to three days of data will store just in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our refer in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we delet in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we deleted in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we deleted to In the beginning stage. Actually, we delete it in the later stages, some, like two to three. Days of data will store just for our references. After that, once the images is taken from the video automatically. We deleted to. the entire thing the entire thing is done the entire thing is done the automation the entire thing is done the automation part only the entire thing is done the automation part only so the entire thing is done the automation part only so we can't the entire thing is done the automation part only so we can't do the entire thing is done the automation part only so we can't do it man the entire thing is done the automation part only so we can't do it manually the entire thing is done the automation part only so we can't do it manually only the the entire thing is done the automation part only so we can't do it manually only the manual thing the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling part the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling part so the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling part so we don't know The entire thing is done, the automation part only. So we can't do it manually only. The manual thing is, what we have done is the labeling part, so we don't know. which which imag which image is which image is correct which image is correct which is not which image is correct which is not correct because which image is correct which is not correct because there is no which image is correct which is not correct because there is no one to which image is correct which is not correct because there is no one to guide actually which image is correct which is not correct because there is no one to guide actually so which image is correct which is not correct because there is no one to guide actually so there is a which image is correct which is not correct because there is no one to guide actually so there is a guy's which image is correct which is not correct because there is no one to guide actually so there is a guy's ran which image is correct which is not correct because there is no one to guide actually so there is a guy's ran is there Which image is correct, which is not correct, because there is no one to guide, actually, so there is a guy's ran is there? who who uses who uses station Who uses station. yes so yes so these guys only connect yes so these guys only connect with yes so these guys only connect with once yes so these guys only connect with once a week yes so these guys only connect with once a week so he will yes so these guys only connect with once a week so he will tell like yes so these guys only connect with once a week so he will tell like most of yes so these guys only connect with once a week so he will tell like most of the people yes so these guys only connect with once a week so he will tell like most of the people in our project yes so these guys only connect with once a week so he will tell like most of the people in our project will be like yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then we have to yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then we have to take the in yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then we have to take the inputs and Yes. So these guys only connect with once a week, so he will tell, like, most of the people in our project will be like, such a son of people are there. So mostly we have to connect with him, and then we have to take the inputs and. we have to We have to. demo demo the demo the code and demo the code and everything demo the code and everything what i've done demo the code and everything what i've done in this demo the code and everything what i've done in this week and then demo the code and everything what i've done in this week and then if demo the code and everything what i've done in this week and then if any changes demo the code and everything what i've done in this week and then if any changes will demo the code and everything what i've done in this week and then if any changes will tell and then demo the code and everything what i've done in this week and then if any changes will tell and then if demo the code and everything what i've done in this week and then if any changes will tell and then if any demo the code and everything what i've done in this week and then if any changes will tell and then if any data requ demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the w demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so including demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so including the pcs demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so including the pcs and everything Demo, the code and everything, what I've done in this week, and then if any changes. Will tell and then if any data required. Also he only passes actually the most of the things. Is, the Wim people didn't give them the access of most of the things, so. We have only a few access. So he is only the guy who got so, including the pcs. And everything, so. we have to we have to done We have to done. understood Understood. yeah i think yeah i think i have yeah i think i have what yeah i think i have what i need to yeah i think i have what i need to provide yeah i think i have what i need to provide my feedback yeah i think i have what i need to provide my feedback in h yeah i think i have what i need to provide my feedback in hr yeah i think i have what i need to provide my feedback in hr i will yeah i think i have what i need to provide my feedback in hr i will give them my yeah i think i have what i need to provide my feedback in hr i will give them my feedback and yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely in yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely in a be a client yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely in a be a client okay Yeah. I think I have what I need to provide my feedback in HR. I will give them. My feedback and they'll get back to you with next steps if you're through. Most likely in a. Be a client, okay? so so they would probably so they would probably drill down so they would probably drill down into so they would probably drill down into what so they would probably drill down into what you've implemented so they would probably drill down into what you've implemented in your project so they would probably drill down into what you've implemented in your project at a de so they would probably drill down into what you've implemented in your project at a deeper level so they would probably drill down into what you've implemented in your project at a deeper level in terms of so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those top so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then we can so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then we can take it to that so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then we can take it to that yeah
2024-09-04 18:16:20.008691: pitch the based on actually they have pitch the based on actually they have some kind pitch the based on actually they have some kind of pitch the based on actually they have some kind of certain kind of pitch the based on actually they have some kind of certain kind of format pitch the based on actually they have some kind of certain kind of format will be there pitch the based on actually they have some kind of certain kind of format will be there for each pitch the based on actually they have some kind of certain kind of format will be there for each video pitch the based on actually they have some kind of certain kind of format will be there for each video so we have pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once it is pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once it is available the pitch the based on actually they have some kind of certain kind of format will be there for each video so we have written python code whenever that date is changed like every one hour it will go and then it will fetch the data whenever the video is available it will keep on check and then once it is available the video will Pitch the based on actually they have some kind of certain kind of format will be there for each video. So we have written python code whenever that date is changed, like every 1 hour, it will go and then it will fetch the data. Whenever the video is available, it will keep on. Check, and then once it is available, the video will autom. automatically automatically pull up automatically pull up and automatically pull up and you take automatically pull up and you take the necessary automatically pull up and you take the necessary images only automatically pull up and you take the necessary images only like automatically pull up and you take the necessary images only like as i said pre automatically pull up and you take the necessary images only like as i said previously we have automatically pull up and you take the necessary images only like as i said previously we have used Automatically pull up and you take the necessary images only. Like as I said previously, we have used. open opencv Opencv. different kind of techn different kind of techniques different kind of techniques we have used different kind of techniques we have used like different kind of techniques we have used like to different kind of techniques we have used like to frame fr different kind of techniques we have used like to frame framing fr different kind of techniques we have used like to frame framing frame error al different kind of techniques we have used like to frame framing frame error algorithms different kind of techniques we have used like to frame framing frame error algorithms or those things different kind of techniques we have used like to frame framing frame error algorithms or those things so it will different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also sometimes different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also sometimes we'll of different kind of techniques we have used like to frame framing frame error algorithms or those things so it will automatically take the thing and then automatically it will delete also sometimes we'll of course Different kind of techniques we have used, like to frame framing, frame error algorithms, or those things, so it will automatically take the thing and then automatically it will delete. Also, sometimes we'll of course. in the beginning in the beginning stage actually in the beginning stage actually we de in the beginning stage actually we delete it in the beginning stage actually we delete it in the later in the beginning stage actually we delete it in the later stages in the beginning stage actually we delete it in the later stages some like two in the beginning stage actually we delete it in the later stages some like two to three days in the beginning stage actually we delete it in the later stages some like two to three days of in the beginning stage actually we delete it in the later stages some like two to three days of data in the beginning stage actually we delete it in the later stages some like two to three days of data will store in the beginning stage actually we delete it in the later stages some like two to three days of data will store just in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our refer in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we delet in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we deleted in the beginning stage actually we delete it in the later stages some like two to three days of data will store just for our references after that once the images is taken from the video automatically we deleted to In the beginning stage. Actually, we delete it in the later stages, some, like two to three. Days of data will store just for our references. After that, once the images is taken from the video automatically. We deleted to. the entire thing the entire thing is done the entire thing is done the automation the entire thing is done the automation part only the entire thing is done the automation part only so the entire thing is done the automation part only so we can't the entire thing is done the automation part only so we can't do the entire thing is done the automation part only so we can't do it man the entire thing is done the automation part only so we can't do it manually the entire thing is done the automation part only so we can't do it manually only the the entire thing is done the automation part only so we can't do it manually only the manual thing the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling part the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling part so the entire thing is done the automation part only so we can't do it manually only the manual thing is what we have done is the labeling part so we don't know The entire thing is done, the automation part only. So we can't do it manually only. The manual thing is, what we have done is the labeling part, so we don't know. which which imag which image is which image is correct which image is correct which is not which image is correct which is not correct because which image is correct which is not correct because there is no which image is correct which is not correct because there is no one to which image is correct which is not correct because there is no one to guide actually which image is correct which is not correct because there is no one to guide actually so which image is correct which is not correct because there is no one to guide actually so there is a which image is correct which is not correct because there is no one to guide actually so there is a guy's which image is correct which is not correct because there is no one to guide actually so there is a guy's ran which image is correct which is not correct because there is no one to guide actually so there is a guy's ran is there Which image is correct, which is not correct, because there is no one to guide, actually, so there is a guy's ran is there? who who uses who uses station Who uses station. yes so yes so these guys only connect yes so these guys only connect with yes so these guys only connect with once yes so these guys only connect with once a week yes so these guys only connect with once a week so he will yes so these guys only connect with once a week so he will tell like yes so these guys only connect with once a week so he will tell like most of yes so these guys only connect with once a week so he will tell like most of the people yes so these guys only connect with once a week so he will tell like most of the people in our project yes so these guys only connect with once a week so he will tell like most of the people in our project will be like yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then we have to yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then we have to take the in yes so these guys only connect with once a week so he will tell like most of the people in our project will be like such a son of people are there so mostly we have to connect with him and then we have to take the inputs and Yes. So these guys only connect with once a week, so he will tell, like, most of the people in our project will be like, such a son of people are there. So mostly we have to connect with him, and then we have to take the inputs and. we have to We have to. demo demo the demo the code and demo the code and everything demo the code and everything what i've done demo the code and everything what i've done in this demo the code and everything what i've done in this week and then demo the code and everything what i've done in this week and then if demo the code and everything what i've done in this week and then if any changes demo the code and everything what i've done in this week and then if any changes will demo the code and everything what i've done in this week and then if any changes will tell and then demo the code and everything what i've done in this week and then if any changes will tell and then if demo the code and everything what i've done in this week and then if any changes will tell and then if any demo the code and everything what i've done in this week and then if any changes will tell and then if any data requ demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the w demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so including demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so including the pcs demo the code and everything what i've done in this week and then if any changes will tell and then if any data required also he only passes actually the most of the things is the wim people didn't give them the access of most of the things so we have only a few access so he is only the guy who got so including the pcs and everything Demo, the code and everything, what I've done in this week, and then if any changes. Will tell and then if any data required. Also he only passes actually the most of the things. Is, the Wim people didn't give them the access of most of the things, so. We have only a few access. So he is only the guy who got so, including the pcs. And everything, so. we have to we have to done We have to done. understood Understood. yeah i think yeah i think i have yeah i think i have what yeah i think i have what i need to yeah i think i have what i need to provide yeah i think i have what i need to provide my feedback yeah i think i have what i need to provide my feedback in h yeah i think i have what i need to provide my feedback in hr yeah i think i have what i need to provide my feedback in hr i will yeah i think i have what i need to provide my feedback in hr i will give them my yeah i think i have what i need to provide my feedback in hr i will give them my feedback and yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely in yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely in a be a client yeah i think i have what i need to provide my feedback in hr i will give them my feedback and they'll get back to you with next steps if you're through most likely in a be a client okay Yeah. I think I have what I need to provide my feedback in HR. I will give them. My feedback and they'll get back to you with next steps if you're through. Most likely in a. Be a client, okay? so so they would probably so they would probably drill down so they would probably drill down into so they would probably drill down into what so they would probably drill down into what you've implemented so they would probably drill down into what you've implemented in your project so they would probably drill down into what you've implemented in your project at a de so they would probably drill down into what you've implemented in your project at a deeper level so they would probably drill down into what you've implemented in your project at a deeper level in terms of so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those top so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then we can so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then we can take it to that so they would probably drill down into what you've implemented in your project at a deeper level in terms of all library cues how do you go about optimizing your model and so on right not just a current project maybe a previous project as well okay so i suggest you dress up on those topics and then we can take it to that yeah
