<!DOCTYPE html>
<html>
<head>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <style>
    body, html {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        overflow: hidden;
    }

    .main-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    /* Answer Display Area - 60% */
    .answer-display-area {
        height: 60vh;
        display: flex;
        flex-direction: row;
        background: linear-gradient(145deg, #f8f9fa, #e9ecef);
        border-bottom: 2px solid #dee2e6;
    }

    /* Left Controls - 15% */
    .left-controls {
        width: 15%;
        background: linear-gradient(180deg, #28a745, #20c997);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 15px;
        padding: 20px 10px;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }

    /* Answer Text Area - 70% (Centered) */
    .answer-text-area {
        width: 70%;
        padding: 30px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        background-color: #ffffff;
        position: relative;
        overflow: hidden;
    }

    .answer-content {
        font-size: 20px;
        line-height: 1.6;
        color: #2c3e50;
        font-weight: 400;
        letter-spacing: 0.5px;
        overflow-y: auto;
        max-height: 100%;
        padding: 0 20px;
        scroll-behavior: smooth;
        text-align: left;
        margin: 0 auto;
        max-width: 100%;
    }

    .answer-content::-webkit-scrollbar {
        width: 8px;
    }

    .answer-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .answer-content::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }

    .answer-content::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* Right Controls - 15% */
    .right-controls {
        width: 15%;
        background: linear-gradient(180deg, #007bff, #6610f2);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 15px;
        padding: 20px 10px;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    }

    /* Question Display Area - 40% (Expanded) */
    .question-display-area {
        height: 40vh;
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        border-top: 2px solid #dee2e6;
        display: flex;
        flex-direction: column;
        padding: 20px;
        overflow-y: auto;
    }

    /* Setup Row */
    .setup-row {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .setup-left, .setup-right {
        flex: 1;
    }

    .setup-btn {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .setup-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 10px rgba(0,123,255,0.3);
    }

    .current-question {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        font-size: 16px;
        color: #1565c0;
        min-height: 50px;
        display: flex;
        align-items: center;
    }

    .status-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .status-indicator {
        font-weight: bold;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 14px;
    }

    .status-listening {
        background-color: #d4edda;
        color: #155724;
    }

    .status-processing {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-ready {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .manual-input-container {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    #manualInput {
        flex-grow: 1;
        padding: 12px;
        font-size: 16px;
        border: 2px solid #dee2e6;
        border-radius: 25px;
        outline: none;
        transition: border-color 0.3s;
    }

    #manualInput:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
    }

    /* Control Buttons */
    .control-btn {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 15px;
        width: 70px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        font-size: 12px;
        font-weight: bold;
        color: #333;
        margin: 5px 0;
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        background: rgba(255, 255, 255, 1);
    }

    .control-btn:active {
        transform: translateY(0);
    }

    .send-btn {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .send-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,123,255,0.4);
    }

    .file-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 12px 15px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin-right: 10px;
    }

    .file-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40,167,69,0.4);
    }

    .file-preview {
        margin-top: 10px;
        padding: 8px 12px;
        background-color: #e9ecef;
        border-radius: 5px;
        font-size: 14px;
        color: #495057;
    }

    /* Speed Control */
    .speed-control {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: white;
        font-size: 12px;
        margin: 10px 0;
    }

    .speed-slider {
        writing-mode: bt-lr; /* IE */
        writing-mode: vertical-lr; /* Standard */
        width: 100px;
        height: 20px;
        transform: rotate(270deg);
        margin: 20px 0;
    }



    /* Animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.5s ease-out;
    }

    /* Scroll to bottom button */
    .scroll-to-bottom-btn {
        position: absolute;
        bottom: 20px;
        right: 20px;
        background: rgba(0, 123, 255, 0.9);
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .scroll-to-bottom-btn:hover {
        background: rgba(0, 123, 255, 1);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    /* Responsive adjustments */
    @media (max-width: 1200px) {
        .answer-content {
            font-size: 18px;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }
    }
    </style>
</head>
<body>
    <div class="main-container">


        <!-- Answer Display Area - 60% -->
        <div class="answer-display-area">
            <!-- Left Controls - 15% -->
            <div class="left-controls">
                <button class="control-btn" onclick="startListening()" title="Start Listening (Left Arrow)">
                    MIC
                </button>
                <button class="control-btn" onclick="stopListeningAndSend()" title="Stop and Send (Down Arrow)">
                    STOP
                </button>
                <button class="control-btn" onclick="stopAIResponse()" title="Stop AI Response (Right Arrow)">
                    END
                </button>
                <div id="listeningIndicator" class="status-indicator status-listening" style="display: none;">
                    LIVE
                </div>
            </div>

            <!-- Answer Text Area - 70% (Centered) -->
            <div class="answer-text-area">
                <div id="answerContent" class="answer-content">
                    <div style="text-align: center; color: #6c757d; margin-top: 50px; font-size: 18px;">
                        Ready to receive your questions...
                    </div>
                </div>
                <!-- Scroll to bottom button (appears when user scrolls up) -->
                <button id="scrollToBottomBtn" class="scroll-to-bottom-btn" onclick="forceScrollToBottom()" style="display: none;">
                    ↓ Scroll to Latest
                </button>
            </div>

            <!-- Right Controls - 15% -->
            <div class="right-controls">
                <div class="speed-control">
                    <span>Speed</span>
                    <input type="range" class="speed-slider" id="speedControl" min="1" max="10" value="5" title="Text Display Speed">
                    <span id="speedValue">5</span>
                </div>
                <div class="scroll-status" id="scrollStatus" style="color: white; font-size: 10px; text-align: center; margin: 10px 0;">
                    Auto-scroll: ON
                </div>
                <button class="control-btn" onclick="clearChat()" title="Clear Chat">
                    CLR
                </button>
                <button class="control-btn" onclick="saveSession()" title="Save Session">
                    SAVE
                </button>
                <button class="control-btn" onclick="toggleSettings()" title="Settings">
                    SET
                </button>
            </div>
        </div>

        <!-- Question Display Area - 40% (Expanded) -->
        <div class="question-display-area">
            <!-- Setup Row - Project Details and Intro Message -->
            <div class="setup-row">
                <div class="setup-left">
                    <label style="font-weight: bold; color: #495057; margin-bottom: 5px; display: block;">Project Details:</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="projectDetails" placeholder="Enter project details" style="flex: 1; padding: 8px; border: 1px solid #dee2e6; border-radius: 5px; font-size: 14px;">
                        <button type="button" class="setup-btn" onclick="setProjectDetails()">Set</button>
                    </div>
                </div>
                <div class="setup-right">
                    <label style="font-weight: bold; color: #495057; margin-bottom: 5px; display: block;">Intro Message:</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="introMessage" placeholder="Enter intro message" style="flex: 1; padding: 8px; border: 1px solid #dee2e6; border-radius: 5px; font-size: 14px;">
                        <button type="button" class="setup-btn" onclick="setIntroMessage()">Set</button>
                    </div>
                </div>
            </div>

            <!-- Current Question Display -->
            <div class="current-question" id="currentQuestion">
                No question yet - start by asking something...
            </div>

            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-indicator status-ready" id="statusIndicator">
                    Ready
                </div>
                <div style="font-size: 14px; color: #6c757d;">
                    Use Left Arrow (MIC), Down Arrow (STOP), Right Arrow (END) for quick controls
                </div>
            </div>

            <!-- Manual Input Container -->
            <div class="manual-input-container">
                <input type="text" id="manualInput" placeholder="Type your question here...">
                <input type="file" id="fileInput" accept="image/*,.pdf,.txt,.doc,.docx" style="display: none;">
                <button class="file-btn" onclick="document.getElementById('fileInput').click()" title="Upload File/Image">
                    📎
                </button>
                <button class="send-btn" onclick="sendManualInput()">Send</button>
            </div>
            <div id="filePreview" class="file-preview" style="display: none;">
                <span id="fileName"></span>
                <button onclick="clearFile()" style="margin-left: 10px; background: #dc3545; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer;">✕</button>
            </div>
        </div>
    </div>
    <script>
    var isListening = false;
    var eventSource;
    var currentUserMessage = null;
    var currentAIMessage = null;
    var latestUserMessage = '';
    var baseUrl = window.location.origin;
    var introMessage = '';
    var introMessageSet = false;

    // New variables for smooth text display
    var textBuffer = '';
    var displaySpeed = 5; // Words per second
    var isDisplaying = false;
    var displayQueue = [];
    var currentDisplayIndex = 0;
    var animationFrameId = null;

    // Initialize speed control and scroll detection
    document.addEventListener('DOMContentLoaded', function() {
        const speedControl = document.getElementById('speedControl');
        const speedValue = document.getElementById('speedValue');
        const answerContent = document.getElementById('answerContent');

        speedControl.addEventListener('input', function() {
            displaySpeed = parseInt(this.value);
            speedValue.textContent = this.value;
        });

        // Add scroll event listener to detect manual scrolling
        answerContent.addEventListener('scroll', checkUserScroll);
    });

    document.body.onkeyup = function(e) {
        if (e.keyCode == 37) { // Left arrow key
            startListening();
        } else if (e.keyCode == 40) { // Down arrow key
            stopListeningAndSend();
        } else if (e.keyCode == 39) { // Right arrow key
            stopAIResponse();
        }
    };

    function setIntroMessage() {
        introMessage = document.getElementById('introMessage').value;
        if (introMessage.trim() !== '') {
            introMessageSet = true;
            document.getElementById('introMessage').disabled = true;
            displayIntroMessage();
        }
    }

    function displayIntroMessage() {
        if (introMessageSet && introMessage.trim() !== '') {
            const answerContent = document.getElementById('answerContent');
            answerContent.innerHTML = `
                <div style="text-align: center; color: #007bff; font-size: 22px; margin: 50px 0; padding: 20px; background: linear-gradient(135deg, #e3f2fd, #bbdefb); border-radius: 15px; border-left: 5px solid #2196f3;">
                    ${introMessage}
                </div>
            `;
        }
    }

    // Simple text formatting function
    function displayTextSmoothly(text) {
        const answerContent = document.getElementById('answerContent');
        answerContent.innerHTML = formatText(text);
        scrollToBottom();
    }

    function formatText(text) {
        // Format text with better styling
        return text
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // Manual scroll control - no auto-scroll
    var userScrolledUp = false;
    var lastScrollPosition = 0;

    function checkUserScroll() {
        const answerContent = document.getElementById('answerContent');
        const scrollBtn = document.getElementById('scrollToBottomBtn');
        const scrollStatus = document.getElementById('scrollStatus');
        const currentScroll = answerContent.scrollTop;
        const maxScroll = answerContent.scrollHeight - answerContent.clientHeight;

        // Check if user manually scrolled up
        if (currentScroll < lastScrollPosition || currentScroll < maxScroll - 50) {
            userScrolledUp = true;
            scrollBtn.style.display = 'block'; // Show scroll button
            scrollStatus.textContent = 'Auto-scroll: OFF';
            scrollStatus.style.color = '#ffeb3b'; // Yellow when off
        } else if (currentScroll >= maxScroll - 10) {
            userScrolledUp = false; // User scrolled back to bottom
            scrollBtn.style.display = 'none'; // Hide scroll button
            scrollStatus.textContent = 'Auto-scroll: ON';
            scrollStatus.style.color = '#4caf50'; // Green when on
        }

        lastScrollPosition = currentScroll;
    }

    function forceScrollToBottom() {
        const answerContent = document.getElementById('answerContent');
        const scrollBtn = document.getElementById('scrollToBottomBtn');
        const scrollStatus = document.getElementById('scrollStatus');
        answerContent.scrollTop = answerContent.scrollHeight;
        userScrolledUp = false;
        scrollBtn.style.display = 'none';
        scrollStatus.textContent = 'Auto-scroll: ON';
        scrollStatus.style.color = '#4caf50'; // Green when on
    }

    function scrollToBottom() {
        // Only auto-scroll if user hasn't manually scrolled up
        if (!userScrolledUp) {
            const answerContent = document.getElementById('answerContent');
            answerContent.scrollTop = answerContent.scrollHeight;
        }
    }

    function setProjectDetails() {
        var projectDetails = document.getElementById('projectDetails').value;
        fetch(baseUrl + '/set_project_details', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ details: projectDetails })
        })
        .then(response => response.json())
        .then(data => console.log(data))
        .catch(error => console.error('Error:', error));
    }

    function startListening() {
        // Don't start if already listening
        if (isListening) {
            console.log('Already listening, ignoring request');
            return;
        }

        // Clean up any existing connections first
        cleanupConnections();

        isListening = true;
        updateStatus('listening', 'Listening...');
        document.getElementById('listeningIndicator').style.display = 'block';
        console.log('Voice recognition started. Speak now.');

        fetch(baseUrl + '/start_transcription', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            console.log('Start transcription response:', data);
            startEventSource();
        })
        .catch(error => {
            console.error('Error starting transcription:', error);
            isListening = false;
            document.getElementById('listeningIndicator').style.display = 'none';
            updateStatus('ready', 'Error - Ready');
        });
    }

    function updateStatus(type, message) {
        const statusIndicator = document.getElementById('statusIndicator');
        statusIndicator.className = `status-indicator status-${type}`;
        statusIndicator.textContent = message;
        console.log('Status updated:', type, message);
    }

    function stopListeningAndSend() {
        if (isListening) {
            isListening = false;
            document.getElementById('listeningIndicator').style.display = 'none';
            console.log('Voice recognition stopped.');
            fetch(baseUrl + '/stop_transcription', { method: 'POST' })
            .then(response => response.json())
            .then(data => console.log(data))
            .catch(error => console.error('Error:', error));
        }
        
        // Get manual input
        var manualInput = document.getElementById('manualInput');
        var inputText = manualInput.value.trim();
        
        // Set manual input if there's any text
        if (inputText !== '') {
            // Display the question immediately
            document.getElementById('currentQuestion').textContent = inputText;

            fetch(baseUrl + '/set_manual_input', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ input: inputText })
            })
            .then(response => response.json())
            .then(data => {
                console.log(data);
                manualInput.value = ''; // Clear the input field
                sendToAI(); // Send to AI for processing
            })
            .catch(error => console.error('Error:', error));
        } else {
            // If no manual input, just send to AI (which will use transcribed input if any)
            sendToAI();
        }
    }

    function stopAIResponse() {
        updateStatus('ready', 'Stopping AI...');
        // Clear display queue and stop animations
        displayQueue = [];
        isDisplaying = false;
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
        }

        fetch(baseUrl + '/stop_ai_response', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            console.log(data);
            updateStatus('ready', 'Ready - Starting listening...');
            // Automatically start listening again after stopping AI
            startListening();
        })
        .catch(error => console.error('Error:', error));
    }

    function sendToAI() {
        fetch(baseUrl + '/send_to_ai', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            console.log(data);
        })
        .catch(error => console.error('Error:', error));
    }

    function cleanupConnections() {
        // Close any existing EventSource connections
        if (eventSource) {
            console.log('Cleaning up existing EventSource');
            eventSource.close();
            eventSource = null;
        }

        // Clear any pending animations
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }

        // Clear display queue
        displayQueue = [];
        isDisplaying = false;

        // Reset scroll state
        userScrolledUp = false;
        lastScrollPosition = 0;
    }

    function startEventSource() {
        // Always close existing connection first
        if (eventSource) {
            console.log('Closing existing EventSource connection');
            eventSource.close();
            eventSource = null;
        }

        // Start EventSource immediately for speed
        console.log('Starting new EventSource connection');
        eventSource = new EventSource(baseUrl + '/get_response');

        eventSource.onmessage = function(event) {
                try {
                    var data = JSON.parse(event.data);
                    console.log('Received data:', data);

                    // Display intro message if it hasn't been displayed yet
                    if (!introMessageSet) {
                        displayIntroMessage();
                    }

                    if (data.role === 'user') {
                        // Display user question in the question area
                        document.getElementById('currentQuestion').textContent = data.content;
                        latestUserMessage = data.content;
                        updateStatus('processing', 'Processing your question...');
                    } else if (data.role === 'ai') {
                        if (data.content === "END_OF_RESPONSE") {
                            updateStatus('ready', 'Ready for next question');
                            currentAIMessage = null;
                            // Close EventSource after response is complete
                            if (eventSource) {
                                eventSource.close();
                                eventSource = null;
                            }
                        } else {
                            // Display AI responses chunk by chunk in the answer area
                            const answerContent = document.getElementById('answerContent');
                            if (answerContent) {
                                answerContent.innerHTML = formatText(data.content);
                                scrollToBottom();
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error parsing EventSource data:', error);
                }
            };

            eventSource.onerror = function(event) {
                console.error('EventSource error:', event);
                updateStatus('ready', 'Connection error - Ready');
                // Close and cleanup on error
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }
            };

        eventSource.onopen = function(event) {
            console.log('EventSource connection opened');
        };
    }

    // Additional control functions
    function sendManualInput() {
        var manualInput = document.getElementById('manualInput');
        var inputText = manualInput.value.trim();

        if (inputText !== '') {
            // Display the question immediately
            document.getElementById('currentQuestion').textContent = inputText;
            updateStatus('processing', 'Processing your question...');

            fetch(baseUrl + '/set_manual_input', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ input: inputText })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Manual input response:', data);
                manualInput.value = ''; // Clear the input field
                sendToAI(); // Send to AI for processing
            })
            .catch(error => {
                console.error('Error sending manual input:', error);
                updateStatus('ready', 'Error - Ready');
            });
        }
    }

    // Add Enter key support for manual input and file upload handling
    document.addEventListener('DOMContentLoaded', function() {
        const manualInput = document.getElementById('manualInput');
        const fileInput = document.getElementById('fileInput');

        if (manualInput) {
            manualInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendManualInput();
                }
            });
        }

        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                handleFileSelect(e);
            });
        }
    });

    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            const filePreview = document.getElementById('filePreview');
            const fileName = document.getElementById('fileName');

            fileName.textContent = `📎 ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
            filePreview.style.display = 'block';
        }
    }

    function clearFile() {
        const fileInput = document.getElementById('fileInput');
        const filePreview = document.getElementById('filePreview');

        fileInput.value = '';
        filePreview.style.display = 'none';
    }

    function clearChat() {
        const answerContent = document.getElementById('answerContent');
        answerContent.innerHTML = `
            <div style="text-align: center; color: #6c757d; margin-top: 50px; font-size: 18px;">
                Chat cleared - Ready for new questions...
            </div>
        `;
        document.getElementById('currentQuestion').textContent = 'No question yet - start by asking something...';
        updateStatus('ready', 'Ready');
    }

    function saveSession() {
        const answerContent = document.getElementById('answerContent').innerHTML;
        const currentQuestion = document.getElementById('currentQuestion').textContent;

        const sessionData = {
            timestamp: new Date().toISOString(),
            question: currentQuestion,
            answer: answerContent
        };

        // Create and download file
        const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `session_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        updateStatus('ready', 'Session saved!');
        setTimeout(() => updateStatus('ready', 'Ready'), 2000);
    }

    function toggleSettings() {
        // Toggle visibility of setup row
        const setupRow = document.querySelector('.setup-row');

        if (setupRow.style.display === 'none') {
            setupRow.style.display = 'flex';
        } else {
            setupRow.style.display = 'none';
        }
    }

    // Periodic cleanup to prevent memory leaks
    function periodicCleanup() {
        // Clean up if no activity for a while
        if (!isListening && !isDisplaying) {
            cleanupConnections();
        }
    }

    // Call displayIntroMessage when the page loads
    window.onload = function() {
        displayIntroMessage();
        updateStatus('ready', 'Ready');

        // Set up periodic cleanup every 5 minutes
        setInterval(periodicCleanup, 300000);
    };

    // Clean up when page is about to unload
    window.addEventListener('beforeunload', function() {
        cleanupConnections();
    });
    </script>
</body>
</html>