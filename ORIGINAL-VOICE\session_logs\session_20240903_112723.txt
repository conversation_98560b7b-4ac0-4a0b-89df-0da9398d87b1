2024-09-03 11:28:27.569687: what have you what have you done what have you done in your what have you done in your first what have you done in your first project what have you done in your first project and What have you done in your first project and. how How? gans gans are gans are used Gans are used. in your in your projects
2024-09-03 11:28:27.575203: what have you what have you done what have you done in your what have you done in your first what have you done in your first project what have you done in your first project and What have you done in your first project and. how How? gans gans are gans are used Gans are used. in your in your projects
2024-09-03 11:43:15.552834: home Home. yes Yes. currently currently i'm currently i'm looking Currently I'm looking. yes yes that's fine Yes, that's fine. i have to i have to click i have to click zero one i have to click zero one years and i have to click zero one years and the relevant experience i have to click zero one years and the relevant experience is i have to click zero one years and the relevant experience is like four i have to click zero one years and the relevant experience is like four point i have to click zero one years and the relevant experience is like four point three i have to click zero one years and the relevant experience is like four point three years i have to click zero one years and the relevant experience is like four point three years with regard i have to click zero one years and the relevant experience is like four point three years with regarding i have to click zero one years and the relevant experience is like four point three years with regarding data sci i have to click zero one years and the relevant experience is like four point three years with regarding data science i have to click zero one years and the relevant experience is like four point three years with regarding data science yeah I have to click zero one years and the relevant experience is like 4.3 years with regarding data science. Yeah. seventeen 17. four point four point three 4.3. yes yes we take yes we take communications Yes, we take communications. like like already like already serving not like already serving notice Like, already serving notice. yeah yeah can i call yeah can i call you in two minutes yeah can i call you in two minutes please Yeah. Can I call you in two minutes, please? okay okay fine okay fine sure Okay, fine. Sure. thank you Thank you. hello Hello. sha Sha. shai Shai. yeah sure yeah sure so yeah sure so we're going to start yeah sure so we're going to start the interview yeah sure so we're going to start the interview so yeah sure so we're going to start the interview so please yeah sure so we're going to start the interview so please start yeah sure so we're going to start the interview so please start your yeah sure so we're going to start the interview so please start your introduction yeah sure so we're going to start the interview so please start your introduction okay yeah sure so we're going to start the interview so please start your introduction okay fine yeah sure so we're going to start the interview so please start your introduction okay fine so yeah sure so we're going to start the interview so please start your introduction okay fine so myself Yeah, sure. So we're going to start the interview, so please start your introduction. Okay, fine. So myself. and and i'm having and i'm having totally and i'm having totally point one and i'm having totally point one years experience and i'm having totally point one years experience in and i'm having totally point one years experience in it and i'm having totally point one years experience in it the and i'm having totally point one years experience in it the learned experience and i'm having totally point one years experience in it the learned experience with and i'm having totally point one years experience in it the learned experience with data science and i'm having totally point one years experience in it the learned experience with data science and am and i'm having totally point one years experience in it the learned experience with data science and aml is and i'm having totally point one years experience in it the learned experience with data science and aml is around and i'm having totally point one years experience in it the learned experience with data science and aml is around four and i'm having totally point one years experience in it the learned experience with data science and aml is around four years and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like nump and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pand and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi nlt and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi nlt k ten and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi nlt k tensorflow And I'm having totally point one years experience in it, the learned experience with data science. And AML is around four years, two months and mostly about with python and I work with libraries, tools like numpy pandas, Sci-Fi nlt k, tensorflow. space spacey and spacey and cycling those spacey and cycling those kind of things spacey and cycling those kind of things i worked spacey and cycling those kind of things i worked on spacey and cycling those kind of things i worked on for spacey and cycling those kind of things i worked on for visualization spacey and cycling those kind of things i worked on for visualization part i spacey and cycling those kind of things i worked on for visualization part i worked with spacey and cycling those kind of things i worked on for visualization part i worked with map plot spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and table spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability the spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory and recreation spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory and recreation analysis Spacey and cycling, those kind of things I worked on for visualization part I worked with. Map plotlib and CBO and Power Bi and tableau at Tabia works for only for one project. I'm a regular expert, as in, and I'm experienced with the statical analysis, including hyper assisting and probability theory and recreation analysis and. risk risk assessment risk assessment those things risk assessment those things have worked risk assessment those things have worked on risk assessment those things have worked on and in risk assessment those things have worked on and in machine learning risk assessment those things have worked on and in machine learning i have risk assessment those things have worked on and in machine learning i have worked on risk assessment those things have worked on and in machine learning i have worked on projects risk assessment those things have worked on and in machine learning i have worked on projects like risk assessment those things have worked on and in machine learning i have worked on projects like regression risk assessment those things have worked on and in machine learning i have worked on projects like regression class risk assessment those things have worked on and in machine learning i have worked on projects like regression classification risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommend risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation system risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience with risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience with nlp risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience with nlp as well Risk assessment. Those things have worked on. And in machine learning, I have worked on projects like regression classification, clustering and the recommendation systems and setting of as luck executes those things. I have worked on and also have hands on experience with NLP as well. like fr like frameworks like frameworks like like frameworks like what like frameworks like what keras like frameworks like what keras and like frameworks like what keras and tenso like frameworks like what keras and tensorflo like frameworks like what keras and tensorflow like frameworks like what keras and tensorflow bright arch like frameworks like what keras and tensorflow bright arch open like frameworks like what keras and tensorflow bright arch open cv like frameworks like what keras and tensorflow bright arch open cv those like frameworks like what keras and tensorflow bright arch open cv those things i have most like frameworks like what keras and tensorflow bright arch open cv those things i have mostly about like frameworks like what keras and tensorflow bright arch open cv those things i have mostly about one like frameworks like what keras and tensorflow bright arch open cv those things i have mostly about one and Like frameworks. Like what? Keras and tensorflow, bright arch, open cv, those things I have. Mostly about one and. with with the perform with the perform that with the perform that i with the perform that i worked with With the perform that I worked with. apha apha development apha development as well apha development as well the part also apha development as well the part also i have apha development as well the part also i have worked on apha development as well the part also i have worked on with the databases apha development as well the part also i have worked on with the databases i apha development as well the part also i have worked on with the databases i worked with apha development as well the part also i have worked on with the databases i worked with mysql apha development as well the part also i have worked on with the databases i worked with mysql sql Apha development as well. The part also I have worked on with the databases I worked with MySQL, sql. and and most most of the and most most of the things i and most most of the things i worked on and most most of the things i worked on was those things only and most most of the things i worked on was those things only and and most most of the things i worked on was those things only and with my and most most of the things i worked on was those things only and with my skill i and most most of the things i worked on was those things only and with my skill i work with and most most of the things i worked on was those things only and with my skill i work with only And most, most of the things I worked on was those things only. And with my skill, I work with only. for the For the. storing Storing. sorry sorry i transform sorry i transform the data sorry i transform the data while doing sorry i transform the data while doing the to anal sorry i transform the data while doing the to analysis sorry i transform the data while doing the to analysis part so sorry i transform the data while doing the to analysis part so apart from sorry i transform the data while doing the to analysis part so apart from that Sorry. I transform the data while doing the to analysis part. So apart from that. in my previous project In my previous project. most mostly mostly we develop mostly we developed a system mostly we developed a system that could Mostly, we developed a system that could. do the do the accurately do the accurately identify do the accurately identify and separate do the accurately identify and separate the different do the accurately identify and separate the different types do the accurately identify and separate the different types of do the accurately identify and separate the different types of plastic do the accurately identify and separate the different types of plastic types based do the accurately identify and separate the different types of plastic types based on the do the accurately identify and separate the different types of plastic types based on the images do the accurately identify and separate the different types of plastic types based on the images actually do the accurately identify and separate the different types of plastic types based on the images actually so do the accurately identify and separate the different types of plastic types based on the images actually so that classification do the accurately identify and separate the different types of plastic types based on the images actually so that classification project do the accurately identify and separate the different types of plastic types based on the images actually so that classification project we have do the accurately identify and separate the different types of plastic types based on the images actually so that classification project we have done do the accurately identify and separate the different types of plastic types based on the images actually so that classification project we have done with Do the accurately identify and separate the different types of plastic types based on the images. Actually, so. That classification project we have done with. the main the main aim the main aim is the main aim is to the main aim is to identify the main aim is to identify the chemical the main aim is to identify the chemical contam the main aim is to identify the chemical contamination the main aim is to identify the chemical contamination and then the main aim is to identify the chemical contamination and then ident the main aim is to identify the chemical contamination and then identify the the main aim is to identify the chemical contamination and then identify the plastic the main aim is to identify the chemical contamination and then identify the plastic objects the main aim is to identify the chemical contamination and then identify the plastic objects and the main aim is to identify the chemical contamination and then identify the plastic objects and those type the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so before going to the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so before going to the rec the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so before going to the recycling The main aim is to identify the chemical contamination and then identify the plastic objects and those types. Actually. So before going to the recycling. landfill Landfill. the wast the waste management the waste management team is the waste management team is asked to the waste management team is asked to recycle the waste management team is asked to recycle identify The waste management team is asked to recycle, identify. the the plastic type The plastic type. and and it had a and it had a chemical cont and it had a chemical contamination and it had a chemical contamination or not and it had a chemical contamination or not so those kind and it had a chemical contamination or not so those kind of things and it had a chemical contamination or not so those kind of things we need to ident and it had a chemical contamination or not so those kind of things we need to identify so and it had a chemical contamination or not so those kind of things we need to identify so there are totally and it had a chemical contamination or not so those kind of things we need to identify so there are totally types and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different plastics and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different plastics we have and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different plastics we have worked on And it had a chemical contamination or not, so those kind of things we need to identify. So there are totally types of plastics will be there. So we'll get like Pt, HTTP PVC. LDP, those kind of different plastics we have worked on. my my name is my name is to my name is to reduce my name is to reduce the operational my name is to reduce the operational cost and my name is to reduce the operational cost and before going my name is to reduce the operational cost and before going to the My name is to reduce the operational cost and before going to the. resetting resetting area resetting area recycling resetting area recycling plan resetting area recycling plant so resetting area recycling plant so that's Resetting area recycling plant. So that's. the main the main thing The main thing. we get we get the data we get the data mostly we get the data mostly from the we get the data mostly from the videos we get the data mostly from the videos like we we get the data mostly from the videos like we will get kind of we get the data mostly from the videos like we will get kind of twenty we get the data mostly from the videos like we will get kind of twenty signals of we get the data mostly from the videos like we will get kind of twenty signals of videos and we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the imag we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of random we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly pick the we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly pick the images we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly pick the images we use We get the data mostly from the videos. Like, we will get kind of 20 signals of videos. And we need to extract the images from there. So instead of randomly pick the images, we use. the kind of the kind of motion direct the kind of motion direction algor the kind of motion direction algorithms The kind of motion direction algorithms. where where we have to where we have to subtract Where we have to subtract. the background the background and everything the background and everything and which the background and everything and which is available the background and everything and which is available in open the background and everything and which is available in opencv the background and everything and which is available in opencv to the background and everything and which is available in opencv to identify the background and everything and which is available in opencv to identify the frames the background and everything and which is available in opencv to identify the frames and everything the background and everything and which is available in opencv to identify the frames and everything so the background and everything and which is available in opencv to identify the frames and everything so what the background and everything and which is available in opencv to identify the frames and everything so what we have done is the background and everything and which is available in opencv to identify the frames and everything so what we have done is like the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the frame the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the framework the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the framework scoring the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the framework scoring so where The background and everything, and which is available in OpenCV to identify the frames and everything. So what we have done is, like, we have given the framework scoring. So where. it had it had higher points it had higher points like it had higher points like to implement it had higher points like to implement fact it had higher points like to implement factors such it had higher points like to implement factors such as It had higher points like to implement factors such as. object objective objective class objective classify and objective classify and the overlap Objective, classify and the overlap. and and angle and angle coverage and angle coverage those kind of and angle coverage those kind of things And angle coverage, those kind of things. where it can where it can have where it can have good where it can have good image where it can have good image so where it can have good image so it will where it can have good image so it will have a higher where it can have good image so it will have a higher score where it can have good image so it will have a higher score and where it can have good image so it will have a higher score and it will where it can have good image so it will have a higher score and it will pick as that where it can have good image so it will have a higher score and it will pick as that image so where it can have good image so it will have a higher score and it will pick as that image so from that where it can have good image so it will have a higher score and it will pick as that image so from that image Where it can have good image so it will have a higher score and it will pick as that image. So from that image. there is there is a limitation there is a limitation we have there is a limitation we have to there is a limitation we have to collect there is a limitation we have to collect ten there is a limitation we have to collect ten images There is a limitation. We have to collect ten images. for the video for the video and then after for the video and then after that for the video and then after that we have for the video and then after that we have to do the for the video and then after that we have to do the classification for the video and then after that we have to do the classification part and for the video and then after that we have to do the classification part and everything for the video and then after that we have to do the classification part and everything so for the video and then after that we have to do the classification part and everything so that's for the video and then after that we have to do the classification part and everything so that's the thing and for the video and then after that we have to do the classification part and everything so that's the thing and we have done for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection classification for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection classification we have for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection classification we have used For the video, and then after that, we have to do the classification part and everything so that's. The thing. And we have done many things in the pipeline, like object detection, classification. We have used. like like mascar like mascarn and like mascarn and c like mascarn and cnn Like mascarn and cnn. those kind of those kind of models those kind of models we have used Those kind of models we have used. to to the bonding to the bonding boxes to the bonding boxes and everything to the bonding boxes and everything after that to the bonding boxes and everything after that we have done to the bonding boxes and everything after that we have done image to the bonding boxes and everything after that we have done image enhance to the bonding boxes and everything after that we have done image enhancements to the bonding boxes and everything after that we have done image enhancements and to to the bonding boxes and everything after that we have done image enhancements and to find to the bonding boxes and everything after that we have done image enhancements and to find out layers to the bonding boxes and everything after that we have done image enhancements and to find out layers actually to the bonding boxes and everything after that we have done image enhancements and to find out layers actually to To the bonding boxes and everything after that, we have done image enhancements and to find out layers, actually, to. add add blurry add blurry images add blurry images and add blurry images and everything add blurry images and everything we have to enhance add blurry images and everything we have to enhance the pixel add blurry images and everything we have to enhance the pixel size add blurry images and everything we have to enhance the pixel sizes add blurry images and everything we have to enhance the pixel sizes and everything add blurry images and everything we have to enhance the pixel sizes and everything so add blurry images and everything we have to enhance the pixel sizes and everything so those things add blurry images and everything we have to enhance the pixel sizes and everything so those things also add blurry images and everything we have to enhance the pixel sizes and everything so those things also have add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and for anom add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and for anomaly add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and for anomaly identification Add blurry images and everything. We have to enhance the pixel sizes and everything. So those things also have done after that for the chemical contamination and for anomaly identification. we have we have used we have used kind we have used kind of We have used kind of. when when a chemical when a chemical waste is going on When a chemical waste is going on. we we have used we have used kind of senso we have used kind of sensor actually we have used kind of sensor actually so that we have used kind of sensor actually so that sensor we have used kind of sensor actually so that sensor data We have used kind of sensor, actually, so that sensor data. is a combina is a combination of Is a combination of. senso sensor data Sensor data. with With. image Image. imag image identification image identification so image identification so when we image identification so when we both confir image identification so when we both confirms image identification so when we both confirms so image identification so when we both confirms so we identify image identification so when we both confirms so we identify the sahar image identification so when we both confirms so we identify the sahara image identification so when we both confirms so we identify the sahara chemical cont image identification so when we both confirms so we identify the sahara chemical contamina image identification so when we both confirms so we identify the sahara chemical contamination so image identification so when we both confirms so we identify the sahara chemical contamination so for that image identification so when we both confirms so we identify the sahara chemical contamination so for that we have image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mg image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries who have used image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries who have used after image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries who have used after that Image identification. So when we both confirms, so we identify the Sahara chemical contamination. So for that we have used a different kind of like MGT and those kind of libraries who have used after that. we we identify we identify the we identify the images we identify the images and then we identify the images and then we have class we identify the images and then we have classified it we identify the images and then we have classified it and then we have we identify the images and then we have classified it and then we have done we identify the images and then we have classified it and then we have done that we identify the images and then we have classified it and then we have done that deplo we identify the images and then we have classified it and then we have done that deployment part we identify the images and then we have classified it and then we have done that deployment part and everything we identify the images and then we have classified it and then we have done that deployment part and everything so we identify the images and then we have classified it and then we have done that deployment part and everything so that's we identify the images and then we have classified it and then we have done that deployment part and everything so that's our main thing We identify the images, and then we have classified it, and then we have done that deployment part. And everything, so that's our main thing. yeah yeah can you Yeah. Can you? so so just so just show me your technical so just show me your technical round so so just show me your technical round so i'm going to so just show me your technical round so i'm going to ask so just show me your technical round so i'm going to ask some python so just show me your technical round so i'm going to ask some python coding so just show me your technical round so i'm going to ask some python coding questions so just show me your technical round so i'm going to ask some python coding questions so so just show me your technical round so i'm going to ask some python coding questions so you have to so just show me your technical round so i'm going to ask some python coding questions so you have to compute so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in given time so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in given time of so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in given time of play okay So just show me your technical round. So I'm going to ask some python coding questions. So you have to compute that in given time of play, okay? so so you can so you can share your so you can share your screen so you can share your screen you can so you can share your screen you can use it any so you can share your screen you can use it any online ed so you can share your screen you can use it any online editor so you can share your screen you can use it any online editor or so you can share your screen you can use it any online editor or any so you can share your screen you can use it any online editor or any tip to so you can share your screen you can use it any online editor or any tip to anything so you can share your screen you can use it any online editor or any tip to anything i'm just going so you can share your screen you can use it any online editor or any tip to anything i'm just going to share so you can share your screen you can use it any online editor or any tip to anything i'm just going to share one so you can share your screen you can use it any online editor or any tip to anything i'm just going to share one coding so you can share your screen you can use it any online editor or any tip to anything i'm just going to share one coding question So you can share your screen. You can use it any online editor or any tip to anything. I'm just going to share one coding question. share your screen share your screen also share your screen also yeah share your screen also yeah sure Share your screen also? Yeah, sure. please please can you use please can you use another please can you use another editor please can you use another editor like please can you use another editor like are you trying please can you use another editor like are you trying to use please can you use another editor like are you trying to use collab please can you use another editor like are you trying to use collab yes Please. Can you use another editor? Like. Are you trying to use collab? Yes. because because it gives because it gives suggestions because it gives suggestions so because it gives suggestions so can you use because it gives suggestions so can you use another Because it gives suggestions. So can you use another? you can search you can search on google you can search on google python You can search on Google Python. or or you have jupiter Or you have Jupiter. anything Anything. your local your local supply Your local supply. i'm using i'm using collab i'm using collab only i'm using collab only like i'm using collab only like any other I'm using collab only like any other. vs vs code is vs code is okay vs code is okay for you vs code is okay for you but vs code is okay for you but vs code Vs. Code is okay for you, but vs. Code. but but don't have any but don't have any jupiter but don't have any jupiter in that but don't have any jupiter in that actually But don't have any Jupiter in that, actually. you can run you can run that particular code you can run that particular code anywhere You can run that particular code anywhere. just a just a second Just a second. maybe Maybe. try to Try to. i've i've shared i've shared one i've shared one link this i've shared one link this is online i've shared one link this is online you can i've shared one link this is online you can use that i've shared one link this is online you can use that one i've shared one link this is online you can use that one okay that's i've shared one link this is online you can use that one okay that's fine I've shared one link. This is online. You can use that one. Okay, that's. Fine. at all At all. where it is Where it is. okay Okay. i i've shared one i've shared one question also i've shared one question also python question i've shared one question also python question you can i've shared one question also python question you can copy and I've shared one question also Python question. You can copy and. copy copy here Copy here. no good no good fine No. Good. Fine. is there any is there any issue Is there any issue? like Like. it's not it's not copying It's not copying. anything anything because anything because of Anything because of. the The. something something wrong Something wrong. just just give me just give me a minute just give me a minute okay Just give me a minute, okay? then Then. look at look at that look at that list i can look at that list i can explain look at that list i can explain that look at that list i can explain that logic look at that list i can explain that logic i think there are look at that list i can explain that logic i think there are some look at that list i can explain that logic i think there are some issue look at that list i can explain that logic i think there are some issue that you are not look at that list i can explain that logic i think there are some issue that you are not able to look at that list i can explain that logic i think there are some issue that you are not able to copy Look at that list. I can explain that logic. I think there are some issue that you are. Not able to copy. just just check just check so just check so there is just check so there is a just check so there is a list just check so there is a list l just check so there is a list l and just check so there is a list l and in this just check so there is a list l and in this one there are multip just check so there is a list l and in this one there are multiple just check so there is a list l and in this one there are multiple national just check so there is a list l and in this one there are multiple national list just check so there is a list l and in this one there are multiple national lists so just check so there is a list l and in this one there are multiple national lists so in that one just check so there is a list l and in this one there are multiple national lists so in that one you have to just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all the negative just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all the negative value just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all the negative value with the zero Just check. So there is a list l. And in this one, there are multiple national lists, so. In that one, you have to replace all the negative value with the zero. so you can so you can create So you can create. dummy Dummy. okay Okay.
2024-09-03 11:43:15.560908: home Home. yes Yes. currently currently i'm currently i'm looking Currently I'm looking. yes yes that's fine Yes, that's fine. i have to i have to click i have to click zero one i have to click zero one years and i have to click zero one years and the relevant experience i have to click zero one years and the relevant experience is i have to click zero one years and the relevant experience is like four i have to click zero one years and the relevant experience is like four point i have to click zero one years and the relevant experience is like four point three i have to click zero one years and the relevant experience is like four point three years i have to click zero one years and the relevant experience is like four point three years with regard i have to click zero one years and the relevant experience is like four point three years with regarding i have to click zero one years and the relevant experience is like four point three years with regarding data sci i have to click zero one years and the relevant experience is like four point three years with regarding data science i have to click zero one years and the relevant experience is like four point three years with regarding data science yeah I have to click zero one years and the relevant experience is like 4.3 years with regarding data science. Yeah. seventeen 17. four point four point three 4.3. yes yes we take yes we take communications Yes, we take communications. like like already like already serving not like already serving notice Like, already serving notice. yeah yeah can i call yeah can i call you in two minutes yeah can i call you in two minutes please Yeah. Can I call you in two minutes, please? okay okay fine okay fine sure Okay, fine. Sure. thank you Thank you. hello Hello. sha Sha. shai Shai. yeah sure yeah sure so yeah sure so we're going to start yeah sure so we're going to start the interview yeah sure so we're going to start the interview so yeah sure so we're going to start the interview so please yeah sure so we're going to start the interview so please start yeah sure so we're going to start the interview so please start your yeah sure so we're going to start the interview so please start your introduction yeah sure so we're going to start the interview so please start your introduction okay yeah sure so we're going to start the interview so please start your introduction okay fine yeah sure so we're going to start the interview so please start your introduction okay fine so yeah sure so we're going to start the interview so please start your introduction okay fine so myself Yeah, sure. So we're going to start the interview, so please start your introduction. Okay, fine. So myself. and and i'm having and i'm having totally and i'm having totally point one and i'm having totally point one years experience and i'm having totally point one years experience in and i'm having totally point one years experience in it and i'm having totally point one years experience in it the and i'm having totally point one years experience in it the learned experience and i'm having totally point one years experience in it the learned experience with and i'm having totally point one years experience in it the learned experience with data science and i'm having totally point one years experience in it the learned experience with data science and am and i'm having totally point one years experience in it the learned experience with data science and aml is and i'm having totally point one years experience in it the learned experience with data science and aml is around and i'm having totally point one years experience in it the learned experience with data science and aml is around four and i'm having totally point one years experience in it the learned experience with data science and aml is around four years and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like nump and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pand and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi nlt and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi nlt k ten and i'm having totally point one years experience in it the learned experience with data science and aml is around four years two months and mostly about with python and i work with libraries tools like numpy pandas sci fi nlt k tensorflow And I'm having totally point one years experience in it, the learned experience with data science. And AML is around four years, two months and mostly about with python and I work with libraries, tools like numpy pandas, Sci-Fi nlt k, tensorflow. space spacey and spacey and cycling those spacey and cycling those kind of things spacey and cycling those kind of things i worked spacey and cycling those kind of things i worked on spacey and cycling those kind of things i worked on for spacey and cycling those kind of things i worked on for visualization spacey and cycling those kind of things i worked on for visualization part i spacey and cycling those kind of things i worked on for visualization part i worked with spacey and cycling those kind of things i worked on for visualization part i worked with map plot spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and table spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability the spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory and spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory and recreation spacey and cycling those kind of things i worked on for visualization part i worked with map plotlib and cbo and power bi and tableau at tabia works for only for one project i'm a regular expert as in and i'm experienced with the statical analysis including hyper assisting and probability theory and recreation analysis Spacey and cycling, those kind of things I worked on for visualization part I worked with. Map plotlib and CBO and Power Bi and tableau at Tabia works for only for one project. I'm a regular expert, as in, and I'm experienced with the statical analysis, including hyper assisting and probability theory and recreation analysis and. risk risk assessment risk assessment those things risk assessment those things have worked risk assessment those things have worked on risk assessment those things have worked on and in risk assessment those things have worked on and in machine learning risk assessment those things have worked on and in machine learning i have risk assessment those things have worked on and in machine learning i have worked on risk assessment those things have worked on and in machine learning i have worked on projects risk assessment those things have worked on and in machine learning i have worked on projects like risk assessment those things have worked on and in machine learning i have worked on projects like regression risk assessment those things have worked on and in machine learning i have worked on projects like regression class risk assessment those things have worked on and in machine learning i have worked on projects like regression classification risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommend risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation system risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience with risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience with nlp risk assessment those things have worked on and in machine learning i have worked on projects like regression classification clustering and the recommendation systems and setting of as luck executes those things i have worked on and also have hands on experience with nlp as well Risk assessment. Those things have worked on. And in machine learning, I have worked on projects like regression classification, clustering and the recommendation systems and setting of as luck executes those things. I have worked on and also have hands on experience with NLP as well. like fr like frameworks like frameworks like like frameworks like what like frameworks like what keras like frameworks like what keras and like frameworks like what keras and tenso like frameworks like what keras and tensorflo like frameworks like what keras and tensorflow like frameworks like what keras and tensorflow bright arch like frameworks like what keras and tensorflow bright arch open like frameworks like what keras and tensorflow bright arch open cv like frameworks like what keras and tensorflow bright arch open cv those like frameworks like what keras and tensorflow bright arch open cv those things i have most like frameworks like what keras and tensorflow bright arch open cv those things i have mostly about like frameworks like what keras and tensorflow bright arch open cv those things i have mostly about one like frameworks like what keras and tensorflow bright arch open cv those things i have mostly about one and Like frameworks. Like what? Keras and tensorflow, bright arch, open cv, those things I have. Mostly about one and. with with the perform with the perform that with the perform that i with the perform that i worked with With the perform that I worked with. apha apha development apha development as well apha development as well the part also apha development as well the part also i have apha development as well the part also i have worked on apha development as well the part also i have worked on with the databases apha development as well the part also i have worked on with the databases i apha development as well the part also i have worked on with the databases i worked with apha development as well the part also i have worked on with the databases i worked with mysql apha development as well the part also i have worked on with the databases i worked with mysql sql Apha development as well. The part also I have worked on with the databases I worked with MySQL, sql. and and most most of the and most most of the things i and most most of the things i worked on and most most of the things i worked on was those things only and most most of the things i worked on was those things only and and most most of the things i worked on was those things only and with my and most most of the things i worked on was those things only and with my skill i and most most of the things i worked on was those things only and with my skill i work with and most most of the things i worked on was those things only and with my skill i work with only And most, most of the things I worked on was those things only. And with my skill, I work with only. for the For the. storing Storing. sorry sorry i transform sorry i transform the data sorry i transform the data while doing sorry i transform the data while doing the to anal sorry i transform the data while doing the to analysis sorry i transform the data while doing the to analysis part so sorry i transform the data while doing the to analysis part so apart from sorry i transform the data while doing the to analysis part so apart from that Sorry. I transform the data while doing the to analysis part. So apart from that. in my previous project In my previous project. most mostly mostly we develop mostly we developed a system mostly we developed a system that could Mostly, we developed a system that could. do the do the accurately do the accurately identify do the accurately identify and separate do the accurately identify and separate the different do the accurately identify and separate the different types do the accurately identify and separate the different types of do the accurately identify and separate the different types of plastic do the accurately identify and separate the different types of plastic types based do the accurately identify and separate the different types of plastic types based on the do the accurately identify and separate the different types of plastic types based on the images do the accurately identify and separate the different types of plastic types based on the images actually do the accurately identify and separate the different types of plastic types based on the images actually so do the accurately identify and separate the different types of plastic types based on the images actually so that classification do the accurately identify and separate the different types of plastic types based on the images actually so that classification project do the accurately identify and separate the different types of plastic types based on the images actually so that classification project we have do the accurately identify and separate the different types of plastic types based on the images actually so that classification project we have done do the accurately identify and separate the different types of plastic types based on the images actually so that classification project we have done with Do the accurately identify and separate the different types of plastic types based on the images. Actually, so. That classification project we have done with. the main the main aim the main aim is the main aim is to the main aim is to identify the main aim is to identify the chemical the main aim is to identify the chemical contam the main aim is to identify the chemical contamination the main aim is to identify the chemical contamination and then the main aim is to identify the chemical contamination and then ident the main aim is to identify the chemical contamination and then identify the the main aim is to identify the chemical contamination and then identify the plastic the main aim is to identify the chemical contamination and then identify the plastic objects the main aim is to identify the chemical contamination and then identify the plastic objects and the main aim is to identify the chemical contamination and then identify the plastic objects and those type the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so before going to the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so before going to the rec the main aim is to identify the chemical contamination and then identify the plastic objects and those types actually so before going to the recycling The main aim is to identify the chemical contamination and then identify the plastic objects and those types. Actually. So before going to the recycling. landfill Landfill. the wast the waste management the waste management team is the waste management team is asked to the waste management team is asked to recycle the waste management team is asked to recycle identify The waste management team is asked to recycle, identify. the the plastic type The plastic type. and and it had a and it had a chemical cont and it had a chemical contamination and it had a chemical contamination or not and it had a chemical contamination or not so those kind and it had a chemical contamination or not so those kind of things and it had a chemical contamination or not so those kind of things we need to ident and it had a chemical contamination or not so those kind of things we need to identify so and it had a chemical contamination or not so those kind of things we need to identify so there are totally and it had a chemical contamination or not so those kind of things we need to identify so there are totally types and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different plastics and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different plastics we have and it had a chemical contamination or not so those kind of things we need to identify so there are totally types of plastics will be there so we'll get like pt http pvc ldp those kind of different plastics we have worked on And it had a chemical contamination or not, so those kind of things we need to identify. So there are totally types of plastics will be there. So we'll get like Pt, HTTP PVC. LDP, those kind of different plastics we have worked on. my my name is my name is to my name is to reduce my name is to reduce the operational my name is to reduce the operational cost and my name is to reduce the operational cost and before going my name is to reduce the operational cost and before going to the My name is to reduce the operational cost and before going to the. resetting resetting area resetting area recycling resetting area recycling plan resetting area recycling plant so resetting area recycling plant so that's Resetting area recycling plant. So that's. the main the main thing The main thing. we get we get the data we get the data mostly we get the data mostly from the we get the data mostly from the videos we get the data mostly from the videos like we we get the data mostly from the videos like we will get kind of we get the data mostly from the videos like we will get kind of twenty we get the data mostly from the videos like we will get kind of twenty signals of we get the data mostly from the videos like we will get kind of twenty signals of videos and we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the imag we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of random we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly pick the we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly pick the images we get the data mostly from the videos like we will get kind of twenty signals of videos and we need to extract the images from there so instead of randomly pick the images we use We get the data mostly from the videos. Like, we will get kind of 20 signals of videos. And we need to extract the images from there. So instead of randomly pick the images, we use. the kind of the kind of motion direct the kind of motion direction algor the kind of motion direction algorithms The kind of motion direction algorithms. where where we have to where we have to subtract Where we have to subtract. the background the background and everything the background and everything and which the background and everything and which is available the background and everything and which is available in open the background and everything and which is available in opencv the background and everything and which is available in opencv to the background and everything and which is available in opencv to identify the background and everything and which is available in opencv to identify the frames the background and everything and which is available in opencv to identify the frames and everything the background and everything and which is available in opencv to identify the frames and everything so the background and everything and which is available in opencv to identify the frames and everything so what the background and everything and which is available in opencv to identify the frames and everything so what we have done is the background and everything and which is available in opencv to identify the frames and everything so what we have done is like the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the frame the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the framework the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the framework scoring the background and everything and which is available in opencv to identify the frames and everything so what we have done is like we have given the framework scoring so where The background and everything, and which is available in OpenCV to identify the frames and everything. So what we have done is, like, we have given the framework scoring. So where. it had it had higher points it had higher points like it had higher points like to implement it had higher points like to implement fact it had higher points like to implement factors such it had higher points like to implement factors such as It had higher points like to implement factors such as. object objective objective class objective classify and objective classify and the overlap Objective, classify and the overlap. and and angle and angle coverage and angle coverage those kind of and angle coverage those kind of things And angle coverage, those kind of things. where it can where it can have where it can have good where it can have good image where it can have good image so where it can have good image so it will where it can have good image so it will have a higher where it can have good image so it will have a higher score where it can have good image so it will have a higher score and where it can have good image so it will have a higher score and it will where it can have good image so it will have a higher score and it will pick as that where it can have good image so it will have a higher score and it will pick as that image so where it can have good image so it will have a higher score and it will pick as that image so from that where it can have good image so it will have a higher score and it will pick as that image so from that image Where it can have good image so it will have a higher score and it will pick as that image. So from that image. there is there is a limitation there is a limitation we have there is a limitation we have to there is a limitation we have to collect there is a limitation we have to collect ten there is a limitation we have to collect ten images There is a limitation. We have to collect ten images. for the video for the video and then after for the video and then after that for the video and then after that we have for the video and then after that we have to do the for the video and then after that we have to do the classification for the video and then after that we have to do the classification part and for the video and then after that we have to do the classification part and everything for the video and then after that we have to do the classification part and everything so for the video and then after that we have to do the classification part and everything so that's for the video and then after that we have to do the classification part and everything so that's the thing and for the video and then after that we have to do the classification part and everything so that's the thing and we have done for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection classification for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection classification we have for the video and then after that we have to do the classification part and everything so that's the thing and we have done many things in the pipeline like object detection classification we have used For the video, and then after that, we have to do the classification part and everything so that's. The thing. And we have done many things in the pipeline, like object detection, classification. We have used. like like mascar like mascarn and like mascarn and c like mascarn and cnn Like mascarn and cnn. those kind of those kind of models those kind of models we have used Those kind of models we have used. to to the bonding to the bonding boxes to the bonding boxes and everything to the bonding boxes and everything after that to the bonding boxes and everything after that we have done to the bonding boxes and everything after that we have done image to the bonding boxes and everything after that we have done image enhance to the bonding boxes and everything after that we have done image enhancements to the bonding boxes and everything after that we have done image enhancements and to to the bonding boxes and everything after that we have done image enhancements and to find to the bonding boxes and everything after that we have done image enhancements and to find out layers to the bonding boxes and everything after that we have done image enhancements and to find out layers actually to the bonding boxes and everything after that we have done image enhancements and to find out layers actually to To the bonding boxes and everything after that, we have done image enhancements and to find out layers, actually, to. add add blurry add blurry images add blurry images and add blurry images and everything add blurry images and everything we have to enhance add blurry images and everything we have to enhance the pixel add blurry images and everything we have to enhance the pixel size add blurry images and everything we have to enhance the pixel sizes add blurry images and everything we have to enhance the pixel sizes and everything add blurry images and everything we have to enhance the pixel sizes and everything so add blurry images and everything we have to enhance the pixel sizes and everything so those things add blurry images and everything we have to enhance the pixel sizes and everything so those things also add blurry images and everything we have to enhance the pixel sizes and everything so those things also have add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and for anom add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and for anomaly add blurry images and everything we have to enhance the pixel sizes and everything so those things also have done after that for the chemical contamination and for anomaly identification Add blurry images and everything. We have to enhance the pixel sizes and everything. So those things also have done after that for the chemical contamination and for anomaly identification. we have we have used we have used kind we have used kind of We have used kind of. when when a chemical when a chemical waste is going on When a chemical waste is going on. we we have used we have used kind of senso we have used kind of sensor actually we have used kind of sensor actually so that we have used kind of sensor actually so that sensor we have used kind of sensor actually so that sensor data We have used kind of sensor, actually, so that sensor data. is a combina is a combination of Is a combination of. senso sensor data Sensor data. with With. image Image. imag image identification image identification so image identification so when we image identification so when we both confir image identification so when we both confirms image identification so when we both confirms so image identification so when we both confirms so we identify image identification so when we both confirms so we identify the sahar image identification so when we both confirms so we identify the sahara image identification so when we both confirms so we identify the sahara chemical cont image identification so when we both confirms so we identify the sahara chemical contamina image identification so when we both confirms so we identify the sahara chemical contamination so image identification so when we both confirms so we identify the sahara chemical contamination so for that image identification so when we both confirms so we identify the sahara chemical contamination so for that we have image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mg image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries who have used image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries who have used after image identification so when we both confirms so we identify the sahara chemical contamination so for that we have used a different kind of like mgt and those kind of libraries who have used after that Image identification. So when we both confirms, so we identify the Sahara chemical contamination. So for that we have used a different kind of like MGT and those kind of libraries who have used after that. we we identify we identify the we identify the images we identify the images and then we identify the images and then we have class we identify the images and then we have classified it we identify the images and then we have classified it and then we have we identify the images and then we have classified it and then we have done we identify the images and then we have classified it and then we have done that we identify the images and then we have classified it and then we have done that deplo we identify the images and then we have classified it and then we have done that deployment part we identify the images and then we have classified it and then we have done that deployment part and everything we identify the images and then we have classified it and then we have done that deployment part and everything so we identify the images and then we have classified it and then we have done that deployment part and everything so that's we identify the images and then we have classified it and then we have done that deployment part and everything so that's our main thing We identify the images, and then we have classified it, and then we have done that deployment part. And everything, so that's our main thing. yeah yeah can you Yeah. Can you? so so just so just show me your technical so just show me your technical round so so just show me your technical round so i'm going to so just show me your technical round so i'm going to ask so just show me your technical round so i'm going to ask some python so just show me your technical round so i'm going to ask some python coding so just show me your technical round so i'm going to ask some python coding questions so just show me your technical round so i'm going to ask some python coding questions so so just show me your technical round so i'm going to ask some python coding questions so you have to so just show me your technical round so i'm going to ask some python coding questions so you have to compute so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in given time so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in given time of so just show me your technical round so i'm going to ask some python coding questions so you have to compute that in given time of play okay So just show me your technical round. So I'm going to ask some python coding questions. So you have to compute that in given time of play, okay? so so you can so you can share your so you can share your screen so you can share your screen you can so you can share your screen you can use it any so you can share your screen you can use it any online ed so you can share your screen you can use it any online editor so you can share your screen you can use it any online editor or so you can share your screen you can use it any online editor or any so you can share your screen you can use it any online editor or any tip to so you can share your screen you can use it any online editor or any tip to anything so you can share your screen you can use it any online editor or any tip to anything i'm just going so you can share your screen you can use it any online editor or any tip to anything i'm just going to share so you can share your screen you can use it any online editor or any tip to anything i'm just going to share one so you can share your screen you can use it any online editor or any tip to anything i'm just going to share one coding so you can share your screen you can use it any online editor or any tip to anything i'm just going to share one coding question So you can share your screen. You can use it any online editor or any tip to anything. I'm just going to share one coding question. share your screen share your screen also share your screen also yeah share your screen also yeah sure Share your screen also? Yeah, sure. please please can you use please can you use another please can you use another editor please can you use another editor like please can you use another editor like are you trying please can you use another editor like are you trying to use please can you use another editor like are you trying to use collab please can you use another editor like are you trying to use collab yes Please. Can you use another editor? Like. Are you trying to use collab? Yes. because because it gives because it gives suggestions because it gives suggestions so because it gives suggestions so can you use because it gives suggestions so can you use another Because it gives suggestions. So can you use another? you can search you can search on google you can search on google python You can search on Google Python. or or you have jupiter Or you have Jupiter. anything Anything. your local your local supply Your local supply. i'm using i'm using collab i'm using collab only i'm using collab only like i'm using collab only like any other I'm using collab only like any other. vs vs code is vs code is okay vs code is okay for you vs code is okay for you but vs code is okay for you but vs code Vs. Code is okay for you, but vs. Code. but but don't have any but don't have any jupiter but don't have any jupiter in that but don't have any jupiter in that actually But don't have any Jupiter in that, actually. you can run you can run that particular code you can run that particular code anywhere You can run that particular code anywhere. just a just a second Just a second. maybe Maybe. try to Try to. i've i've shared i've shared one i've shared one link this i've shared one link this is online i've shared one link this is online you can i've shared one link this is online you can use that i've shared one link this is online you can use that one i've shared one link this is online you can use that one okay that's i've shared one link this is online you can use that one okay that's fine I've shared one link. This is online. You can use that one. Okay, that's. Fine. at all At all. where it is Where it is. okay Okay. i i've shared one i've shared one question also i've shared one question also python question i've shared one question also python question you can i've shared one question also python question you can copy and I've shared one question also Python question. You can copy and. copy copy here Copy here. no good no good fine No. Good. Fine. is there any is there any issue Is there any issue? like Like. it's not it's not copying It's not copying. anything anything because anything because of Anything because of. the The. something something wrong Something wrong. just just give me just give me a minute just give me a minute okay Just give me a minute, okay? then Then. look at look at that look at that list i can look at that list i can explain look at that list i can explain that look at that list i can explain that logic look at that list i can explain that logic i think there are look at that list i can explain that logic i think there are some look at that list i can explain that logic i think there are some issue look at that list i can explain that logic i think there are some issue that you are not look at that list i can explain that logic i think there are some issue that you are not able to look at that list i can explain that logic i think there are some issue that you are not able to copy Look at that list. I can explain that logic. I think there are some issue that you are. Not able to copy. just just check just check so just check so there is just check so there is a just check so there is a list just check so there is a list l just check so there is a list l and just check so there is a list l and in this just check so there is a list l and in this one there are multip just check so there is a list l and in this one there are multiple just check so there is a list l and in this one there are multiple national just check so there is a list l and in this one there are multiple national list just check so there is a list l and in this one there are multiple national lists so just check so there is a list l and in this one there are multiple national lists so in that one just check so there is a list l and in this one there are multiple national lists so in that one you have to just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all the negative just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all the negative value just check so there is a list l and in this one there are multiple national lists so in that one you have to replace all the negative value with the zero Just check. So there is a list l. And in this one, there are multiple national lists, so. In that one, you have to replace all the negative value with the zero. so you can so you can create So you can create. dummy Dummy. okay Okay.
2024-09-03 11:53:54.924020: we need we need to replace we need to replace with a value We need to replace with a value. okay okay fine Okay, fine. you can use you can use the you can use the negative you can use the negative value You can use the negative value. with With. it is kind of it is kind of a matrix it is kind of a matrix kind of thing it is kind of a matrix kind of thing right It is kind of a matrix kind of thing, right? okay okay fine Okay, fine. okay okay fine Okay, fine. then going then going okay fine then going okay fine so then going okay fine so goes in then going okay fine so goes in a range Then going. Okay, fine. So goes in a range. okay fine okay fine i'll try to okay fine i'll try to write one okay fine i'll try to write one code Okay, fine. I'll try to write one code. here it is Here it is. and and this will and this will replace and this will replace the zero and this will replace the zero value and this will replace the zero value right i mean and this will replace the zero value right i mean after and this will replace the zero value right i mean after matrix and this will replace the zero value right i mean after matrix the and this will replace the zero value right i mean after matrix the values and this will replace the zero value right i mean after matrix the values like if you give and this will replace the zero value right i mean after matrix the values like if you give you any mat and this will replace the zero value right i mean after matrix the values like if you give you any matrix And this will replace the zero value, right? I mean, after matrix, the values like if you give. You any matrix. so here so here what so here what it will so here what it will be so here what it will be do is so here what it will be do is like so here what it will be do is like it will go so here what it will be do is like it will go with so here what it will be do is like it will go with the function so here what it will be do is like it will go with the function like so here what it will be do is like it will go with the function like it will replace so here what it will be do is like it will go with the function like it will replace the so here what it will be do is like it will go with the function like it will replace the negative value so here what it will be do is like it will go with the function like it will replace the negative value with so here what it will be do is like it will go with the function like it will replace the negative value with a zero so here what it will be do is like it will go with the function like it will replace the negative value with a zero like if so here what it will be do is like it will go with the function like it will replace the negative value with a zero like if we had any so here what it will be do is like it will go with the function like it will replace the negative value with a zero like if we had any kind of So here what it will be do is like it will go with the function like it will. Replace the negative value with a zero. Like if we had any kind of. list list or something List or something. okay Okay. can you can you test can you test with can you test with the dummy can you test with the dummy met can you test with the dummy method Can you test with the dummy method? i've shown i've shown i just cop i've shown i just copy some value i've shown i just copy some values in that i've shown i just copy some values in that from that i've shown i just copy some values in that from that one i've shown i just copy some values in that from that one like type i've shown i just copy some values in that from that one like type manual i've shown i just copy some values in that from that one like type manual type I've shown. I just copy some values in that from that one, like type manual type. okay fine okay fine i will okay fine i will try to okay fine i will try to get one Okay, fine. I will try to get one. so can so can i so can i go So can I go? any kind of Any kind of. this is this is a domain this is a domain mat this is a domain matrix This is a domain matrix. we have to print we have to print the value we have to print the values we have to print the values right We have to print the values right. we can we can store we can store in two ways we can store in two ways actually We can store in two ways, actually. here here i'm using here i'm using first here i'm using first term actually here i'm using first term actually so i'm here i'm using first term actually so i'm not sure here i'm using first term actually so i'm not sure on this edit here i'm using first term actually so i'm not sure on this editor Here. I'm using first term, actually, so I'm not sure on this editor. so So. store the result Store. The result? here Here. negative Negative. we have we have to print we have to print the value We have to print the value. six Six. i think i think right i think right the result i think right the result it will give i think right the result it will give and then we i think right the result it will give and then we can print i think right the result it will give and then we can print the result i think right the result it will give and then we can print the result simply i think right the result it will give and then we can print the result simply so i think right the result it will give and then we can print the result simply so i need to i think right the result it will give and then we can print the result simply so i need to write that i think right the result it will give and then we can print the result simply so i need to write that also I think, right, the result it will give and then we can print the result simply so I need to write that also. try try to try to run try to run this Try to run this. remove like remove like p ix Remove like p ix. okay fine Okay, fine. i have written i have written points I have written points. okay okay fine okay fine so okay fine so here okay fine so here it will replace okay fine so here it will replace neg okay fine so here it will replace negative Okay, fine. So here. It will replace negative. yeah yeah great Yeah, great. just just share this code just share this code in the check just share this code in the checkbox just share this code in the checkbox i will give you just share this code in the checkbox i will give you one last just share this code in the checkbox i will give you one last question just share this code in the checkbox i will give you one last question okay just share this code in the checkbox i will give you one last question okay fine Just share this code in the checkbox. I will give you one last question. Okay, fine. i think something i think something's wrong i think something's wrong with i think something's wrong with this I think something's wrong with this. not not okay Not okay. i i've shared another question i've shared another question also i've shared another question also can you i've shared another question also can you okay I've shared another question. Also, can you. Okay. something something maybe something maybe it's only something maybe it's only copy something maybe it's only copying something maybe it's only copying there only something maybe it's only copying there only okay fine Something. Maybe it's only copying there. Only? Okay, fine. apart from apart from this Apart from this. it's not it's not copying it's not copying not copying it's not copying not copying again It's not copying. Not copying again. i think i think you are not i think you are not able to i think you are not able to cover i think you are not able to cover okay i think you are not able to cover okay fine i think you are not able to cover okay fine is it possible i think you are not able to cover okay fine is it possible to i think you are not able to cover okay fine is it possible to explain i think you are not able to cover okay fine is it possible to explain the code I think you are not able to cover. Okay, fine. Is it possible to explain the code? pre previously Previously. this this is a new question This is a new question. yeah yeah that's an Yeah, that's an. like like break curly like break curly paper like break curly paper starting like break curly paper starting anything like break curly paper starting anything so you like break curly paper starting anything so you have to valid like break curly paper starting anything so you have to validate like break curly paper starting anything so you have to validate if it is Like break curly paper starting anything. So you have to validate if it is. like bra like bracket like bracket is starting like bracket is starting after Like bracket is starting after. this not in any type this not in any type of this not in any type of predicate this not in any type of predicate if it is not happening this not in any type of predicate if it is not happening it should this not in any type of predicate if it is not happening it should go false this not in any type of predicate if it is not happening it should go false else it should this not in any type of predicate if it is not happening it should go false else it should give true This not in any type of predicate. If it is not happening, it should go false else. It should give true. okay Okay.
2024-09-03 11:53:54.924020: we need we need to replace we need to replace with a value We need to replace with a value. okay okay fine Okay, fine. you can use you can use the you can use the negative you can use the negative value You can use the negative value. with With. it is kind of it is kind of a matrix it is kind of a matrix kind of thing it is kind of a matrix kind of thing right It is kind of a matrix kind of thing, right? okay okay fine Okay, fine. okay okay fine Okay, fine. then going then going okay fine then going okay fine so then going okay fine so goes in then going okay fine so goes in a range Then going. Okay, fine. So goes in a range. okay fine okay fine i'll try to okay fine i'll try to write one okay fine i'll try to write one code Okay, fine. I'll try to write one code. here it is Here it is. and and this will and this will replace and this will replace the zero and this will replace the zero value and this will replace the zero value right i mean and this will replace the zero value right i mean after and this will replace the zero value right i mean after matrix and this will replace the zero value right i mean after matrix the and this will replace the zero value right i mean after matrix the values and this will replace the zero value right i mean after matrix the values like if you give and this will replace the zero value right i mean after matrix the values like if you give you any mat and this will replace the zero value right i mean after matrix the values like if you give you any matrix And this will replace the zero value, right? I mean, after matrix, the values like if you give. You any matrix. so here so here what so here what it will so here what it will be so here what it will be do is so here what it will be do is like so here what it will be do is like it will go so here what it will be do is like it will go with so here what it will be do is like it will go with the function so here what it will be do is like it will go with the function like so here what it will be do is like it will go with the function like it will replace so here what it will be do is like it will go with the function like it will replace the so here what it will be do is like it will go with the function like it will replace the negative value so here what it will be do is like it will go with the function like it will replace the negative value with so here what it will be do is like it will go with the function like it will replace the negative value with a zero so here what it will be do is like it will go with the function like it will replace the negative value with a zero like if so here what it will be do is like it will go with the function like it will replace the negative value with a zero like if we had any so here what it will be do is like it will go with the function like it will replace the negative value with a zero like if we had any kind of So here what it will be do is like it will go with the function like it will. Replace the negative value with a zero. Like if we had any kind of. list list or something List or something. okay Okay. can you can you test can you test with can you test with the dummy can you test with the dummy met can you test with the dummy method Can you test with the dummy method? i've shown i've shown i just cop i've shown i just copy some value i've shown i just copy some values in that i've shown i just copy some values in that from that i've shown i just copy some values in that from that one i've shown i just copy some values in that from that one like type i've shown i just copy some values in that from that one like type manual i've shown i just copy some values in that from that one like type manual type I've shown. I just copy some values in that from that one, like type manual type. okay fine okay fine i will okay fine i will try to okay fine i will try to get one Okay, fine. I will try to get one. so can so can i so can i go So can I go? any kind of Any kind of. this is this is a domain this is a domain mat this is a domain matrix This is a domain matrix. we have to print we have to print the value we have to print the values we have to print the values right We have to print the values right. we can we can store we can store in two ways we can store in two ways actually We can store in two ways, actually. here here i'm using here i'm using first here i'm using first term actually here i'm using first term actually so i'm here i'm using first term actually so i'm not sure here i'm using first term actually so i'm not sure on this edit here i'm using first term actually so i'm not sure on this editor Here. I'm using first term, actually, so I'm not sure on this editor. so So. store the result Store. The result? here Here. negative Negative. we have we have to print we have to print the value We have to print the value. six Six. i think i think right i think right the result i think right the result it will give i think right the result it will give and then we i think right the result it will give and then we can print i think right the result it will give and then we can print the result i think right the result it will give and then we can print the result simply i think right the result it will give and then we can print the result simply so i think right the result it will give and then we can print the result simply so i need to i think right the result it will give and then we can print the result simply so i need to write that i think right the result it will give and then we can print the result simply so i need to write that also I think, right, the result it will give and then we can print the result simply so I need to write that also. try try to try to run try to run this Try to run this. remove like remove like p ix Remove like p ix. okay fine Okay, fine. i have written i have written points I have written points. okay okay fine okay fine so okay fine so here okay fine so here it will replace okay fine so here it will replace neg okay fine so here it will replace negative Okay, fine. So here. It will replace negative. yeah yeah great Yeah, great. just just share this code just share this code in the check just share this code in the checkbox just share this code in the checkbox i will give you just share this code in the checkbox i will give you one last just share this code in the checkbox i will give you one last question just share this code in the checkbox i will give you one last question okay just share this code in the checkbox i will give you one last question okay fine Just share this code in the checkbox. I will give you one last question. Okay, fine. i think something i think something's wrong i think something's wrong with i think something's wrong with this I think something's wrong with this. not not okay Not okay. i i've shared another question i've shared another question also i've shared another question also can you i've shared another question also can you okay I've shared another question. Also, can you. Okay. something something maybe something maybe it's only something maybe it's only copy something maybe it's only copying something maybe it's only copying there only something maybe it's only copying there only okay fine Something. Maybe it's only copying there. Only? Okay, fine. apart from apart from this Apart from this. it's not it's not copying it's not copying not copying it's not copying not copying again It's not copying. Not copying again. i think i think you are not i think you are not able to i think you are not able to cover i think you are not able to cover okay i think you are not able to cover okay fine i think you are not able to cover okay fine is it possible i think you are not able to cover okay fine is it possible to i think you are not able to cover okay fine is it possible to explain i think you are not able to cover okay fine is it possible to explain the code I think you are not able to cover. Okay, fine. Is it possible to explain the code? pre previously Previously. this this is a new question This is a new question. yeah yeah that's an Yeah, that's an. like like break curly like break curly paper like break curly paper starting like break curly paper starting anything like break curly paper starting anything so you like break curly paper starting anything so you have to valid like break curly paper starting anything so you have to validate like break curly paper starting anything so you have to validate if it is Like break curly paper starting anything. So you have to validate if it is. like bra like bracket like bracket is starting like bracket is starting after Like bracket is starting after. this not in any type this not in any type of this not in any type of predicate this not in any type of predicate if it is not happening this not in any type of predicate if it is not happening it should this not in any type of predicate if it is not happening it should go false this not in any type of predicate if it is not happening it should go false else it should this not in any type of predicate if it is not happening it should go false else it should give true This not in any type of predicate. If it is not happening, it should go false else. It should give true. okay Okay.
