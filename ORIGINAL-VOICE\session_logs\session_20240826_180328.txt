2024-08-26 18:12:32.379536: okay okay so okay so how okay so how do you okay so how do you track Okay, so how do you track. your Your. parameter Parameter.
2024-08-26 18:12:32.386065: okay okay so okay so how okay so how do you okay so how do you track Okay, so how do you track. your Your. parameter Parameter.
2024-08-26 18:13:59.471309: as in as in whatever as in whatever you are tuning as in whatever you are tuning how as in whatever you are tuning how you record as in whatever you are tuning how you record those as in whatever you are tuning how you record those do you record as in whatever you are tuning how you record those do you record those as in whatever you are tuning how you record those do you record those like as in whatever you are tuning how you record those do you record those like okay As in whatever you are tuning, how you record those. Do you record those? Like, okay. with this model with this model it with this model it was with this model it was so and so with this model it was so and so after with this model it was so and so after doing with this model it was so and so after doing some changes with this model it was so and so after doing some changes to the with this model it was so and so after doing some changes to the model with this model it was so and so after doing some changes to the model it became with this model it was so and so after doing some changes to the model it became so and with this model it was so and so after doing some changes to the model it became so and so With this model, it was so and so. After doing some changes to the model, it became so. And so. like like while while like while while i'm like while while i'm like i like while while i'm like i did not understand like while while i'm like i did not understand some part like while while i'm like i did not understand some part actually like while while i'm like i did not understand some part actually i mean like while while i'm like i did not understand some part actually i mean well like while while i'm like i did not understand some part actually i mean well if like while while i'm like i did not understand some part actually i mean well if i'm doing an like while while i'm like i did not understand some part actually i mean well if i'm doing an experiment Like, while. While I'm like, I did not understand some part, actually. I mean, well, if. I'm doing an experiment. i do I do. like like ten set like ten sets of read like ten sets of reading correct Like, ten sets of reading, correct? and and i and i note that read and i note that reading correct and i note that reading correct i and i note that reading correct i make a table and i note that reading correct i make a table i and i note that reading correct i make a table i note down the and i note that reading correct i make a table i note down the reading and i note that reading correct i make a table i note down the reading correct and i note that reading correct i make a table i note down the reading correct yes and i note that reading correct i make a table i note down the reading correct yes like and i note that reading correct i make a table i note down the reading correct yes like simple ten and i note that reading correct i make a table i note down the reading correct yes like simple ten standard exper and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay now and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay now when you're running and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay now when you're running this project And I note that reading correct. I make a table, I note down the reading correct. Yes. Like, simple ten standard experiments, right? Okay. Now, when you're running this project, you're you're tuning it you're tuning it yes you're tuning it yes okay You're tuning it? Yes. Okay. that that means you are that means you are making some that means you are making some changes That means you are making some changes. correct correct so correct so with correct so with each correct so with each what correct so with each what is the result Correct. So with each, what is the result? are you tracking are you tracking that Are you tracking that? okay okay definitely okay definitely or is it okay definitely or is it just like okay definitely or is it just like okay okay definitely or is it just like okay this is good okay definitely or is it just like okay this is good this okay definitely or is it just like okay this is good this is bad Okay, definitely. Or is it just like, okay, this is good, this is bad. okay okay this appears okay this appears to be good okay this appears to be good so okay this appears to be good so let us okay this appears to be good so let us stick with this okay this appears to be good so let us stick with this how do okay this appears to be good so let us stick with this how do you make that okay this appears to be good so let us stick with this how do you make that decision okay this appears to be good so let us stick with this how do you make that decision let okay this appears to be good so let us stick with this how do you make that decision let us okay this appears to be good so let us stick with this how do you make that decision let us say okay this appears to be good so let us stick with this how do you make that decision let us say if you okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay you tell him okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay you tell him like Okay, this appears to be good, so let us stick with this. How do you make that decision? Let us say if you give that to a third person, okay, you tell him, like, we did We did. these many these many iterations These many iterations. and And. you take a you take a call on you take a call on which one is you take a call on which one is good you take a call on which one is good so you'll need some you take a call on which one is good so you'll need some data you take a call on which one is good so you'll need some data right you take a call on which one is good so you'll need some data right to you take a call on which one is good so you'll need some data right to look at you take a call on which one is good so you'll need some data right to look at to you take a call on which one is good so you'll need some data right to look at to determine you take a call on which one is good so you'll need some data right to look at to determine how you take a call on which one is good so you'll need some data right to look at to determine how do you track you take a call on which one is good so you'll need some data right to look at to determine how do you track that You take a call on which one is good. So you'll need some data, right, to look at to determine. How do you track that? how
2024-08-26 18:13:59.474290: as in as in whatever as in whatever you are tuning as in whatever you are tuning how as in whatever you are tuning how you record as in whatever you are tuning how you record those as in whatever you are tuning how you record those do you record as in whatever you are tuning how you record those do you record those as in whatever you are tuning how you record those do you record those like as in whatever you are tuning how you record those do you record those like okay As in whatever you are tuning, how you record those. Do you record those? Like, okay. with this model with this model it with this model it was with this model it was so and so with this model it was so and so after with this model it was so and so after doing with this model it was so and so after doing some changes with this model it was so and so after doing some changes to the with this model it was so and so after doing some changes to the model with this model it was so and so after doing some changes to the model it became with this model it was so and so after doing some changes to the model it became so and with this model it was so and so after doing some changes to the model it became so and so With this model, it was so and so. After doing some changes to the model, it became so. And so. like like while while like while while i'm like while while i'm like i like while while i'm like i did not understand like while while i'm like i did not understand some part like while while i'm like i did not understand some part actually like while while i'm like i did not understand some part actually i mean like while while i'm like i did not understand some part actually i mean well like while while i'm like i did not understand some part actually i mean well if like while while i'm like i did not understand some part actually i mean well if i'm doing an like while while i'm like i did not understand some part actually i mean well if i'm doing an experiment Like, while. While I'm like, I did not understand some part, actually. I mean, well, if. I'm doing an experiment. i do I do. like like ten set like ten sets of read like ten sets of reading correct Like, ten sets of reading, correct? and and i and i note that read and i note that reading correct and i note that reading correct i and i note that reading correct i make a table and i note that reading correct i make a table i and i note that reading correct i make a table i note down the and i note that reading correct i make a table i note down the reading and i note that reading correct i make a table i note down the reading correct and i note that reading correct i make a table i note down the reading correct yes and i note that reading correct i make a table i note down the reading correct yes like and i note that reading correct i make a table i note down the reading correct yes like simple ten and i note that reading correct i make a table i note down the reading correct yes like simple ten standard exper and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay now and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay now when you're running and i note that reading correct i make a table i note down the reading correct yes like simple ten standard experiments right okay now when you're running this project And I note that reading correct. I make a table, I note down the reading correct. Yes. Like, simple ten standard experiments, right? Okay. Now, when you're running this project, you're you're tuning it you're tuning it yes you're tuning it yes okay You're tuning it? Yes. Okay. that that means you are that means you are making some that means you are making some changes That means you are making some changes. correct correct so correct so with correct so with each correct so with each what correct so with each what is the result Correct. So with each, what is the result? are you tracking are you tracking that Are you tracking that? okay okay definitely okay definitely or is it okay definitely or is it just like okay definitely or is it just like okay okay definitely or is it just like okay this is good okay definitely or is it just like okay this is good this okay definitely or is it just like okay this is good this is bad Okay, definitely. Or is it just like, okay, this is good, this is bad. okay okay this appears okay this appears to be good okay this appears to be good so okay this appears to be good so let us okay this appears to be good so let us stick with this okay this appears to be good so let us stick with this how do okay this appears to be good so let us stick with this how do you make that okay this appears to be good so let us stick with this how do you make that decision okay this appears to be good so let us stick with this how do you make that decision let okay this appears to be good so let us stick with this how do you make that decision let us okay this appears to be good so let us stick with this how do you make that decision let us say okay this appears to be good so let us stick with this how do you make that decision let us say if you okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay you tell him okay this appears to be good so let us stick with this how do you make that decision let us say if you give that to a third person okay you tell him like Okay, this appears to be good, so let us stick with this. How do you make that decision? Let us say if you give that to a third person, okay, you tell him, like, we did We did. these many these many iterations These many iterations. and And. you take a you take a call on you take a call on which one is you take a call on which one is good you take a call on which one is good so you'll need some you take a call on which one is good so you'll need some data you take a call on which one is good so you'll need some data right you take a call on which one is good so you'll need some data right to you take a call on which one is good so you'll need some data right to look at you take a call on which one is good so you'll need some data right to look at to you take a call on which one is good so you'll need some data right to look at to determine you take a call on which one is good so you'll need some data right to look at to determine how you take a call on which one is good so you'll need some data right to look at to determine how do you track you take a call on which one is good so you'll need some data right to look at to determine how do you track that You take a call on which one is good. So you'll need some data, right, to look at to determine. How do you track that? how
2024-08-26 18:14:35.545463: how is that How is that? there are there are two types of there are two types of things there are two types of things to be there are two types of things to be dragged one there are two types of things to be dragged one is there are two types of things to be dragged one is the model there are two types of things to be dragged one is the model itself there are two types of things to be dragged one is the model itself yes There are two types of things to be dragged. One is the model itself. Yes. which which will probably Which will probably. all the delt all the deltas all the deltas will be store all the deltas will be stored all the deltas will be stored in some all the deltas will be stored in some github or All the deltas will be stored in some GitHub or. some some repository some repository right yes Some repository, right? Yes. but but what each but what each of but what each of the version But what each of the version. results results into Results into. when run When run. okay Okay. those those readings those readings or those readings or those those readings or those output those readings or those outputs those readings or those outputs or those those readings or those outputs or those par those readings or those outputs or those parameters Those readings or those outputs or those parameters. or or sensitivities or sensitivities whatever Or sensitivities, whatever. okay now okay now how do you okay now how do you track okay now how do you track that Okay, now, how do you track that?
2024-08-26 18:14:35.547516: how is that How is that? there are there are two types of there are two types of things there are two types of things to be there are two types of things to be dragged one there are two types of things to be dragged one is there are two types of things to be dragged one is the model there are two types of things to be dragged one is the model itself there are two types of things to be dragged one is the model itself yes There are two types of things to be dragged. One is the model itself. Yes. which which will probably Which will probably. all the delt all the deltas all the deltas will be store all the deltas will be stored all the deltas will be stored in some all the deltas will be stored in some github or All the deltas will be stored in some GitHub or. some some repository some repository right yes Some repository, right? Yes. but but what each but what each of but what each of the version But what each of the version. results results into Results into. when run When run. okay Okay. those those readings those readings or those readings or those those readings or those output those readings or those outputs those readings or those outputs or those those readings or those outputs or those par those readings or those outputs or those parameters Those readings or those outputs or those parameters. or or sensitivities or sensitivities whatever Or sensitivities, whatever. okay now okay now how do you okay now how do you track okay now how do you track that Okay, now, how do you track that?
2024-08-26 18:16:17.549354: we have we have system We have system. or you just maintain or you just maintain it in a piece of or you just maintain it in a piece of paper or you just maintain it in a piece of paper or in or you just maintain it in a piece of paper or in excel or you just maintain it in a piece of paper or in excel and or you just maintain it in a piece of paper or in excel and say okay or you just maintain it in a piece of paper or in excel and say okay this one or you just maintain it in a piece of paper or in excel and say okay this one is good Or you just maintain it in a piece of paper or in excel and say, okay, this one. Is good. it's kind of like It's kind of like. mostly mostly boys done mostly boys done with the weight mostly boys done with the weights and mostly boys done with the weights and bash mostly boys done with the weights and bash while mostly boys done with the weights and bash while further mostly boys done with the weights and bash while further tracking parame mostly boys done with the weights and bash while further tracking parameters mostly boys done with the weights and bash while further tracking parameters like Mostly boys done with the weights and bash while further tracking parameters like. whatever whatever the system whatever the systems you have whatever the systems you have of course whatever the systems you have of course you can do whatever the systems you have of course you can do it whatever the systems you have of course you can do it of course by the whatever the systems you have of course you can do it of course by the autom whatever the systems you have of course you can do it of course by the automation also whatever the systems you have of course you can do it of course by the automation also whatever whatever the systems you have of course you can do it of course by the automation also whatever the autom whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first time you will get whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first time you will get like enough whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first time you will get like enough seventy two percent Whatever the systems you have, of course you can do it. Of course, by the automation also, whatever. The automation is coming like we can store in a temporary data and we'll take the result. And then we can write a python code simply whenever it is crossing the curriculum canister. Cancel, like, for the first time, you will get, like, enough 72% of acc. accuracy Accuracy. in the second time in the second time if in the second time if we go with in the second time if we go with the batch in the second time if we go with the batch by in the second time if we go with the batch by process in the second time if we go with the batch by process sometimes in the second time if we go with the batch by process sometimes the imag in the second time if we go with the batch by process sometimes the images in the second time if we go with the batch by process sometimes the images are kind of most in the second time if we go with the batch by process sometimes the images are kind of mostly with in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right so while in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right so while going in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right so while going into the training In the second time. If we go with the batch by process, sometimes the images are kind of mostly with GPU based, right? So while going into the training, so like So, like. the images The images. while while going while going the decrease in while going the decrease in the amount while going the decrease in the amount of while going the decrease in the amount of images while going the decrease in the amount of images like while going the decrease in the amount of images like you can while going the decrease in the amount of images like you can see like while going the decrease in the amount of images like you can see like four hundred while going the decrease in the amount of images like you can see like four hundred images While going, the decrease in the amount of images like you can see, like 400 images. start start trying start trying in that time start trying in that time actually start trying in that time actually the accur start trying in that time actually the accuracy start trying in that time actually the accuracy is little bit start trying in that time actually the accuracy is little bit high start trying in that time actually the accuracy is little bit high so start trying in that time actually the accuracy is little bit high so we need to start trying in that time actually the accuracy is little bit high so we need to adjust start trying in that time actually the accuracy is little bit high so we need to adjust that based start trying in that time actually the accuracy is little bit high so we need to adjust that based on that start trying in that time actually the accuracy is little bit high so we need to adjust that based on that of course Start trying in that time. Actually, the accuracy is little bit high, so we need to adjust that. Based on that, of course. we have done we have done based on we have done based on manual we have done based on manual itself we have done based on manual itself actually we have done based on manual itself actually i mean of course We have done based on manual itself, actually. I mean, of course. we can we can do it whenever we can do it whenever going we can do it whenever going with the same we can do it whenever going with the same thing with the py we can do it whenever going with the same thing with the python code we can do it whenever going with the same thing with the python code as because we can do it whenever going with the same thing with the python code as because you can store we can do it whenever going with the same thing with the python code as because you can store the data we can do it whenever going with the same thing with the python code as because you can store the data and then whenever we can do it whenever going with the same thing with the python code as because you can store the data and then whenever we train it We can do it whenever going with the same thing with the python code as because you can store the data, and then whenever we train it. we can we can go with we can go with the visual we can go with the visualization we can go with the visualization mode mode also we can go with the visualization mode mode also like we can go with the visualization mode mode also like if you want we can go with the visualization mode mode also like if you want to see we can go with the visualization mode mode also like if you want to see how many we can go with the visualization mode mode also like if you want to see how many times we can go with the visualization mode mode also like if you want to see how many times it's trained we can go with the visualization mode mode also like if you want to see how many times it's trained and everything We can go with the visualization mode. Mode. Also, like if you want to see how many times. It's trained and everything. when you say when you say accurac when you say accuracy how when you say accuracy how do you determine when you say accuracy how do you determine accurac when you say accuracy how do you determine accuracy When you say accuracy, how do you determine accuracy? how do you arrive how do you arrive at how do you arrive at this number how do you arrive at this number seventy how do you arrive at this number seventy percent How do you arrive at this number? 70%. like here we like here we have done
2024-08-26 18:16:17.550336: we have we have system We have system. or you just maintain or you just maintain it in a piece of or you just maintain it in a piece of paper or you just maintain it in a piece of paper or in or you just maintain it in a piece of paper or in excel or you just maintain it in a piece of paper or in excel and or you just maintain it in a piece of paper or in excel and say okay or you just maintain it in a piece of paper or in excel and say okay this one or you just maintain it in a piece of paper or in excel and say okay this one is good Or you just maintain it in a piece of paper or in excel and say, okay, this one. Is good. it's kind of like It's kind of like. mostly mostly boys done mostly boys done with the weight mostly boys done with the weights and mostly boys done with the weights and bash mostly boys done with the weights and bash while mostly boys done with the weights and bash while further mostly boys done with the weights and bash while further tracking parame mostly boys done with the weights and bash while further tracking parameters mostly boys done with the weights and bash while further tracking parameters like Mostly boys done with the weights and bash while further tracking parameters like. whatever whatever the system whatever the systems you have whatever the systems you have of course whatever the systems you have of course you can do whatever the systems you have of course you can do it whatever the systems you have of course you can do it of course by the whatever the systems you have of course you can do it of course by the autom whatever the systems you have of course you can do it of course by the automation also whatever the systems you have of course you can do it of course by the automation also whatever whatever the systems you have of course you can do it of course by the automation also whatever the autom whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first time you will get whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first time you will get like enough whatever the systems you have of course you can do it of course by the automation also whatever the automation is coming like we can store in a temporary data and we'll take the result and then we can write a python code simply whenever it is crossing the curriculum canister cancel like for the first time you will get like enough seventy two percent Whatever the systems you have, of course you can do it. Of course, by the automation also, whatever. The automation is coming like we can store in a temporary data and we'll take the result. And then we can write a python code simply whenever it is crossing the curriculum canister. Cancel, like, for the first time, you will get, like, enough 72% of acc. accuracy Accuracy. in the second time in the second time if in the second time if we go with in the second time if we go with the batch in the second time if we go with the batch by in the second time if we go with the batch by process in the second time if we go with the batch by process sometimes in the second time if we go with the batch by process sometimes the imag in the second time if we go with the batch by process sometimes the images in the second time if we go with the batch by process sometimes the images are kind of most in the second time if we go with the batch by process sometimes the images are kind of mostly with in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right so while in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right so while going in the second time if we go with the batch by process sometimes the images are kind of mostly with gpu based right so while going into the training In the second time. If we go with the batch by process, sometimes the images are kind of mostly with GPU based, right? So while going into the training, so like So, like. the images The images. while while going while going the decrease in while going the decrease in the amount while going the decrease in the amount of while going the decrease in the amount of images while going the decrease in the amount of images like while going the decrease in the amount of images like you can while going the decrease in the amount of images like you can see like while going the decrease in the amount of images like you can see like four hundred while going the decrease in the amount of images like you can see like four hundred images While going, the decrease in the amount of images like you can see, like 400 images. start start trying start trying in that time start trying in that time actually start trying in that time actually the accur start trying in that time actually the accuracy start trying in that time actually the accuracy is little bit start trying in that time actually the accuracy is little bit high start trying in that time actually the accuracy is little bit high so start trying in that time actually the accuracy is little bit high so we need to start trying in that time actually the accuracy is little bit high so we need to adjust start trying in that time actually the accuracy is little bit high so we need to adjust that based start trying in that time actually the accuracy is little bit high so we need to adjust that based on that start trying in that time actually the accuracy is little bit high so we need to adjust that based on that of course Start trying in that time. Actually, the accuracy is little bit high, so we need to adjust that. Based on that, of course. we have done we have done based on we have done based on manual we have done based on manual itself we have done based on manual itself actually we have done based on manual itself actually i mean of course We have done based on manual itself, actually. I mean, of course. we can we can do it whenever we can do it whenever going we can do it whenever going with the same we can do it whenever going with the same thing with the py we can do it whenever going with the same thing with the python code we can do it whenever going with the same thing with the python code as because we can do it whenever going with the same thing with the python code as because you can store we can do it whenever going with the same thing with the python code as because you can store the data we can do it whenever going with the same thing with the python code as because you can store the data and then whenever we can do it whenever going with the same thing with the python code as because you can store the data and then whenever we train it We can do it whenever going with the same thing with the python code as because you can store the data, and then whenever we train it. we can we can go with we can go with the visual we can go with the visualization we can go with the visualization mode mode also we can go with the visualization mode mode also like we can go with the visualization mode mode also like if you want we can go with the visualization mode mode also like if you want to see we can go with the visualization mode mode also like if you want to see how many we can go with the visualization mode mode also like if you want to see how many times we can go with the visualization mode mode also like if you want to see how many times it's trained we can go with the visualization mode mode also like if you want to see how many times it's trained and everything We can go with the visualization mode. Mode. Also, like if you want to see how many times. It's trained and everything. when you say when you say accurac when you say accuracy how when you say accuracy how do you determine when you say accuracy how do you determine accurac when you say accuracy how do you determine accuracy When you say accuracy, how do you determine accuracy? how do you arrive how do you arrive at how do you arrive at this number how do you arrive at this number seventy how do you arrive at this number seventy percent How do you arrive at this number? 70%. like here we like here we have done
2024-08-26 18:16:59.300566: like here we have done the test like here we have done the test we already know like here we have done the test we already know what is like here we have done the test we already know what is one hundred percent like here we have done the test we already know what is one hundred percent yes Like here. We have done the test. We already know what is 100%. Yes. we don't know we don't know exactly we don't know exactly the one hundred we don't know exactly the one hundred percent we don't know exactly the one hundred percent is We don't know exactly the 100% is. but i mean based but i mean based on but i mean based on the but i mean based on the training model But, I mean, based on the training model. yeah Yeah. looking at looking at the image Looking at the image. there is an there is an animal there is an animal view or there is an animal view or there is there is an animal view or there is some issue There is an animal view or there is some issue. or or this is this type of or this is this type of plast or this is this type of plastic or this is this type of plastic whatever or this is this type of plastic whatever you're Or this is this type of plastic, whatever you're. okay okay go on Okay, go on. i'm sorry I'm sorry. what kind of What kind of. okay okay how okay how we find the okay how we find the accuracy okay how we find the accuracy right
2024-08-26 18:16:59.302525: like here we have done the test like here we have done the test we already know like here we have done the test we already know what is like here we have done the test we already know what is one hundred percent like here we have done the test we already know what is one hundred percent yes Like here. We have done the test. We already know what is 100%. Yes. we don't know we don't know exactly we don't know exactly the one hundred we don't know exactly the one hundred percent we don't know exactly the one hundred percent is We don't know exactly the 100% is. but i mean based but i mean based on but i mean based on the but i mean based on the training model But, I mean, based on the training model. yeah Yeah. looking at looking at the image Looking at the image. there is an there is an animal there is an animal view or there is an animal view or there is there is an animal view or there is some issue There is an animal view or there is some issue. or or this is this type of or this is this type of plast or this is this type of plastic or this is this type of plastic whatever or this is this type of plastic whatever you're Or this is this type of plastic, whatever you're. okay okay go on Okay, go on. i'm sorry I'm sorry. what kind of What kind of. okay okay how okay how we find the okay how we find the accuracy okay how we find the accuracy right
2024-08-26 18:18:45.372991: Okay, how we find the accuracy, right? okay okay we have okay we have trained okay we have trained the model Okay, we have trained the model. like based like based on Like, based on. the the while google The while Google. accuracy accuracy score accuracy score like standard accuracy score like standard evol accuracy score like standard evolution like accuracy score like standard evolution like mostly Accuracy score? Like standard evolution? Like, mostly. giving Giving. the the while going the while going with the accur the while going with the accuracy actually the while going with the accuracy actually here the while going with the accuracy actually here we have the while going with the accuracy actually here we have two things the while going with the accuracy actually here we have two things like the while going with the accuracy actually here we have two things like r score the while going with the accuracy actually here we have two things like r score f one sc the while going with the accuracy actually here we have two things like r score f one score also the while going with the accuracy actually here we have two things like r score f one score also have done The while going with the accuracy. Actually, here we have two things, like r score, f one score. Also have done. and and before that and before that also and before that also there and before that also there is and before that also there is some kind of and before that also there is some kind of problem and before that also there is some kind of problem with and before that also there is some kind of problem with what and before that also there is some kind of problem with what kind of data and before that also there is some kind of problem with what kind of data we are and before that also there is some kind of problem with what kind of data we are training and before that also there is some kind of problem with what kind of data we are training also And before that also, there is some kind of problem with what kind of data we are training. Also. so so before that so before that the so before that the quality check so before that the quality check is done so before that the quality check is done after that so before that the quality check is done after that there is so before that the quality check is done after that there is a different so before that the quality check is done after that there is a different kind so before that the quality check is done after that there is a different kind of So before that, the quality check is done. After that, there is a different kind of. scor scoring is coming scoring is coming kind of scoring is coming kind of appreciation while scoring is coming kind of appreciation while doing scoring is coming kind of appreciation while doing the recall scoring is coming kind of appreciation while doing the recall f one score Scoring is coming kind of appreciation while doing the recall f one score. those kind those kind of those kind of perform those kind of performance those kind of performance after those kind of performance after perform those kind of performance after performance testing Those kind of performance after performance testing. it is correct it is correct and then it is correct and then we have done it is correct and then we have done that it is correct and then we have done that process it is correct and then we have done that process that accuracy it is correct and then we have done that process that accuracy test it is correct and then we have done that process that accuracy test and everything it is correct and then we have done that process that accuracy test and everything like how many images it is correct and then we have done that process that accuracy test and everything like how many images are we it is correct and then we have done that process that accuracy test and everything like how many images are we processing It is correct. And then we have done that process, that accuracy test and everything, like how many images are we processing? the four hundred imag the four hundred images the four hundred images with the less the four hundred images with the less quality The 400 images with the less quality. and then and then the same four hundred and then the same four hundred ms and then the same four hundred ms with high and then the same four hundred ms with high quality and then the same four hundred ms with high quality so and then the same four hundred ms with high quality so there is and then the same four hundred ms with high quality so there is a difference and then the same four hundred ms with high quality so there is a difference like that And then the same 400 ms with high quality. So there is a difference like that. we have calcul we have calculated with we have calculated with f and square we have calculated with f and square and everything we have calculated with f and square and everything so we have calculated with f and square and everything so i like that we have calculated with f and square and everything so i like that we have We have calculated with f and square and everything, so I like that we have. okay Okay. so so for how many so for how many years so for how many years you have been so for how many years you have been working with So for how many years you have been working with. ten tensorflo tensorflow tensorflow krs and tensorflow krs and py tensorflow krs and python Tensorflow, krs and python. like like three like three projects like three projects i have done like three projects i have done three like three projects i have done three years Like, three projects I have done three years. three three years Three years. okay okay and okay and you've done the okay and you've done the ibm okay and you've done the ibm cert okay and you've done the ibm certification Okay. And you've done the IBM certification. yes yes well yes well i'm doing yes well i'm doing how much time yes well i'm doing how much time it took yes well i'm doing how much time it took you to yes well i'm doing how much time it took you to get yes well i'm doing how much time it took you to get your ibm yes well i'm doing how much time it took you to get your ibm cert yes well i'm doing how much time it took you to get your ibm certification Yes, well, I'm doing how much time it took you to get your IBM certification. like like forty like forty days Like, 40 days. in in the beginning in the beginning days i have done in the beginning days i have done actually in the beginning days i have done actually i in the beginning days i have done actually i required in the beginning days i have done actually i required that in the beginning days i have done actually i required that certification In the beginning days, I have done. Actually, I required that certification. what is that what is that in the what is that in the beginning what is that in the beginning days what is that in the beginning days of my care what is that in the beginning days of my career actually what is that in the beginning days of my career actually i have done what is that in the beginning days of my career actually i have done that cert what is that in the beginning days of my career actually i have done that certificate what is that in the beginning days of my career actually i have done that certificate after what is that in the beginning days of my career actually i have done that certificate after that What is that? In the beginning days of my career, actually I have done that certificate after that. okay okay where did you okay where did you learn the okay where did you learn the statistics
2024-08-26 18:18:45.375011: Okay, how we find the accuracy, right? okay okay we have okay we have trained okay we have trained the model Okay, we have trained the model. like based like based on Like, based on. the the while google The while Google. accuracy accuracy score accuracy score like standard accuracy score like standard evol accuracy score like standard evolution like accuracy score like standard evolution like mostly Accuracy score? Like standard evolution? Like, mostly. giving Giving. the the while going the while going with the accur the while going with the accuracy actually the while going with the accuracy actually here the while going with the accuracy actually here we have the while going with the accuracy actually here we have two things the while going with the accuracy actually here we have two things like the while going with the accuracy actually here we have two things like r score the while going with the accuracy actually here we have two things like r score f one sc the while going with the accuracy actually here we have two things like r score f one score also the while going with the accuracy actually here we have two things like r score f one score also have done The while going with the accuracy. Actually, here we have two things, like r score, f one score. Also have done. and and before that and before that also and before that also there and before that also there is and before that also there is some kind of and before that also there is some kind of problem and before that also there is some kind of problem with and before that also there is some kind of problem with what and before that also there is some kind of problem with what kind of data and before that also there is some kind of problem with what kind of data we are and before that also there is some kind of problem with what kind of data we are training and before that also there is some kind of problem with what kind of data we are training also And before that also, there is some kind of problem with what kind of data we are training. Also. so so before that so before that the so before that the quality check so before that the quality check is done so before that the quality check is done after that so before that the quality check is done after that there is so before that the quality check is done after that there is a different so before that the quality check is done after that there is a different kind so before that the quality check is done after that there is a different kind of So before that, the quality check is done. After that, there is a different kind of. scor scoring is coming scoring is coming kind of scoring is coming kind of appreciation while scoring is coming kind of appreciation while doing scoring is coming kind of appreciation while doing the recall scoring is coming kind of appreciation while doing the recall f one score Scoring is coming kind of appreciation while doing the recall f one score. those kind those kind of those kind of perform those kind of performance those kind of performance after those kind of performance after perform those kind of performance after performance testing Those kind of performance after performance testing. it is correct it is correct and then it is correct and then we have done it is correct and then we have done that it is correct and then we have done that process it is correct and then we have done that process that accuracy it is correct and then we have done that process that accuracy test it is correct and then we have done that process that accuracy test and everything it is correct and then we have done that process that accuracy test and everything like how many images it is correct and then we have done that process that accuracy test and everything like how many images are we it is correct and then we have done that process that accuracy test and everything like how many images are we processing It is correct. And then we have done that process, that accuracy test and everything, like how many images are we processing? the four hundred imag the four hundred images the four hundred images with the less the four hundred images with the less quality The 400 images with the less quality. and then and then the same four hundred and then the same four hundred ms and then the same four hundred ms with high and then the same four hundred ms with high quality and then the same four hundred ms with high quality so and then the same four hundred ms with high quality so there is and then the same four hundred ms with high quality so there is a difference and then the same four hundred ms with high quality so there is a difference like that And then the same 400 ms with high quality. So there is a difference like that. we have calcul we have calculated with we have calculated with f and square we have calculated with f and square and everything we have calculated with f and square and everything so we have calculated with f and square and everything so i like that we have calculated with f and square and everything so i like that we have We have calculated with f and square and everything, so I like that we have. okay Okay. so so for how many so for how many years so for how many years you have been so for how many years you have been working with So for how many years you have been working with. ten tensorflo tensorflow tensorflow krs and tensorflow krs and py tensorflow krs and python Tensorflow, krs and python. like like three like three projects like three projects i have done like three projects i have done three like three projects i have done three years Like, three projects I have done three years. three three years Three years. okay okay and okay and you've done the okay and you've done the ibm okay and you've done the ibm cert okay and you've done the ibm certification Okay. And you've done the IBM certification. yes yes well yes well i'm doing yes well i'm doing how much time yes well i'm doing how much time it took yes well i'm doing how much time it took you to yes well i'm doing how much time it took you to get yes well i'm doing how much time it took you to get your ibm yes well i'm doing how much time it took you to get your ibm cert yes well i'm doing how much time it took you to get your ibm certification Yes, well, I'm doing how much time it took you to get your IBM certification. like like forty like forty days Like, 40 days. in in the beginning in the beginning days i have done in the beginning days i have done actually in the beginning days i have done actually i in the beginning days i have done actually i required in the beginning days i have done actually i required that in the beginning days i have done actually i required that certification In the beginning days, I have done. Actually, I required that certification. what is that what is that in the what is that in the beginning what is that in the beginning days what is that in the beginning days of my care what is that in the beginning days of my career actually what is that in the beginning days of my career actually i have done what is that in the beginning days of my career actually i have done that cert what is that in the beginning days of my career actually i have done that certificate what is that in the beginning days of my career actually i have done that certificate after what is that in the beginning days of my career actually i have done that certificate after that What is that? In the beginning days of my career, actually I have done that certificate after that. okay okay where did you okay where did you learn the okay where did you learn the statistics
2024-08-26 18:19:30.291915: Okay. Where did you learn the statistics? i have done i have done myself i have done myself only i have done myself only like i have done myself only like in the beginning I have done myself only like in the beginning. after after first after first three year after first three year comput after first three year computation after first three year computation i've after first three year computation i've done after first three year computation i've done this cert after first three year computation i've done this certification After first three year computation, I've done this certification. in the beginning in the beginning days in the beginning days i worked in the beginning days i worked as a in the beginning days i worked as a cloud in the beginning days i worked as a cloud admin in the beginning days i worked as a cloud admin after in the beginning days i worked as a cloud admin after that i in the beginning days i worked as a cloud admin after that i had got a chance in the beginning days i worked as a cloud admin after that i had got a chance to in the beginning days i worked as a cloud admin after that i had got a chance to move to in the beginning days i worked as a cloud admin after that i had got a chance to move to data anal in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics path in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics path and in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics path and then In the beginning days I worked as a cloud admin. After that I had got a chance. To move to data analytics path and then. this this data science this data science so this data science so in that time this data science so in that time i this data science so in that time i required that this data science so in that time i required that certific this data science so in that time i required that certificate so this data science so in that time i required that certificate so that this data science so in that time i required that certificate so that's why i this data science so in that time i required that certificate so that's why i have done This data science. So in that time, I required that certificate. So that's why I have done. i work i work with pandas i work with pandas number right i work with pandas number right yes i work with pandas number right yes i have done I work with pandas number right? Yes, I have done. how how long How long? every mission every mission learning every mission learning technique Every mission, learning technique. four four years four years plus four years plus because four years plus because everywhere four years plus because everywhere i have used four years plus because everywhere i have used it four years plus because everywhere i have used it actually Four years plus. Because everywhere I have used it, actually.
2024-08-26 18:19:30.296105: Okay. Where did you learn the statistics? i have done i have done myself i have done myself only i have done myself only like i have done myself only like in the beginning I have done myself only like in the beginning. after after first after first three year after first three year comput after first three year computation after first three year computation i've after first three year computation i've done after first three year computation i've done this cert after first three year computation i've done this certification After first three year computation, I've done this certification. in the beginning in the beginning days in the beginning days i worked in the beginning days i worked as a in the beginning days i worked as a cloud in the beginning days i worked as a cloud admin in the beginning days i worked as a cloud admin after in the beginning days i worked as a cloud admin after that i in the beginning days i worked as a cloud admin after that i had got a chance in the beginning days i worked as a cloud admin after that i had got a chance to in the beginning days i worked as a cloud admin after that i had got a chance to move to in the beginning days i worked as a cloud admin after that i had got a chance to move to data anal in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics path in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics path and in the beginning days i worked as a cloud admin after that i had got a chance to move to data analytics path and then In the beginning days I worked as a cloud admin. After that I had got a chance. To move to data analytics path and then. this this data science this data science so this data science so in that time this data science so in that time i this data science so in that time i required that this data science so in that time i required that certific this data science so in that time i required that certificate so this data science so in that time i required that certificate so that this data science so in that time i required that certificate so that's why i this data science so in that time i required that certificate so that's why i have done This data science. So in that time, I required that certificate. So that's why I have done. i work i work with pandas i work with pandas number right i work with pandas number right yes i work with pandas number right yes i have done I work with pandas number right? Yes, I have done. how how long How long? every mission every mission learning every mission learning technique Every mission, learning technique. four four years four years plus four years plus because four years plus because everywhere four years plus because everywhere i have used four years plus because everywhere i have used it four years plus because everywhere i have used it actually Four years plus. Because everywhere I have used it, actually.
2024-08-26 18:19:46.604290: okay Okay. now Now. architect architecture Architecture. and and advantages and advantages of tenso and advantages of tensorflo and advantages of tensorflow And advantages of tensorflow. advantage advantage of ten advantage of tensor advantage of tensorflo
2024-08-26 18:19:46.605652: okay Okay. now Now. architect architecture Architecture. and and advantages and advantages of tenso and advantages of tensorflo and advantages of tensorflow And advantages of tensorflow. advantage advantage of ten advantage of tensor advantage of tensorflo
2024-08-26 18:21:33.127227: advantage of tensorflow Advantage of tensorflow. what what does it do what does it do create what does it do create tensor What does it do? Create tensor. et cetera Et cetera. mostly Mostly. it's open it's open doors it's open doors and then it's open doors and then it had it's open doors and then it had like flexibility it's open doors and then it had like flexibility to it's open doors and then it had like flexibility to dep it's open doors and then it had like flexibility to deploy the mach it's open doors and then it had like flexibility to deploy the machine learning it's open doors and then it had like flexibility to deploy the machine learning models it's open doors and then it had like flexibility to deploy the machine learning models exc it's open doors and then it had like flexibility to deploy the machine learning models excaliburability it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data set it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used tensorflow it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used tensorflow had it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used tensorflow had kind of It's open doors. And then it had, like, flexibility to deploy the machine learning models. Excaliburability for large data sets. Mostly, that's why we have used tensorflow. Had kind of. coordinate coordinate flow Coordinate flow. we have we have the we have the part of tensor we have the part of tensorflow is we have the part of tensorflow is the cance we have the part of tensorflow is the cancer of gra we have the part of tensorflow is the cancer of graph based we have the part of tensorflow is the cancer of graph based architect we have the part of tensorflow is the cancer of graph based architecture like we have the part of tensorflow is the cancer of graph based architecture like oper we have the part of tensorflow is the cancer of graph based architecture like operations we have the part of tensorflow is the cancer of graph based architecture like operations are we have the part of tensorflow is the cancer of graph based architecture like operations are described by we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where node we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations as we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations as the edges we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations as the edges represents We have the part of Tensorflow is the cancer of graph based architecture like operations are described. By a graph where nodes represent the operations as the edges represents. like multid like multidimension like multidimensional ar like multidimensional array of like multidimensional array of data like multidimensional array of data that like multidimensional array of data that we can go ahead Like multidimensional array of data that we can go ahead. like like this graph like this graph kind of like this graph kind of go like this graph kind of go with the comput like this graph kind of go with the computation like this graph kind of go with the computation across like this graph kind of go with the computation across like different like this graph kind of go with the computation across like different hardware like this graph kind of go with the computation across like different hardware like including like this graph kind of go with the computation across like different hardware like including cp like this graph kind of go with the computation across like different hardware like including cpu like this graph kind of go with the computation across like different hardware like including cpu cpus like this graph kind of go with the computation across like different hardware like including cpu cpus that like this graph kind of go with the computation across like different hardware like including cpu cpus that's where like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used in like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used in the tensorflow like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used in the tensorflow we have Like, this graph kind of go with the computation across, like, different hardware, like including CPU Cpus. That's where we have used in the tensorflow. We have. tfx also tfx also like tfx also like in terms of tfx also like in terms of extend tfx also like in terms of extended like tfx also like in terms of extended like a platform tfx also like in terms of extended like a platform that tfx also like in terms of extended like a platform that will deploy to tfx also like in terms of extended like a platform that will deploy to production tfx also like in terms of extended like a platform that will deploy to production ml p tfx also like in terms of extended like a platform that will deploy to production ml pipelines tfx also like in terms of extended like a platform that will deploy to production ml pipelines including TFX also like in terms of extended like a platform that will deploy to production ML pipelines, including. that that components that components like that components like tensorf that components like tensorflow that components like tensorflow tensorf that components like tensorflow tensorflow from that components like tensorflow tensorflow from data that components like tensorflow tensorflow from data process that components like tensorflow tensorflow from data processing that components like tensorflow tensorflow from data processing val that components like tensorflow tensorflow from data processing validation that components like tensorflow tensorflow from data processing validation data that components like tensorflow tensorflow from data processing validation data analysis that components like tensorflow tensorflow from data processing validation data analysis also can that components like tensorflow tensorflow from data processing validation data analysis also can do that components like tensorflow tensorflow from data processing validation data analysis also can do that that components like tensorflow tensorflow from data processing validation data analysis also can do that tensor that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tenso that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embed that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded dev that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also can do that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also can do in terms that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also can do in terms of That components like Tensorflow, tensorflow from data processing, validation, data analysis also can do that. Tensorflow Hub is there Tensorflow light also is there like to optimize the mobile and embedded devices, those kind of things also can do in terms of. servicing servicing also is there servicing also is there like servicing also is there like design servicing also is there like design to servicing also is there like design to like Servicing. Also, is there, like, design to, like? when i'm when i'm going with when i'm going with action to dep when i'm going with action to deployment when i'm going with action to deployment of models like when i'm going with action to deployment of models like manag when i'm going with action to deployment of models like managing When I'm going with action to deployment of models like managing. the the versions the versions and scalability the versions and scalability in that case the versions and scalability in that case we go with the versions and scalability in that case we go with that The versions and scalability. In that case, we go with that. no No. now Now. we work with we work with the dock we work with the docker and we work with the docker and kubernetes we work with the docker and kubernetes right we work with the docker and kubernetes right yes We work with the Docker and Kubernetes, right? Yes. how long how long you how long you work how long you work with How long you work with. docker docker and k docker and kubernetes docker and kubernetes i docker and kubernetes i worked for docker and kubernetes i worked for the last two docker and kubernetes i worked for the last two project docker and kubernetes i worked for the last two projects Docker and Kubernetes. I worked for the last two projects. i mean i mean pre i mean previously I mean, previously. i'm not aware i'm not aware of i'm not aware of this i'm not aware of this aks actually i'm not aware of this aks actually az i'm not aware of this aks actually azure
2024-08-26 18:21:33.128282: advantage of tensorflow Advantage of tensorflow. what what does it do what does it do create what does it do create tensor What does it do? Create tensor. et cetera Et cetera. mostly Mostly. it's open it's open doors it's open doors and then it's open doors and then it had it's open doors and then it had like flexibility it's open doors and then it had like flexibility to it's open doors and then it had like flexibility to dep it's open doors and then it had like flexibility to deploy the mach it's open doors and then it had like flexibility to deploy the machine learning it's open doors and then it had like flexibility to deploy the machine learning models it's open doors and then it had like flexibility to deploy the machine learning models exc it's open doors and then it had like flexibility to deploy the machine learning models excaliburability it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data set it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used tensorflow it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used tensorflow had it's open doors and then it had like flexibility to deploy the machine learning models excaliburability for large data sets mostly that's why we have used tensorflow had kind of It's open doors. And then it had, like, flexibility to deploy the machine learning models. Excaliburability for large data sets. Mostly, that's why we have used tensorflow. Had kind of. coordinate coordinate flow Coordinate flow. we have we have the we have the part of tensor we have the part of tensorflow is we have the part of tensorflow is the cance we have the part of tensorflow is the cancer of gra we have the part of tensorflow is the cancer of graph based we have the part of tensorflow is the cancer of graph based architect we have the part of tensorflow is the cancer of graph based architecture like we have the part of tensorflow is the cancer of graph based architecture like oper we have the part of tensorflow is the cancer of graph based architecture like operations we have the part of tensorflow is the cancer of graph based architecture like operations are we have the part of tensorflow is the cancer of graph based architecture like operations are described by we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where node we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations as we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations as the edges we have the part of tensorflow is the cancer of graph based architecture like operations are described by a graph where nodes represent the operations as the edges represents We have the part of Tensorflow is the cancer of graph based architecture like operations are described. By a graph where nodes represent the operations as the edges represents. like multid like multidimension like multidimensional ar like multidimensional array of like multidimensional array of data like multidimensional array of data that like multidimensional array of data that we can go ahead Like multidimensional array of data that we can go ahead. like like this graph like this graph kind of like this graph kind of go like this graph kind of go with the comput like this graph kind of go with the computation like this graph kind of go with the computation across like this graph kind of go with the computation across like different like this graph kind of go with the computation across like different hardware like this graph kind of go with the computation across like different hardware like including like this graph kind of go with the computation across like different hardware like including cp like this graph kind of go with the computation across like different hardware like including cpu like this graph kind of go with the computation across like different hardware like including cpu cpus like this graph kind of go with the computation across like different hardware like including cpu cpus that like this graph kind of go with the computation across like different hardware like including cpu cpus that's where like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used in like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used in the tensorflow like this graph kind of go with the computation across like different hardware like including cpu cpus that's where we have used in the tensorflow we have Like, this graph kind of go with the computation across, like, different hardware, like including CPU Cpus. That's where we have used in the tensorflow. We have. tfx also tfx also like tfx also like in terms of tfx also like in terms of extend tfx also like in terms of extended like tfx also like in terms of extended like a platform tfx also like in terms of extended like a platform that tfx also like in terms of extended like a platform that will deploy to tfx also like in terms of extended like a platform that will deploy to production tfx also like in terms of extended like a platform that will deploy to production ml p tfx also like in terms of extended like a platform that will deploy to production ml pipelines tfx also like in terms of extended like a platform that will deploy to production ml pipelines including TFX also like in terms of extended like a platform that will deploy to production ML pipelines, including. that that components that components like that components like tensorf that components like tensorflow that components like tensorflow tensorf that components like tensorflow tensorflow from that components like tensorflow tensorflow from data that components like tensorflow tensorflow from data process that components like tensorflow tensorflow from data processing that components like tensorflow tensorflow from data processing val that components like tensorflow tensorflow from data processing validation that components like tensorflow tensorflow from data processing validation data that components like tensorflow tensorflow from data processing validation data analysis that components like tensorflow tensorflow from data processing validation data analysis also can that components like tensorflow tensorflow from data processing validation data analysis also can do that components like tensorflow tensorflow from data processing validation data analysis also can do that that components like tensorflow tensorflow from data processing validation data analysis also can do that tensor that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tenso that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embed that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded dev that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also can do that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also can do in terms that components like tensorflow tensorflow from data processing validation data analysis also can do that tensorflow hub is there tensorflow light also is there like to optimize the mobile and embedded devices those kind of things also can do in terms of That components like Tensorflow, tensorflow from data processing, validation, data analysis also can do that. Tensorflow Hub is there Tensorflow light also is there like to optimize the mobile and embedded devices, those kind of things also can do in terms of. servicing servicing also is there servicing also is there like servicing also is there like design servicing also is there like design to servicing also is there like design to like Servicing. Also, is there, like, design to, like? when i'm when i'm going with when i'm going with action to dep when i'm going with action to deployment when i'm going with action to deployment of models like when i'm going with action to deployment of models like manag when i'm going with action to deployment of models like managing When I'm going with action to deployment of models like managing. the the versions the versions and scalability the versions and scalability in that case the versions and scalability in that case we go with the versions and scalability in that case we go with that The versions and scalability. In that case, we go with that. no No. now Now. we work with we work with the dock we work with the docker and we work with the docker and kubernetes we work with the docker and kubernetes right we work with the docker and kubernetes right yes We work with the Docker and Kubernetes, right? Yes. how long how long you how long you work how long you work with How long you work with. docker docker and k docker and kubernetes docker and kubernetes i docker and kubernetes i worked for docker and kubernetes i worked for the last two docker and kubernetes i worked for the last two project docker and kubernetes i worked for the last two projects Docker and Kubernetes. I worked for the last two projects. i mean i mean pre i mean previously I mean, previously. i'm not aware i'm not aware of i'm not aware of this i'm not aware of this aks actually i'm not aware of this aks actually az i'm not aware of this aks actually azure
2024-08-26 18:23:01.347362: i'm not aware of this aks actually azure kube i'm not aware of this aks actually azure kubernetes i'm not aware of this aks actually azure kubernetes part i'm not aware of this aks actually azure kubernetes part so i'm not aware of this aks actually azure kubernetes part so whenever i i'm not aware of this aks actually azure kubernetes part so whenever i use this i'm not aware of this aks actually azure kubernetes part so whenever i use this azure I'm not aware of this aks actually azure kubernetes part so whenever I use this azure? have you have you deployed your own have you deployed your own model have you deployed your own models to have you deployed your own models to any have you deployed your own models to any yeah Have you deployed your own models to any? Yeah. i've done i've done the i've done the till staging i've done the till staging have done I've done the till staging have done. we have built we have built the we have built the develop we have built the development we have built the development qa We have built the development QA. and And. staging staging so staging so for those staging so for those things we have staging so for those things we have written the staging so for those things we have written the docker files staging so for those things we have written the docker files and then we have staging so for those things we have written the docker files and then we have written staging so for those things we have written the docker files and then we have written the jen staging so for those things we have written the docker files and then we have written the jenkins staging so for those things we have written the docker files and then we have written the jenkins files staging so for those things we have written the docker files and then we have written the jenkins files what to do staging so for those things we have written the docker files and then we have written the jenkins files what to do the staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files and staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files and everything staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files and everything and then Staging. So for those things, we have written the docker files, and then we have written the Jenkins files what to do the ACD part. And then we have written the Kubernetes. Files also, like the basic files and everything, and then. for for your for your current for your current project for your current project yes for your current project yes for the current for your current project yes for the current project For your current project? Yes, for the current project. and then And then. mostly mostly i mostly i worked with data mostly i worked with data engineer mostly i worked with data engineering part mostly i worked with data engineering part like mostly i worked with data engineering part like here actually mostly i worked with data engineering part like here actually the data Mostly I worked with data engineering part. Like here, actually. The data. we need to use we need to use the we need to use the pi sp we need to use the pi spark We need to use the PI Spark. to To. give the give the pi spark give the pi spark fil give the pi spark files coming give the pi spark files coming with these give the pi spark files coming with these files Give the PI spark files coming with these files. from from one sou from one source actually from one source actually it is like from one source actually it is like hadoop from one source actually it is like hadoop so from one source actually it is like hadoop so even from one source actually it is like hadoop so even i'm also from one source actually it is like hadoop so even i'm also very new from one source actually it is like hadoop so even i'm also very new to that from one source actually it is like hadoop so even i'm also very new to that and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the autom from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that most from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have written the from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have written the notes from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have written the notes and then From one source. Actually, it is like Hadoop. So even I'm also very new to that. And then I have learned it, and then I have done that part, and then we build. The pipelines and then we have done the automation part and everything till that. Mostly I work. With Azure databrick under time only. So we have written the notes and then after. that that this aks that this aksis came that this aksis came so the cz That this aksis came so the cz. bedrock bedrock yes bedrock yes aws bedrock yes aws in the beginning bedrock yes aws in the beginning days as i said Bedrock. Yes. Aws. In the beginning days, as I said. i'm from the aw i'm from the aws i'm from the aws cloud admin i'm from the aws cloud admin so that's why i'm from the aws cloud admin so that's why i have that i'm from the aws cloud admin so that's why i have that certification i'm from the aws cloud admin so that's why i have that certification also i'm from the aws cloud admin so that's why i have that certification also after i'm from the aws cloud admin so that's why i have that certification also after that i'm from the aws cloud admin so that's why i have that certification also after that aws i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock
2024-08-26 18:23:01.348588: i'm not aware of this aks actually azure kube i'm not aware of this aks actually azure kubernetes i'm not aware of this aks actually azure kubernetes part i'm not aware of this aks actually azure kubernetes part so i'm not aware of this aks actually azure kubernetes part so whenever i i'm not aware of this aks actually azure kubernetes part so whenever i use this i'm not aware of this aks actually azure kubernetes part so whenever i use this azure I'm not aware of this aks actually azure kubernetes part so whenever I use this azure? have you have you deployed your own have you deployed your own model have you deployed your own models to have you deployed your own models to any have you deployed your own models to any yeah Have you deployed your own models to any? Yeah. i've done i've done the i've done the till staging i've done the till staging have done I've done the till staging have done. we have built we have built the we have built the develop we have built the development we have built the development qa We have built the development QA. and And. staging staging so staging so for those staging so for those things we have staging so for those things we have written the staging so for those things we have written the docker files staging so for those things we have written the docker files and then we have staging so for those things we have written the docker files and then we have written staging so for those things we have written the docker files and then we have written the jen staging so for those things we have written the docker files and then we have written the jenkins staging so for those things we have written the docker files and then we have written the jenkins files staging so for those things we have written the docker files and then we have written the jenkins files what to do staging so for those things we have written the docker files and then we have written the jenkins files what to do the staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files and staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files and everything staging so for those things we have written the docker files and then we have written the jenkins files what to do the acd part and then we have written the kubernetes files also like the basic files and everything and then Staging. So for those things, we have written the docker files, and then we have written the Jenkins files what to do the ACD part. And then we have written the Kubernetes. Files also, like the basic files and everything, and then. for for your for your current for your current project for your current project yes for your current project yes for the current for your current project yes for the current project For your current project? Yes, for the current project. and then And then. mostly mostly i mostly i worked with data mostly i worked with data engineer mostly i worked with data engineering part mostly i worked with data engineering part like mostly i worked with data engineering part like here actually mostly i worked with data engineering part like here actually the data Mostly I worked with data engineering part. Like here, actually. The data. we need to use we need to use the we need to use the pi sp we need to use the pi spark We need to use the PI Spark. to To. give the give the pi spark give the pi spark fil give the pi spark files coming give the pi spark files coming with these give the pi spark files coming with these files Give the PI spark files coming with these files. from from one sou from one source actually from one source actually it is like from one source actually it is like hadoop from one source actually it is like hadoop so from one source actually it is like hadoop so even from one source actually it is like hadoop so even i'm also from one source actually it is like hadoop so even i'm also very new from one source actually it is like hadoop so even i'm also very new to that from one source actually it is like hadoop so even i'm also very new to that and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the autom from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that most from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have written the from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have written the notes from one source actually it is like hadoop so even i'm also very new to that and then i have learned it and then i have done that part and then we build the pipelines and then we have done the automation part and everything till that mostly i work with azure databrick under time only so we have written the notes and then From one source. Actually, it is like Hadoop. So even I'm also very new to that. And then I have learned it, and then I have done that part, and then we build. The pipelines and then we have done the automation part and everything till that. Mostly I work. With Azure databrick under time only. So we have written the notes and then after. that that this aks that this aksis came that this aksis came so the cz That this aksis came so the cz. bedrock bedrock yes bedrock yes aws bedrock yes aws in the beginning bedrock yes aws in the beginning days as i said Bedrock. Yes. Aws. In the beginning days, as I said. i'm from the aw i'm from the aws i'm from the aws cloud admin i'm from the aws cloud admin so that's why i'm from the aws cloud admin so that's why i have that i'm from the aws cloud admin so that's why i have that certification i'm from the aws cloud admin so that's why i have that certification also i'm from the aws cloud admin so that's why i have that certification also after i'm from the aws cloud admin so that's why i have that certification also after that i'm from the aws cloud admin so that's why i have that certification also after that aws i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock
2024-08-26 18:23:22.971206: i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm sorry i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm sorry aw i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm sorry aws I'm from the AWS cloud admin, so that's why I have that. Certification. Also after that, AWS bedrock. I'm sorry. Aws? bed bedrock Bedrock. sorry sorry your sorry your voice sorry your voice is Sorry. Your voice is. there there is some problem there is some problem here there is some problem here okay There is some problem here, okay? i'm talking about i'm talking about aw i'm talking about aws i'm talking about aws bed i'm talking about aws bedrock i'm talking about aws bedrock b i'm talking about aws bedrock b d r i'm talking about aws bedrock b d r o c k i'm talking about aws bedrock b d r o c k yes I'm talking about aws. Bedrock. B d r o c k. Yes.
2024-08-26 18:23:22.971206: i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm sorry i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm sorry aw i'm from the aws cloud admin so that's why i have that certification also after that aws bedrock i'm sorry aws I'm from the AWS cloud admin, so that's why I have that. Certification. Also after that, AWS bedrock. I'm sorry. Aws? bed bedrock Bedrock. sorry sorry your sorry your voice sorry your voice is Sorry. Your voice is. there there is some problem there is some problem here there is some problem here okay There is some problem here, okay? i'm talking about i'm talking about aw i'm talking about aws i'm talking about aws bed i'm talking about aws bedrock i'm talking about aws bedrock b i'm talking about aws bedrock b d r i'm talking about aws bedrock b d r o c k i'm talking about aws bedrock b d r o c k yes I'm talking about aws. Bedrock. B d r o c k. Yes.
