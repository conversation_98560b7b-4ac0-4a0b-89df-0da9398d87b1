import time
import pyperclip
import pyautogui
import keyboard
import threading
import random
import string
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

typing = False
paused = True
stop = False
copied_text = ""
waiting_for_permission = False
typing_completed = False
monitoring_enabled = True  # New variable to control monitoring

def on_insert_key():
  global paused, typing, copied_text, waiting_for_permission, typing_completed
  if not typing and copied_text and not typing_completed:
      typing = True
      paused = False
      waiting_for_permission = False
      threading.Thread(target=type_text, args=(copied_text,)).start()
  elif typing:
      paused = not paused
      if not paused:
          waiting_for_permission = False

def on_delete_key():
  global stop, typing
  if typing:
      stop = True
      typing = False

def clean_indentation(text):
  lines = text.split('\n')
  cleaned_lines = [line.strip() for line in lines]
  return '\n'.join(cleaned_lines)

def monitor_clipboard():
  global copied_text, typing_completed
  last_text = ""
  while True:
      if monitoring_enabled:  # Check if monitoring is enabled
          try:
              current_text = pyperclip.paste()
              if current_text != last_text and current_text.strip():
                  last_text = current_text
                  copied_text = clean_indentation(current_text)
                  typing_completed = False  # Reset the flag when new text is copied
                  logging.info(f"New text copied: {copied_text}")
          except Exception as e:
              logging.warning(f"Clipboard access error: {e}")
      time.sleep(1)  # Increase sleep time to reduce frequency

def get_variable_delay():
    base_delay = random.uniform(0.25, 0.35)
    return base_delay * random.uniform(0.9, 1.1)

def simulate_hand_movement():
  if random.random() < 0.05:  # 5% chance to simulate hand movement
      hand_move_time = random.uniform(0.2, 0.5)
      time.sleep(hand_move_time)

def simulate_thinking():
  if random.random() < 0.1:
      think_time = random.uniform(0.5, 2)
      time.sleep(think_time)

def make_typo(char):
  if random.random() < 0.06:  # 6% chance of typo
      if char in string.punctuation:
          return random.choice(string.punctuation), True
      elif char.isalpha():
          return random.choice(string.ascii_letters), True
      elif char.isdigit():
          return random.choice(string.digits), True
  return char, False

def correct_typo(char):
  # Always correct typos
  time.sleep(random.uniform(0.5, 1.0))  # Pause before correction
  pyautogui.press('backspace')
  time.sleep(get_variable_delay())
  pyautogui.typewrite(char)
  time.sleep(get_variable_delay())

def simulate_realistic_pause():
  if random.random() < 0.1:  # سنة% chance to pause after a sentence
      pause_time = random.uniform(1, 3)
      time.sleep(pause_time)

def type_text(text):
  global paused, stop, typing, waiting_for_permission, typing_completed
  try:
      lines = text.splitlines()
      for line in lines:
          if stop:
              break

          waiting_for_permission = True
          while waiting_for_permission and not stop:
              time.sleep(0.1)
          
          if stop:
              break
          
          simulate_thinking()
          
          line_length = len(line)
          for index, char in enumerate(line):
              if stop:
                  break
              if paused:
                  while paused and not stop:
                      time.sleep(0.1)
              
              char_to_type, is_typo = make_typo(char)
              
              pyautogui.typewrite(char_to_type)
              
              time.sleep(get_variable_delay())
              
              if is_typo:
                  correct_typo(char)
              
              simulate_hand_movement()
          
          pyautogui.press('enter')
          time.sleep(get_variable_delay())
          simulate_realistic_pause()
      
      typing_completed = True  # Mark typing as completed
      copied_text = ""  # Clear the copied text to prevent re-typing

  except Exception as e:
      logging.error(f"Error during typing: {e}")
  finally:
      typing = False
      stop = False

def toggle_monitoring():
  global monitoring_enabled
  monitoring_enabled = not monitoring_enabled
  logging.info(f"Clipboard monitoring {'enabled' if monitoring_enabled else 'disabled'}.")

def main():
  try:
      
      # Ensure these key bindings do not conflict with system shortcuts
      keyboard.on_press_key("insert", lambda _: on_insert_key())
      keyboard.on_press_key("delete", lambda _: on_delete_key())
      keyboard.on_press_key("f10", lambda _: toggle_monitoring())  # Add a key to toggle monitoring

      clipboard_thread = threading.Thread(target=monitor_clipboard, daemon=True)
      clipboard_thread.start()

      while True:
          time.sleep(1)
  except KeyboardInterrupt:
      pass
  except Exception as e:
      logging.error(f"Error during typing: {e}")

if __name__ == "__main__":
  main()