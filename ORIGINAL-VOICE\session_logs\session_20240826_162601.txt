2024-08-26 16:27:23.834354: can you can you explain can you explain how can you explain how did you can you explain how did you handle Can you explain? How did you handle. fore forecasting Forecasting. and and any recommend and any recommendation system and any recommendation system with and any recommendation system with arima And any recommendation system with Arima. and and time and time series and time series analysis And time series analysis.
2024-08-26 16:27:23.841891: can you can you explain can you explain how can you explain how did you can you explain how did you handle Can you explain? How did you handle. fore forecasting Forecasting. and and any recommend and any recommendation system and any recommendation system with and any recommendation system with arima And any recommendation system with Arima. and and time and time series and time series analysis And time series analysis.
2024-08-26 16:27:58.075368: you were done You were done. can you can you explain can you explain in which project Can you explain in which project? you have you have done you have done this time ser you have done this time series anal you have done this time series analysis
2024-08-26 16:27:58.075368: you were done You were done. can you can you explain can you explain in which project Can you explain in which project? you have you have done you have done this time ser you have done this time series anal you have done this time series analysis
2024-08-26 16:28:46.074296: You have done this time series analysis. in in which project in which project you in which project you have used in which project you have used around in which project you have used around deduction and in which project you have used around deduction and how in which project you have used around deduction and how it in which project you have used around deduction and how it's useful In which project you have used around deduction and how it's useful.
2024-08-26 16:28:46.075816: You have done this time series analysis. in in which project in which project you in which project you have used in which project you have used around in which project you have used around deduction and in which project you have used around deduction and how in which project you have used around deduction and how it in which project you have used around deduction and how it's useful In which project you have used around deduction and how it's useful.
2024-08-26 16:40:41.888959: your teammate your teammate ten o'clock your teammate ten o'clock by your teammate ten o'clock by twelve Your teammate. 10:00 by twelve. yeah hi yeah hi kasik Yeah. Hi, Kasik. kasikam Kasikam. is joining is joining in a minute is joining in a minute just is joining in a minute just stay on is joining in a minute just stay on the line is joining in a minute just stay on the line yeah sure Is joining in a minute. Just stay on the line. Yeah, sure. yeah sure Yeah, sure. hi hi hash Hi, hash. can can turn on can turn on your camera can turn on your camera as well can turn on your camera as well yes can turn on your camera as well yes sure Can turn on your camera as well. Yes, sure. hi Hi. i'll assume i'll assume that i'll assume that you are a fair idea i'll assume that you are a fair idea about i'll assume that you are a fair idea about the job i'll assume that you are a fair idea about the job role I'll assume that you are a fair idea about the job role. team Team. any questions you have any questions you have before any questions you have before we start Any questions you have before we start. why don't why don't you tell me why don't you tell me something about why don't you tell me something about yourself why don't you tell me something about yourself okay why don't you tell me something about yourself okay fine Why don't you tell me something about yourself? Okay, fine. i have i have total eight point i have total eight point one years i have total eight point one years of experience i have total eight point one years of experience in i have total eight point one years of experience in it and then i have total eight point one years of experience in it and then the relevant i have total eight point one years of experience in it and then the relevant experience i have total eight point one years of experience in it and then the relevant experience with the i have total eight point one years of experience in it and then the relevant experience with the data science i have total eight point one years of experience in it and then the relevant experience with the data science and am i have total eight point one years of experience in it and then the relevant experience with the data science and aml i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around four point i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around four point one i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around four point one years I have total 8.1 years of experience in it, and then the relevant experience with the data science and Aml. It's come around 4.1 years. i work with i work with mostly with i work with mostly with python i work with mostly with python and i work with mostly with python and work with i work with mostly with python and work with libr i work with mostly with python and work with libraries like num i work with mostly with python and work with libraries like numpy pan i work with mostly with python and work with libraries like numpy panel sci f i work with mostly with python and work with libraries like numpy panel sci fi nlt i work with mostly with python and work with libraries like numpy panel sci fi nltk i work with mostly with python and work with libraries like numpy panel sci fi nltk tenso i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow space i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i worked with the i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i worked with the map I work with, mostly with python and work with libraries like numpy panel sci-fi nltk, tensorflow, Spacey cycle and those kind of things. I woke down for data. I worked with the map. probably probably power bi probably power bi students probably power bi students i worked probably power bi students i worked on probably power bi students i worked on i'm experience probably power bi students i worked on i'm experienced in probably power bi students i worked on i'm experienced in psychoanalys probably power bi students i worked on i'm experienced in psychoanalysis like probably power bi students i worked on i'm experienced in psychoanalysis like ingredient probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy testing probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy testing probably probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy testing probably the Probably power BI students I worked on. I'm experienced in psychoanalysis like ingredient hypocrisy. Testing probably the. regression regression analysis regression analysis and regression analysis and discussment regression analysis and discussment and time regression analysis and discussment and time series and regression analysis and discussment and time series and all this Regression analysis and discussment and time series and all this. that's why i was That's why I was. running machine running machine learning Running machine learning. i worked i worked on project i worked on projects I worked on projects. like like regress like regression class like regression classification like regression classification cl like regression classification clustering like regression classification clustering and recommend like regression classification clustering and recommendation system like regression classification clustering and recommendation systems like regression classification clustering and recommendation systems and like regression classification clustering and recommendation systems and nsm method like regression classification clustering and recommendation systems and nsm methods was Like regression classification, clustering and recommendation systems and NSM methods was. i i worked with i worked with deep learning i worked with deep learning ml i worked with deep learning mlp framework i worked with deep learning mlp frameworks as well i worked with deep learning mlp frameworks as well like i worked with deep learning mlp frameworks as well like tensorflow i worked with deep learning mlp frameworks as well like tensorflow keras i worked with deep learning mlp frameworks as well like tensorflow keras space i worked with deep learning mlp frameworks as well like tensorflow keras spacey i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv in i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv in the i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv in the setting I worked with deep learning MLP frameworks as well, like Tensorflow, Keras Spacey Bird and Python CV in the setting. i i worked with i worked with a few i worked with a few api i worked with a few api development i worked with a few api development part i worked with a few api development part as well I worked with a few API development part as well. and and i and i worked on and i worked on a database and i worked on a database like and i worked on a database like mys and i worked on a database like mysql and i worked on a database like mysql and sql serv and i worked on a database like mysql and sql servers and i worked on a database like mysql and sql servers and and i worked on a database like mysql and sql servers and those kind of and i worked on a database like mysql and sql servers and those kind of invoice skill and i worked on a database like mysql and sql servers and those kind of invoice skill i worked and i worked on a database like mysql and sql servers and those kind of invoice skill i worked only for and i worked on a database like mysql and sql servers and those kind of invoice skill i worked only for cassand and i worked on a database like mysql and sql servers and those kind of invoice skill i worked only for cassandra and And I worked on a database like MySQL and SQL servers and those kind of invoice skill. I worked only for Cassandra and. the mamod the mamodb the mamodb ones have the mamodb ones have worked the mamodb ones have worked downloads the mamodb ones have worked downloads on one data the mamodb ones have worked downloads on one database The mamodb ones have worked. Downloads on one database. transaction transaction analysis Transaction analysis. and ap and apart from that And apart from that. i might i might consider i might consider my previous i might consider my previous project I might consider my previous project. what we have what we have built is like what we have built is like to develop what we have built is like to develop a system what we have built is like to develop a system that what we have built is like to develop a system that could what we have built is like to develop a system that could accurately what we have built is like to develop a system that could accurately identify what we have built is like to develop a system that could accurately identify and what we have built is like to develop a system that could accurately identify and separate what we have built is like to develop a system that could accurately identify and separate the what we have built is like to develop a system that could accurately identify and separate the different what we have built is like to develop a system that could accurately identify and separate the different plastic what we have built is like to develop a system that could accurately identify and separate the different plastic types actually what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contam what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects in what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects in that we have what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects in that we have to do the What we have built is like to develop a system that could accurately identify and separate the different plastic types, actually. So when they are mixed together and also identified chemical contamination, plastic in objects in that we have to do the. features features and features and pattern features and patterns and features and patterns and we have features and patterns and we have to distinguish features and patterns and we have to distinguish each plast features and patterns and we have to distinguish each plastic type features and patterns and we have to distinguish each plastic type those kind of things features and patterns and we have to distinguish each plastic type those kind of things we features and patterns and we have to distinguish each plastic type those kind of things we need to do Features and patterns, and we have to distinguish each plastic type, those kind of things. We need to do. it will it will lead it will lead to it will lead to the more effic it will lead to the more efficient recyc it will lead to the more efficient recycling it will lead to the more efficient recycling and wast it will lead to the more efficient recycling and waste management it will lead to the more efficient recycling and waste management and it will lead to the more efficient recycling and waste management and deceased the operational it will lead to the more efficient recycling and waste management and deceased the operational cost it will lead to the more efficient recycling and waste management and deceased the operational cost and it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it is it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it is so here it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it is so here totally It will lead to the more efficient recycling and waste management and deceased the operational cost. And while doing this artist again. That's the thing, mainly. Here it is. So here. Totally. we have We have. eight type eight types of eight types of plast eight types of plastics Eight types of plastics. some some we need to ident some we need to identify some we need to identify in chem some we need to identify in chemical contam some we need to identify in chemical contamination also some we need to identify in chemical contamination also based some we need to identify in chemical contamination also based on Some we need to identify in chemical contamination. Also, based on. the video the video actually the video actually like in the video actually like in this video The video actually, like in this video. we need to extra we need to extract we need to extract the images we need to extract the images from there we need to extract the images from there by using we need to extract the images from there by using the open we need to extract the images from there by using the openc we need to extract the images from there by using the opencv we need to extract the images from there by using the opencv and we need to extract the images from there by using the opencv and instead we need to extract the images from there by using the opencv and instead of we need to extract the images from there by using the opencv and instead of taking the we need to extract the images from there by using the opencv and instead of taking the random we need to extract the images from there by using the opencv and instead of taking the random pictures we need to extract the images from there by using the opencv and instead of taking the random pictures we have used we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detect we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algor we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the opt we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical fl we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the open we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and twenty we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and twenty five frames and we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and twenty five frames and it We need to extract the images from there by using the OpenCV. And instead of taking the random pictures. We have used kind of motion detection algorithm like we need to in the background. We need to subtract the optical flow and whatever the availability from the OpenCV. And 25 frames, and it will give us. like Like. more more any kind of more any kind of motion object more any kind of motion objects More any kind of motion, objects. based based on frame based on frame scoring also based on frame scoring also we have Based on frame scoring. Also, we have. implemented implemented like to elev implemented like to elevate the implemented like to elevate the factors implemented like to elevate the factors like implemented like to elevate the factors like kind of implemented like to elevate the factors like kind of object implemented like to elevate the factors like kind of object clar implemented like to elevate the factors like kind of object clarity and implemented like to elevate the factors like kind of object clarity and overlap Implemented, like, to elevate the factors like kind of object clarity and overlap. and And. kind of kind of coverage kind of coverage and kind of coverage and based on kind of coverage and based on the prioritized kind of coverage and based on the prioritized frames Kind of coverage. And based on the prioritized frames, we need to we need to extract we need to extract the we need to extract the images we need to extract the images after we need to extract the images after that We need to extract the images after that. imag image extraction image extraction we have done image extraction we have done that image extraction we have done that like image extraction we have done that like a classification image extraction we have done that like a classification we image extraction we have done that like a classification we need to classify image extraction we have done that like a classification we need to classify the imag image extraction we have done that like a classification we need to classify the images like image extraction we have done that like a classification we need to classify the images like after that image extraction we have done that like a classification we need to classify the images like after that we need image extraction we have done that like a classification we need to classify the images like after that we need to do the image extraction we have done that like a classification we need to classify the images like after that we need to do the detection image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rs image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsn image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsnn image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsnn this image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsnn this model Image extraction. We have done that. Like a classification. We need to classify the images, like after that. We need to do the detection model like mask, rsnn. This model. gives a gives a boundary box gives a boundary boxes gives a boundary boxes of each gives a boundary boxes of each pixel gives a boundary boxes of each pixel wise gives a boundary boxes of each pixel wise the marks gives a boundary boxes of each pixel wise the marks of gives a boundary boxes of each pixel wise the marks of each gives a boundary boxes of each pixel wise the marks of each image gives a boundary boxes of each pixel wise the marks of each image and Gives a boundary boxes of each pixel wise, the marks of each image, and. the the canvas the canvas vertically the canvas vertically within the the canvas vertically within the single frame the canvas vertically within the single frame it will the canvas vertically within the single frame it will have the canvas vertically within the single frame it will have like the canvas vertically within the single frame it will have like one hundred to one the canvas vertically within the single frame it will have like one hundred to one hundred and fifty the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to ident the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask r the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcn the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm al the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like most the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character labels and we have the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character labels and we have to do the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character labels and we have to do the The canvas vertically within the single frame, it will have like 100 to 150 objects are there, so. We need to identify each one app. So we require boundary boxes to give that. So that's. Why we have used the mask RCNM algorithm. I was involved like mostly it will character labels, and we have to do the. masking masking things and masking things and we have to do it masking things and we have to do it manually Masking things and we have to do it manually. in the beginning in the beginning days after in the beginning days after that In the beginning, days after that. after that after that we have after that we have done that after that we have done that predri after that we have done that predrine models after that we have done that predrine models and then after that we have done that predrine models and then if after that we have done that predrine models and then if shinnet also after that we have done that predrine models and then if shinnet also have used after that we have done that predrine models and then if shinnet also have used to find after that we have done that predrine models and then if shinnet also have used to find the layers after that we have done that predrine models and then if shinnet also have used to find the layers under everything After that we have done that predrine models and then if Shinnet also have used to find the layers under everything. after that after that we have after that we have done that after that we have done that image after that we have done that image enhance after that we have done that image enhancements techn after that we have done that image enhancements techniques after that we have done that image enhancements techniques and those things After that we have done that image enhancements, techniques and those things. and and to deep learning and to deep learning the imag and to deep learning the images and to deep learning the images sometimes we and to deep learning the images sometimes we'll get kind of and to deep learning the images sometimes we'll get kind of a low and to deep learning the images sometimes we'll get kind of a low quality of and to deep learning the images sometimes we'll get kind of a low quality of imag and to deep learning the images sometimes we'll get kind of a low quality of images and to deep learning the images sometimes we'll get kind of a low quality of images so and to deep learning the images sometimes we'll get kind of a low quality of images so we need to and to deep learning the images sometimes we'll get kind of a low quality of images so we need to deep blur them and to deep learning the images sometimes we'll get kind of a low quality of images so we need to deep blur them and then and to deep learning the images sometimes we'll get kind of a low quality of images so we need to deep blur them and then we have to And to deep learning the images. Sometimes we'll get kind of a low quality of images. So we need to deep blur them, and then we have to. use the use the super res use the super residential Use the super residential. deep learning deep learning resist deep learning resistance network deep learning resistance network so that's deep learning resistance network so that's the thing deep learning resistance network so that's the thing we have to ups deep learning resistance network so that's the thing we have to upscale deep learning resistance network so that's the thing we have to upscale the imag deep learning resistance network so that's the thing we have to upscale the image deep learning resistance network so that's the thing we have to upscale the image resolution deep learning resistance network so that's the thing we have to upscale the image resolutions deep learning resistance network so that's the thing we have to upscale the image resolutions those deep learning resistance network so that's the thing we have to upscale the image resolutions those things have deep learning resistance network so that's the thing we have to upscale the image resolutions those things have done after deep learning resistance network so that's the thing we have to upscale the image resolutions those things have done after that Deep learning resistance network. So that's the thing. We have to upscale the image resolutions. Those things have done after that. we have we have handy we have handy that we have handy that overlap we have handy that overlapping imag we have handy that overlapping images we have handy that overlapping images and kind of we have handy that overlapping images and kind of things we have handy that overlapping images and kind of things we have used we have handy that overlapping images and kind of things we have used like we have handy that overlapping images and kind of things we have used like a soft we have handy that overlapping images and kind of things we have used like a soft nms we have handy that overlapping images and kind of things we have used like a soft nms like We have handy that overlapping images and kind of things we have used like a soft NMS like. we have to we have to use the we have to use the layers we have to use the layers to we have to use the layers to identify We have to use the layers to identify. which is which is overlap which is overlap to which is overlap to in a single object which is overlap to in a single object like it which is overlap to in a single object like it had like three which is overlap to in a single object like it had like three or four which is overlap to in a single object like it had like three or four objects which is overlap to in a single object like it had like three or four objects but which is overlap to in a single object like it had like three or four objects but in the image which is overlap to in a single object like it had like three or four objects but in the image it will which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object it will which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object it will be there which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object it will be there so Which is overlap to in a single object, like it had like three or four objects, but in the image. It will detect 21 kind of object. It will be there. So. we have to we have to handle we have to handle that kind of we have to handle that kind of overlap we have to handle that kind of overlap object we have to handle that kind of overlap objects also we have to handle that kind of overlap objects also after that We have to handle that kind of overlap objects also after that. chemical chemical detect chemical detection and chemical detection and anomaly chemical detection and anomaly detection chemical detection and anomaly detection we have to Chemical detection and anomaly detection. We have to. have have done have done by using have done by using the have done by using the sensor have done by using the sensor and then have done by using the sensor and then imag have done by using the sensor and then image data Have done by using the sensor and then image data. so so we have to so we have to combine so we have to combine the both things So we have to combine the both things. whenever the whenever the sensor whenever the sensor is whenever the sensor is detected whenever the sensor is detected and whenever the sensor is detected and whenever whenever the sensor is detected and whenever the spike whenever the sensor is detected and whenever the spike is coming whenever the sensor is detected and whenever the spike is coming it is consider whenever the sensor is detected and whenever the spike is coming it is considered as like whenever the sensor is detected and whenever the spike is coming it is considered as like an arm det whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object red whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and the prediction and whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and the prediction and recall whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and the prediction and recall and Whenever the sensor is detected and whenever the spike is coming, it is considered as like an arm. Detection, and we will compare that thing with the object reduction as well. So, like that, we need. To identify things like that. After that, we have done that cross validation and the prediction and recall. And. score score those kind of things score those kind of things to val score those kind of things to validate score those kind of things to validate the things Score those kind of things to validate the things. after that after that we after that we go in with after that we go in with a real time after that we go in with a real time dep after that we go in with a real time deployment after that we go in with a real time deployment with after that we go in with a real time deployment with using the data after that we go in with a real time deployment with using the data breaks after that we go in with a real time deployment with using the data breaks and the cct after that we go in with a real time deployment with using the data breaks and the cct pip after that we go in with a real time deployment with using the data breaks and the cct pipelines and after that we go in with a real time deployment with using the data breaks and the cct pipelines and everything after that we go in with a real time deployment with using the data breaks and the cct pipelines and everything we have after that we go in with a real time deployment with using the data breaks and the cct pipelines and everything we have done After that we go in with a real time deployment with using the data breaks and the CCt. Pipelines and everything we have done. the fine tun the fine tuning also the fine tuning also the test the fine tuning also the testing the fine tuning also the testing and the fine tuning also the testing and improved acc the fine tuning also the testing and improved accuracy the fine tuning also the testing and improved accuracy and everything the fine tuning also the testing and improved accuracy and everything so the fine tuning also the testing and improved accuracy and everything so that's the thing The fine tuning, also the testing and improved accuracy and everything. So that's the thing. hello Hello. can you explain can you explain me can you explain me a last can you explain me a last project can you explain me a last project what can you explain me a last project what was the business can you explain me a last project what was the business problem can you explain me a last project what was the business problem what kind of can you explain me a last project what was the business problem what kind of a solution can you explain me a last project what was the business problem what kind of a solution you can you explain me a last project what was the business problem what kind of a solution you design
2024-08-26 16:40:41.890945: your teammate your teammate ten o'clock your teammate ten o'clock by your teammate ten o'clock by twelve Your teammate. 10:00 by twelve. yeah hi yeah hi kasik Yeah. Hi, Kasik. kasikam Kasikam. is joining is joining in a minute is joining in a minute just is joining in a minute just stay on is joining in a minute just stay on the line is joining in a minute just stay on the line yeah sure Is joining in a minute. Just stay on the line. Yeah, sure. yeah sure Yeah, sure. hi hi hash Hi, hash. can can turn on can turn on your camera can turn on your camera as well can turn on your camera as well yes can turn on your camera as well yes sure Can turn on your camera as well. Yes, sure. hi Hi. i'll assume i'll assume that i'll assume that you are a fair idea i'll assume that you are a fair idea about i'll assume that you are a fair idea about the job i'll assume that you are a fair idea about the job role I'll assume that you are a fair idea about the job role. team Team. any questions you have any questions you have before any questions you have before we start Any questions you have before we start. why don't why don't you tell me why don't you tell me something about why don't you tell me something about yourself why don't you tell me something about yourself okay why don't you tell me something about yourself okay fine Why don't you tell me something about yourself? Okay, fine. i have i have total eight point i have total eight point one years i have total eight point one years of experience i have total eight point one years of experience in i have total eight point one years of experience in it and then i have total eight point one years of experience in it and then the relevant i have total eight point one years of experience in it and then the relevant experience i have total eight point one years of experience in it and then the relevant experience with the i have total eight point one years of experience in it and then the relevant experience with the data science i have total eight point one years of experience in it and then the relevant experience with the data science and am i have total eight point one years of experience in it and then the relevant experience with the data science and aml i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around four point i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around four point one i have total eight point one years of experience in it and then the relevant experience with the data science and aml it's come around four point one years I have total 8.1 years of experience in it, and then the relevant experience with the data science and Aml. It's come around 4.1 years. i work with i work with mostly with i work with mostly with python i work with mostly with python and i work with mostly with python and work with i work with mostly with python and work with libr i work with mostly with python and work with libraries like num i work with mostly with python and work with libraries like numpy pan i work with mostly with python and work with libraries like numpy panel sci f i work with mostly with python and work with libraries like numpy panel sci fi nlt i work with mostly with python and work with libraries like numpy panel sci fi nltk i work with mostly with python and work with libraries like numpy panel sci fi nltk tenso i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow space i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i worked with the i work with mostly with python and work with libraries like numpy panel sci fi nltk tensorflow spacey cycle and those kind of things i woke down for data i worked with the map I work with, mostly with python and work with libraries like numpy panel sci-fi nltk, tensorflow, Spacey cycle and those kind of things. I woke down for data. I worked with the map. probably probably power bi probably power bi students probably power bi students i worked probably power bi students i worked on probably power bi students i worked on i'm experience probably power bi students i worked on i'm experienced in probably power bi students i worked on i'm experienced in psychoanalys probably power bi students i worked on i'm experienced in psychoanalysis like probably power bi students i worked on i'm experienced in psychoanalysis like ingredient probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy testing probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy testing probably probably power bi students i worked on i'm experienced in psychoanalysis like ingredient hypocrisy testing probably the Probably power BI students I worked on. I'm experienced in psychoanalysis like ingredient hypocrisy. Testing probably the. regression regression analysis regression analysis and regression analysis and discussment regression analysis and discussment and time regression analysis and discussment and time series and regression analysis and discussment and time series and all this Regression analysis and discussment and time series and all this. that's why i was That's why I was. running machine running machine learning Running machine learning. i worked i worked on project i worked on projects I worked on projects. like like regress like regression class like regression classification like regression classification cl like regression classification clustering like regression classification clustering and recommend like regression classification clustering and recommendation system like regression classification clustering and recommendation systems like regression classification clustering and recommendation systems and like regression classification clustering and recommendation systems and nsm method like regression classification clustering and recommendation systems and nsm methods was Like regression classification, clustering and recommendation systems and NSM methods was. i i worked with i worked with deep learning i worked with deep learning ml i worked with deep learning mlp framework i worked with deep learning mlp frameworks as well i worked with deep learning mlp frameworks as well like i worked with deep learning mlp frameworks as well like tensorflow i worked with deep learning mlp frameworks as well like tensorflow keras i worked with deep learning mlp frameworks as well like tensorflow keras space i worked with deep learning mlp frameworks as well like tensorflow keras spacey i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv in i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv in the i worked with deep learning mlp frameworks as well like tensorflow keras spacey bird and python cv in the setting I worked with deep learning MLP frameworks as well, like Tensorflow, Keras Spacey Bird and Python CV in the setting. i i worked with i worked with a few i worked with a few api i worked with a few api development i worked with a few api development part i worked with a few api development part as well I worked with a few API development part as well. and and i and i worked on and i worked on a database and i worked on a database like and i worked on a database like mys and i worked on a database like mysql and i worked on a database like mysql and sql serv and i worked on a database like mysql and sql servers and i worked on a database like mysql and sql servers and and i worked on a database like mysql and sql servers and those kind of and i worked on a database like mysql and sql servers and those kind of invoice skill and i worked on a database like mysql and sql servers and those kind of invoice skill i worked and i worked on a database like mysql and sql servers and those kind of invoice skill i worked only for and i worked on a database like mysql and sql servers and those kind of invoice skill i worked only for cassand and i worked on a database like mysql and sql servers and those kind of invoice skill i worked only for cassandra and And I worked on a database like MySQL and SQL servers and those kind of invoice skill. I worked only for Cassandra and. the mamod the mamodb the mamodb ones have the mamodb ones have worked the mamodb ones have worked downloads the mamodb ones have worked downloads on one data the mamodb ones have worked downloads on one database The mamodb ones have worked. Downloads on one database. transaction transaction analysis Transaction analysis. and ap and apart from that And apart from that. i might i might consider i might consider my previous i might consider my previous project I might consider my previous project. what we have what we have built is like what we have built is like to develop what we have built is like to develop a system what we have built is like to develop a system that what we have built is like to develop a system that could what we have built is like to develop a system that could accurately what we have built is like to develop a system that could accurately identify what we have built is like to develop a system that could accurately identify and what we have built is like to develop a system that could accurately identify and separate what we have built is like to develop a system that could accurately identify and separate the what we have built is like to develop a system that could accurately identify and separate the different what we have built is like to develop a system that could accurately identify and separate the different plastic what we have built is like to develop a system that could accurately identify and separate the different plastic types actually what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contam what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects in what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects in that we have what we have built is like to develop a system that could accurately identify and separate the different plastic types actually so when they are mixed together and also identified chemical contamination plastic in objects in that we have to do the What we have built is like to develop a system that could accurately identify and separate the different plastic types, actually. So when they are mixed together and also identified chemical contamination, plastic in objects in that we have to do the. features features and features and pattern features and patterns and features and patterns and we have features and patterns and we have to distinguish features and patterns and we have to distinguish each plast features and patterns and we have to distinguish each plastic type features and patterns and we have to distinguish each plastic type those kind of things features and patterns and we have to distinguish each plastic type those kind of things we features and patterns and we have to distinguish each plastic type those kind of things we need to do Features and patterns, and we have to distinguish each plastic type, those kind of things. We need to do. it will it will lead it will lead to it will lead to the more effic it will lead to the more efficient recyc it will lead to the more efficient recycling it will lead to the more efficient recycling and wast it will lead to the more efficient recycling and waste management it will lead to the more efficient recycling and waste management and it will lead to the more efficient recycling and waste management and deceased the operational it will lead to the more efficient recycling and waste management and deceased the operational cost it will lead to the more efficient recycling and waste management and deceased the operational cost and it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it is it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it is so here it will lead to the more efficient recycling and waste management and deceased the operational cost and while doing this artist again that's the thing mainly here it is so here totally It will lead to the more efficient recycling and waste management and deceased the operational cost. And while doing this artist again. That's the thing, mainly. Here it is. So here. Totally. we have We have. eight type eight types of eight types of plast eight types of plastics Eight types of plastics. some some we need to ident some we need to identify some we need to identify in chem some we need to identify in chemical contam some we need to identify in chemical contamination also some we need to identify in chemical contamination also based some we need to identify in chemical contamination also based on Some we need to identify in chemical contamination. Also, based on. the video the video actually the video actually like in the video actually like in this video The video actually, like in this video. we need to extra we need to extract we need to extract the images we need to extract the images from there we need to extract the images from there by using we need to extract the images from there by using the open we need to extract the images from there by using the openc we need to extract the images from there by using the opencv we need to extract the images from there by using the opencv and we need to extract the images from there by using the opencv and instead we need to extract the images from there by using the opencv and instead of we need to extract the images from there by using the opencv and instead of taking the we need to extract the images from there by using the opencv and instead of taking the random we need to extract the images from there by using the opencv and instead of taking the random pictures we need to extract the images from there by using the opencv and instead of taking the random pictures we have used we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detect we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algor we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the opt we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical fl we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the open we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and twenty we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and twenty five frames and we need to extract the images from there by using the opencv and instead of taking the random pictures we have used kind of motion detection algorithm like we need to in the background we need to subtract the optical flow and whatever the availability from the opencv and twenty five frames and it We need to extract the images from there by using the OpenCV. And instead of taking the random pictures. We have used kind of motion detection algorithm like we need to in the background. We need to subtract the optical flow and whatever the availability from the OpenCV. And 25 frames, and it will give us. like Like. more more any kind of more any kind of motion object more any kind of motion objects More any kind of motion, objects. based based on frame based on frame scoring also based on frame scoring also we have Based on frame scoring. Also, we have. implemented implemented like to elev implemented like to elevate the implemented like to elevate the factors implemented like to elevate the factors like implemented like to elevate the factors like kind of implemented like to elevate the factors like kind of object implemented like to elevate the factors like kind of object clar implemented like to elevate the factors like kind of object clarity and implemented like to elevate the factors like kind of object clarity and overlap Implemented, like, to elevate the factors like kind of object clarity and overlap. and And. kind of kind of coverage kind of coverage and kind of coverage and based on kind of coverage and based on the prioritized kind of coverage and based on the prioritized frames Kind of coverage. And based on the prioritized frames, we need to we need to extract we need to extract the we need to extract the images we need to extract the images after we need to extract the images after that We need to extract the images after that. imag image extraction image extraction we have done image extraction we have done that image extraction we have done that like image extraction we have done that like a classification image extraction we have done that like a classification we image extraction we have done that like a classification we need to classify image extraction we have done that like a classification we need to classify the imag image extraction we have done that like a classification we need to classify the images like image extraction we have done that like a classification we need to classify the images like after that image extraction we have done that like a classification we need to classify the images like after that we need image extraction we have done that like a classification we need to classify the images like after that we need to do the image extraction we have done that like a classification we need to classify the images like after that we need to do the detection image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rs image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsn image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsnn image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsnn this image extraction we have done that like a classification we need to classify the images like after that we need to do the detection model like mask rsnn this model Image extraction. We have done that. Like a classification. We need to classify the images, like after that. We need to do the detection model like mask, rsnn. This model. gives a gives a boundary box gives a boundary boxes gives a boundary boxes of each gives a boundary boxes of each pixel gives a boundary boxes of each pixel wise gives a boundary boxes of each pixel wise the marks gives a boundary boxes of each pixel wise the marks of gives a boundary boxes of each pixel wise the marks of each gives a boundary boxes of each pixel wise the marks of each image gives a boundary boxes of each pixel wise the marks of each image and Gives a boundary boxes of each pixel wise, the marks of each image, and. the the canvas the canvas vertically the canvas vertically within the the canvas vertically within the single frame the canvas vertically within the single frame it will the canvas vertically within the single frame it will have the canvas vertically within the single frame it will have like the canvas vertically within the single frame it will have like one hundred to one the canvas vertically within the single frame it will have like one hundred to one hundred and fifty the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to ident the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask r the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcn the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm al the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like most the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character labels and we have the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character labels and we have to do the canvas vertically within the single frame it will have like one hundred to one hundred and fifty objects are there so we need to identify each one app so we require boundary boxes to give that so that's why we have used the mask rcnm algorithm i was involved like mostly it will character labels and we have to do the The canvas vertically within the single frame, it will have like 100 to 150 objects are there, so. We need to identify each one app. So we require boundary boxes to give that. So that's. Why we have used the mask RCNM algorithm. I was involved like mostly it will character labels, and we have to do the. masking masking things and masking things and we have to do it masking things and we have to do it manually Masking things and we have to do it manually. in the beginning in the beginning days after in the beginning days after that In the beginning, days after that. after that after that we have after that we have done that after that we have done that predri after that we have done that predrine models after that we have done that predrine models and then after that we have done that predrine models and then if after that we have done that predrine models and then if shinnet also after that we have done that predrine models and then if shinnet also have used after that we have done that predrine models and then if shinnet also have used to find after that we have done that predrine models and then if shinnet also have used to find the layers after that we have done that predrine models and then if shinnet also have used to find the layers under everything After that we have done that predrine models and then if Shinnet also have used to find the layers under everything. after that after that we have after that we have done that after that we have done that image after that we have done that image enhance after that we have done that image enhancements techn after that we have done that image enhancements techniques after that we have done that image enhancements techniques and those things After that we have done that image enhancements, techniques and those things. and and to deep learning and to deep learning the imag and to deep learning the images and to deep learning the images sometimes we and to deep learning the images sometimes we'll get kind of and to deep learning the images sometimes we'll get kind of a low and to deep learning the images sometimes we'll get kind of a low quality of and to deep learning the images sometimes we'll get kind of a low quality of imag and to deep learning the images sometimes we'll get kind of a low quality of images and to deep learning the images sometimes we'll get kind of a low quality of images so and to deep learning the images sometimes we'll get kind of a low quality of images so we need to and to deep learning the images sometimes we'll get kind of a low quality of images so we need to deep blur them and to deep learning the images sometimes we'll get kind of a low quality of images so we need to deep blur them and then and to deep learning the images sometimes we'll get kind of a low quality of images so we need to deep blur them and then we have to And to deep learning the images. Sometimes we'll get kind of a low quality of images. So we need to deep blur them, and then we have to. use the use the super res use the super residential Use the super residential. deep learning deep learning resist deep learning resistance network deep learning resistance network so that's deep learning resistance network so that's the thing deep learning resistance network so that's the thing we have to ups deep learning resistance network so that's the thing we have to upscale deep learning resistance network so that's the thing we have to upscale the imag deep learning resistance network so that's the thing we have to upscale the image deep learning resistance network so that's the thing we have to upscale the image resolution deep learning resistance network so that's the thing we have to upscale the image resolutions deep learning resistance network so that's the thing we have to upscale the image resolutions those deep learning resistance network so that's the thing we have to upscale the image resolutions those things have deep learning resistance network so that's the thing we have to upscale the image resolutions those things have done after deep learning resistance network so that's the thing we have to upscale the image resolutions those things have done after that Deep learning resistance network. So that's the thing. We have to upscale the image resolutions. Those things have done after that. we have we have handy we have handy that we have handy that overlap we have handy that overlapping imag we have handy that overlapping images we have handy that overlapping images and kind of we have handy that overlapping images and kind of things we have handy that overlapping images and kind of things we have used we have handy that overlapping images and kind of things we have used like we have handy that overlapping images and kind of things we have used like a soft we have handy that overlapping images and kind of things we have used like a soft nms we have handy that overlapping images and kind of things we have used like a soft nms like We have handy that overlapping images and kind of things we have used like a soft NMS like. we have to we have to use the we have to use the layers we have to use the layers to we have to use the layers to identify We have to use the layers to identify. which is which is overlap which is overlap to which is overlap to in a single object which is overlap to in a single object like it which is overlap to in a single object like it had like three which is overlap to in a single object like it had like three or four which is overlap to in a single object like it had like three or four objects which is overlap to in a single object like it had like three or four objects but which is overlap to in a single object like it had like three or four objects but in the image which is overlap to in a single object like it had like three or four objects but in the image it will which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object it will which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object it will be there which is overlap to in a single object like it had like three or four objects but in the image it will detect twenty one kind of object it will be there so Which is overlap to in a single object, like it had like three or four objects, but in the image. It will detect 21 kind of object. It will be there. So. we have to we have to handle we have to handle that kind of we have to handle that kind of overlap we have to handle that kind of overlap object we have to handle that kind of overlap objects also we have to handle that kind of overlap objects also after that We have to handle that kind of overlap objects also after that. chemical chemical detect chemical detection and chemical detection and anomaly chemical detection and anomaly detection chemical detection and anomaly detection we have to Chemical detection and anomaly detection. We have to. have have done have done by using have done by using the have done by using the sensor have done by using the sensor and then have done by using the sensor and then imag have done by using the sensor and then image data Have done by using the sensor and then image data. so so we have to so we have to combine so we have to combine the both things So we have to combine the both things. whenever the whenever the sensor whenever the sensor is whenever the sensor is detected whenever the sensor is detected and whenever the sensor is detected and whenever whenever the sensor is detected and whenever the spike whenever the sensor is detected and whenever the spike is coming whenever the sensor is detected and whenever the spike is coming it is consider whenever the sensor is detected and whenever the spike is coming it is considered as like whenever the sensor is detected and whenever the spike is coming it is considered as like an arm det whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object red whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and the prediction and whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and the prediction and recall whenever the sensor is detected and whenever the spike is coming it is considered as like an arm detection and we will compare that thing with the object reduction as well so like that we need to identify things like that after that we have done that cross validation and the prediction and recall and Whenever the sensor is detected and whenever the spike is coming, it is considered as like an arm. Detection, and we will compare that thing with the object reduction as well. So, like that, we need. To identify things like that. After that, we have done that cross validation and the prediction and recall. And. score score those kind of things score those kind of things to val score those kind of things to validate score those kind of things to validate the things Score those kind of things to validate the things. after that after that we after that we go in with after that we go in with a real time after that we go in with a real time dep after that we go in with a real time deployment after that we go in with a real time deployment with after that we go in with a real time deployment with using the data after that we go in with a real time deployment with using the data breaks after that we go in with a real time deployment with using the data breaks and the cct after that we go in with a real time deployment with using the data breaks and the cct pip after that we go in with a real time deployment with using the data breaks and the cct pipelines and after that we go in with a real time deployment with using the data breaks and the cct pipelines and everything after that we go in with a real time deployment with using the data breaks and the cct pipelines and everything we have after that we go in with a real time deployment with using the data breaks and the cct pipelines and everything we have done After that we go in with a real time deployment with using the data breaks and the CCt. Pipelines and everything we have done. the fine tun the fine tuning also the fine tuning also the test the fine tuning also the testing the fine tuning also the testing and the fine tuning also the testing and improved acc the fine tuning also the testing and improved accuracy the fine tuning also the testing and improved accuracy and everything the fine tuning also the testing and improved accuracy and everything so the fine tuning also the testing and improved accuracy and everything so that's the thing The fine tuning, also the testing and improved accuracy and everything. So that's the thing. hello Hello. can you explain can you explain me can you explain me a last can you explain me a last project can you explain me a last project what can you explain me a last project what was the business can you explain me a last project what was the business problem can you explain me a last project what was the business problem what kind of can you explain me a last project what was the business problem what kind of a solution can you explain me a last project what was the business problem what kind of a solution you can you explain me a last project what was the business problem what kind of a solution you design
2024-08-26 16:42:15.893314: Can you explain me a last project? What was the business problem? What kind of a solution? You. Design. okay okay the business okay the business problem okay the business problem is okay the business problem is like Okay. The business problem is, like. we need to we need to ident we need to identify we need to identify the we need to identify the plastic we need to identify the plastic types we need to identify the plastic types before going we need to identify the plastic types before going to the rec we need to identify the plastic types before going to the recycling process We need to identify the plastic types before going to the recycling process. they want their kind of they want their kind of eight types of they want their kind of eight types of plastics they want their kind of eight types of plastics or in recycled they want their kind of eight types of plastics or in recycled process they want their kind of eight types of plastics or in recycled process some they want their kind of eight types of plastics or in recycled process some plast they want their kind of eight types of plastics or in recycled process some plastics are they want their kind of eight types of plastics or in recycled process some plastics are recyclable they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plast they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not recyclable they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not recyclable at the same they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not recyclable at the same time They want their kind of eight types of plastics or in recycled process. Some plastics are recyclable some plastics are not recyclable at the same time. in the plastic in the plastic we need to in the plastic we need to identify in the plastic we need to identify any in the plastic we need to identify any chem in the plastic we need to identify any chemical in the plastic we need to identify any chemical contamination in the plastic we need to identify any chemical contamination is there in the plastic we need to identify any chemical contamination is there or not in the plastic we need to identify any chemical contamination is there or not some chemical in the plastic we need to identify any chemical contamination is there or not some chemicals in the plastic we need to identify any chemical contamination is there or not some chemicals are not in the plastic we need to identify any chemical contamination is there or not some chemicals are not to be recycle in the plastic we need to identify any chemical contamination is there or not some chemicals are not to be recycled In the plastic. We need to identify any chemical contamination. Is there or not? Some chemicals are not. To be recycled. like that Like that. the objects the objects will be there the objects will be there so the objects will be there so there are kind of the objects will be there so there are kind of eight objects the objects will be there so there are kind of eight objects i'm sorry the objects will be there so there are kind of eight objects i'm sorry eight type of plast the objects will be there so there are kind of eight objects i'm sorry eight type of plastics like the objects will be there so there are kind of eight objects i'm sorry eight type of plastics like pet The objects will be there. So there are kind of eight objects. I'm sorry, eight type? Of plastics, like pet. httpe httpe pv httpe pvc l httpe pvc ld httpe pvc ldp httpe pvc ldp pp httpe pvc ldp pp like httpe pvc ldp pp like those kind of httpe pvc ldp pp like those kind of polynorph httpe pvc ldp pp like those kind of polynorphyls httpe pvc ldp pp like those kind of polynorphyls and those httpe pvc ldp pp like those kind of polynorphyls and those kind of httpe pvc ldp pp like those kind of polynorphyls and those kind of object httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the images httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the images we need to httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the images we need to differentiate Httpe PvC LDP pp like those kind of polynorphyls and those kind of objects are there. So based on the images, we need to differentiate. each plast each plastic type each plastic type and Each plastic type and. we need to we need to identify We need to identify. which plastic which plastic can be which plastic can be recyclable which plastic can be recyclable and which which plastic can be recyclable and which can Which plastic can be recyclable and which can. we need to we need to grade them we need to grade them actually so we need to grade them actually so it will give the we need to grade them actually so it will give the like it we need to grade them actually so it will give the like it will give we need to grade them actually so it will give the like it will give a decrease we need to grade them actually so it will give the like it will give a decrease of we need to grade them actually so it will give the like it will give a decrease of operational cost we need to grade them actually so it will give the like it will give a decrease of operational cost while doing we need to grade them actually so it will give the like it will give a decrease of operational cost while doing that recycle we need to grade them actually so it will give the like it will give a decrease of operational cost while doing that recycle that's we need to grade them actually so it will give the like it will give a decrease of operational cost while doing that recycle that's the thing We need to grade them, actually. So it will give the like, it will give a decrease of. Operational cost while doing that recycle. That's the thing. then Then. how How? do you do you treat your do you treat your outliers
2024-08-26 16:42:15.893314: Can you explain me a last project? What was the business problem? What kind of a solution? You. Design. okay okay the business okay the business problem okay the business problem is okay the business problem is like Okay. The business problem is, like. we need to we need to ident we need to identify we need to identify the we need to identify the plastic we need to identify the plastic types we need to identify the plastic types before going we need to identify the plastic types before going to the rec we need to identify the plastic types before going to the recycling process We need to identify the plastic types before going to the recycling process. they want their kind of they want their kind of eight types of they want their kind of eight types of plastics they want their kind of eight types of plastics or in recycled they want their kind of eight types of plastics or in recycled process they want their kind of eight types of plastics or in recycled process some they want their kind of eight types of plastics or in recycled process some plast they want their kind of eight types of plastics or in recycled process some plastics are they want their kind of eight types of plastics or in recycled process some plastics are recyclable they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plast they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not recyclable they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not recyclable at the same they want their kind of eight types of plastics or in recycled process some plastics are recyclable some plastics are not recyclable at the same time They want their kind of eight types of plastics or in recycled process. Some plastics are recyclable some plastics are not recyclable at the same time. in the plastic in the plastic we need to in the plastic we need to identify in the plastic we need to identify any in the plastic we need to identify any chem in the plastic we need to identify any chemical in the plastic we need to identify any chemical contamination in the plastic we need to identify any chemical contamination is there in the plastic we need to identify any chemical contamination is there or not in the plastic we need to identify any chemical contamination is there or not some chemical in the plastic we need to identify any chemical contamination is there or not some chemicals in the plastic we need to identify any chemical contamination is there or not some chemicals are not in the plastic we need to identify any chemical contamination is there or not some chemicals are not to be recycle in the plastic we need to identify any chemical contamination is there or not some chemicals are not to be recycled In the plastic. We need to identify any chemical contamination. Is there or not? Some chemicals are not. To be recycled. like that Like that. the objects the objects will be there the objects will be there so the objects will be there so there are kind of the objects will be there so there are kind of eight objects the objects will be there so there are kind of eight objects i'm sorry the objects will be there so there are kind of eight objects i'm sorry eight type of plast the objects will be there so there are kind of eight objects i'm sorry eight type of plastics like the objects will be there so there are kind of eight objects i'm sorry eight type of plastics like pet The objects will be there. So there are kind of eight objects. I'm sorry, eight type? Of plastics, like pet. httpe httpe pv httpe pvc l httpe pvc ld httpe pvc ldp httpe pvc ldp pp httpe pvc ldp pp like httpe pvc ldp pp like those kind of httpe pvc ldp pp like those kind of polynorph httpe pvc ldp pp like those kind of polynorphyls httpe pvc ldp pp like those kind of polynorphyls and those httpe pvc ldp pp like those kind of polynorphyls and those kind of httpe pvc ldp pp like those kind of polynorphyls and those kind of object httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the images httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the images we need to httpe pvc ldp pp like those kind of polynorphyls and those kind of objects are there so based on the images we need to differentiate Httpe PvC LDP pp like those kind of polynorphyls and those kind of objects are there. So based on the images, we need to differentiate. each plast each plastic type each plastic type and Each plastic type and. we need to we need to identify We need to identify. which plastic which plastic can be which plastic can be recyclable which plastic can be recyclable and which which plastic can be recyclable and which can Which plastic can be recyclable and which can. we need to we need to grade them we need to grade them actually so we need to grade them actually so it will give the we need to grade them actually so it will give the like it we need to grade them actually so it will give the like it will give we need to grade them actually so it will give the like it will give a decrease we need to grade them actually so it will give the like it will give a decrease of we need to grade them actually so it will give the like it will give a decrease of operational cost we need to grade them actually so it will give the like it will give a decrease of operational cost while doing we need to grade them actually so it will give the like it will give a decrease of operational cost while doing that recycle we need to grade them actually so it will give the like it will give a decrease of operational cost while doing that recycle that's we need to grade them actually so it will give the like it will give a decrease of operational cost while doing that recycle that's the thing We need to grade them, actually. So it will give the like, it will give a decrease of. Operational cost while doing that recycle. That's the thing. then Then. how How? do you do you treat your do you treat your outliers
2024-08-26 16:42:32.864382: Do you treat your outliers? outliers Outliers. in our in our project In our project. we have we have done We have done. distinguish distinguish between the distinguish between the types of distinguish between the types of plast distinguish between the types of plastics distinguish between the types of plastics so distinguish between the types of plastics so after that Distinguish between the types of plastics, so after that. we have we have done We have done. like like if like if there is no like if there is non plastic like if there is non plastic objects
2024-08-26 16:42:32.865377: Do you treat your outliers? outliers Outliers. in our in our project In our project. we have we have done We have done. distinguish distinguish between the distinguish between the types of distinguish between the types of plast distinguish between the types of plastics distinguish between the types of plastics so distinguish between the types of plastics so after that Distinguish between the types of plastics, so after that. we have we have done We have done. like like if like if there is no like if there is non plastic like if there is non plastic objects
2024-08-26 16:43:18.312446: like if there is non plastic objects identify like if there is non plastic objects identifying like if there is non plastic objects identifying non like if there is non plastic objects identifying non plastic like if there is non plastic objects identifying non plastic objects Like if there is non plastic objects. Identifying non plastic objects. that's that's why we do that's why we do identify that that's why we do identify that things based that's why we do identify that things based on the that's why we do identify that things based on the images actually that's why we do identify that things based on the images actually we that's why we do identify that things based on the images actually we have the pict that's why we do identify that things based on the images actually we have the picture that's why we do identify that things based on the images actually we have the picture wise that's why we do identify that things based on the images actually we have the picture wise in the layers that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlay that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resol that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techn that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techniques to impro that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techniques to improve the imag that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techniques to improve the image quality That's why we do identify that things based on the images. Actually, we have the picture. Wise in the layers. We have done that outlier outlays and how we can handle these things and everything, like image enhancement, like the deblurring and super resolution techniques to improve the image quality. based based on the based on the pixel based on the pixel things based on the pixel things we have done based on the pixel things we have done after that based on the pixel things we have done after that we based on the pixel things we have done after that we have done based on the pixel things we have done after that we have done the super based on the pixel things we have done after that we have done the super resolution based on the pixel things we have done after that we have done the super resolution with based on the pixel things we have done after that we have done the super resolution with the based on the pixel things we have done after that we have done the super resolution with the edsr based on the pixel things we have done after that we have done the super resolution with the edsr so enh based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the detail based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clar based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the tissue based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the tissue images based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the tissue images like that Based on the pixel things we have done after that, we have done the super resolution with the EDSR, so enhance the details and clarity of the tissue images like that. we have to run we have to run that and we have to run that and after that we have to run that and after that object we have to run that and after that object det we have to run that and after that object detection we have to run that and after that object detection after we have to run that and after that object detection after we have we have to run that and after that object detection after we have done with the we have to run that and after that object detection after we have done with the mask we have to run that and after that object detection after we have done with the mask are seen we have to run that and after that object detection after we have done with the mask are seen inside we have to run that and after that object detection after we have done with the mask are seen inside so it will we have to run that and after that object detection after we have done with the mask are seen inside so it will improve we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary box we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pix we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we can we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we can identify we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we can identify that
2024-08-26 16:43:18.313406: like if there is non plastic objects identify like if there is non plastic objects identifying like if there is non plastic objects identifying non like if there is non plastic objects identifying non plastic like if there is non plastic objects identifying non plastic objects Like if there is non plastic objects. Identifying non plastic objects. that's that's why we do that's why we do identify that that's why we do identify that things based that's why we do identify that things based on the that's why we do identify that things based on the images actually that's why we do identify that things based on the images actually we that's why we do identify that things based on the images actually we have the pict that's why we do identify that things based on the images actually we have the picture that's why we do identify that things based on the images actually we have the picture wise that's why we do identify that things based on the images actually we have the picture wise in the layers that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlay that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resol that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techn that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techniques to impro that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techniques to improve the imag that's why we do identify that things based on the images actually we have the picture wise in the layers we have done that outlier outlays and how we can handle these things and everything like image enhancement like the deblurring and super resolution techniques to improve the image quality That's why we do identify that things based on the images. Actually, we have the picture. Wise in the layers. We have done that outlier outlays and how we can handle these things and everything, like image enhancement, like the deblurring and super resolution techniques to improve the image quality. based based on the based on the pixel based on the pixel things based on the pixel things we have done based on the pixel things we have done after that based on the pixel things we have done after that we based on the pixel things we have done after that we have done based on the pixel things we have done after that we have done the super based on the pixel things we have done after that we have done the super resolution based on the pixel things we have done after that we have done the super resolution with based on the pixel things we have done after that we have done the super resolution with the based on the pixel things we have done after that we have done the super resolution with the edsr based on the pixel things we have done after that we have done the super resolution with the edsr so enh based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the detail based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clar based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the tissue based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the tissue images based on the pixel things we have done after that we have done the super resolution with the edsr so enhance the details and clarity of the tissue images like that Based on the pixel things we have done after that, we have done the super resolution with the EDSR, so enhance the details and clarity of the tissue images like that. we have to run we have to run that and we have to run that and after that we have to run that and after that object we have to run that and after that object det we have to run that and after that object detection we have to run that and after that object detection after we have to run that and after that object detection after we have we have to run that and after that object detection after we have done with the we have to run that and after that object detection after we have done with the mask we have to run that and after that object detection after we have done with the mask are seen we have to run that and after that object detection after we have done with the mask are seen inside we have to run that and after that object detection after we have done with the mask are seen inside so it will we have to run that and after that object detection after we have done with the mask are seen inside so it will improve we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary box we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pix we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we can we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we can identify we have to run that and after that object detection after we have done with the mask are seen inside so it will improve that boundary boxes if each pixel wise marks are correct or not so based on that we can identify that
2024-08-26 16:43:31.337498: We have to run that. And after that, object detection, after we have done with the mask, are seen inside. So it will improve that boundary boxes if each pixel wise marks are correct or not. So based on that, we can identify that. are we involved are we involved in deployment are we involved in deployment of the model are we involved in deployment of the models as are we involved in deployment of the models as well are we involved in deployment of the models as well or not are we involved in deployment of the models as well or not i'm sorry Are we involved in deployment of the models as well, or not? I'm sorry. deploy Deploy.
2024-08-26 16:43:31.337498: We have to run that. And after that, object detection, after we have done with the mask, are seen inside. So it will improve that boundary boxes if each pixel wise marks are correct or not. So based on that, we can identify that. are we involved are we involved in deployment are we involved in deployment of the model are we involved in deployment of the models as are we involved in deployment of the models as well are we involved in deployment of the models as well or not are we involved in deployment of the models as well or not i'm sorry Are we involved in deployment of the models as well, or not? I'm sorry. deploy Deploy.
2024-08-26 16:44:46.858681: yes Yes. mostly mostly i worked with Mostly I worked with. data data passing data passing and data passing and that data passing and that part actually data passing and that part actually like we're data passing and that part actually like we're going with the az data passing and that part actually like we're going with the azure data data passing and that part actually like we're going with the azure databics Data passing. And that part actually like we're going with the azure databics. here here the data here the data is coming here the data is coming as from here the data is coming as from the here the data is coming as from the videos actually here the data is coming as from the videos actually like it's here the data is coming as from the videos actually like it's a twenty second here the data is coming as from the videos actually like it's a twenty second video here the data is coming as from the videos actually like it's a twenty second video we need to here the data is coming as from the videos actually like it's a twenty second video we need to get here the data is coming as from the videos actually like it's a twenty second video we need to get the images here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a pipeline here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a pipeline after here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a pipeline after that Here. The data is coming as from the videos, actually, like it's a 22nd video. We need to get the images from there, and then after that we have to apply the OpenCV. And then we need to get the images there, and then we have to build a pipeline. After that. we have done We have done. this this testing This. Testing. like like fine tun like fine tuning like fine tuning and then like fine tuning and then data like fine tuning and then data augmentation like fine tuning and then data augmentation if like fine tuning and then data augmentation if there is flip like fine tuning and then data augmentation if there is flip part or not like fine tuning and then data augmentation if there is flip part or not we need to do that like fine tuning and then data augmentation if there is flip part or not we need to do that after like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyper like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparame like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparame like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tun like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have used this like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have used this azure data like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have used this azure database Like fine tuning and then data augmentation. If there is flip part or not, we need to do that after the hyperparameter. Hyperparameter tuning we have done. After that, I. Will try the detection model and then we have used this azure database. any Any. other Other. in modern in modern deployment in modern deployment i In modern deployment, I. work for Work for. one previous project one previous project that is One previous project that is. with with incident with incident management with incident management actually with incident management actually so in with incident management actually so in that with incident management actually so in that actually invol with incident management actually so in that actually involved with incident management actually so in that actually involved in With incident management, actually. So in that actually involved in. this this mod this modal deployment this modal deployment using this modal deployment using the aw this modal deployment using the aws this modal deployment using the aws seas make this modal deployment using the aws seas maker actually this modal deployment using the aws seas maker actually so this modal deployment using the aws seas maker actually so that this modal deployment using the aws seas maker actually so that i have
2024-08-26 16:44:46.861501: yes Yes. mostly mostly i worked with Mostly I worked with. data data passing data passing and data passing and that data passing and that part actually data passing and that part actually like we're data passing and that part actually like we're going with the az data passing and that part actually like we're going with the azure data data passing and that part actually like we're going with the azure databics Data passing. And that part actually like we're going with the azure databics. here here the data here the data is coming here the data is coming as from here the data is coming as from the here the data is coming as from the videos actually here the data is coming as from the videos actually like it's here the data is coming as from the videos actually like it's a twenty second here the data is coming as from the videos actually like it's a twenty second video here the data is coming as from the videos actually like it's a twenty second video we need to here the data is coming as from the videos actually like it's a twenty second video we need to get here the data is coming as from the videos actually like it's a twenty second video we need to get the images here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a pipeline here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a pipeline after here the data is coming as from the videos actually like it's a twenty second video we need to get the images from there and then after that we have to apply the opencv and then we need to get the images there and then we have to build a pipeline after that Here. The data is coming as from the videos, actually, like it's a 22nd video. We need to get the images from there, and then after that we have to apply the OpenCV. And then we need to get the images there, and then we have to build a pipeline. After that. we have done We have done. this this testing This. Testing. like like fine tun like fine tuning like fine tuning and then like fine tuning and then data like fine tuning and then data augmentation like fine tuning and then data augmentation if like fine tuning and then data augmentation if there is flip like fine tuning and then data augmentation if there is flip part or not like fine tuning and then data augmentation if there is flip part or not we need to do that like fine tuning and then data augmentation if there is flip part or not we need to do that after like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyper like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparame like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparame like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tun like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have used this like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have used this azure data like fine tuning and then data augmentation if there is flip part or not we need to do that after the hyperparameter hyperparameter tuning we have done after that i will try the detection model and then we have used this azure database Like fine tuning and then data augmentation. If there is flip part or not, we need to do that after the hyperparameter. Hyperparameter tuning we have done. After that, I. Will try the detection model and then we have used this azure database. any Any. other Other. in modern in modern deployment in modern deployment i In modern deployment, I. work for Work for. one previous project one previous project that is One previous project that is. with with incident with incident management with incident management actually with incident management actually so in with incident management actually so in that with incident management actually so in that actually invol with incident management actually so in that actually involved with incident management actually so in that actually involved in With incident management, actually. So in that actually involved in. this this mod this modal deployment this modal deployment using this modal deployment using the aw this modal deployment using the aws this modal deployment using the aws seas make this modal deployment using the aws seas maker actually this modal deployment using the aws seas maker actually so this modal deployment using the aws seas maker actually so that this modal deployment using the aws seas maker actually so that i have
