from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from openai import OpenAI
import queue
import threading
import assemblyai as aai
import time
import json
import logging
from concurrent.futures import ThreadPoolExecutor
import signal
import sys
import asyncio
from collections import deque
import os
from pydantic import BaseModel
import datetime
import uvicorn

app = FastAPI()
app.add_middleware(
  CORSMiddleware,
  allow_origins=["*"],
  allow_credentials=True,
  allow_methods=["*"],
  allow_headers=["*"],
)

# Disable logging for specific modules
logging.getLogger('websockets').setLevel(logging.CRITICAL)
logging.getLogger('httpx').setLevel(logging.CRITICAL)
logging.getLogger('assemblyai').setLevel(logging.CRITICAL)
logging.getLogger('openai').setLevel(logging.CRITICAL)

# Set the global logging level to DEBUG
logging.basicConfig(level=logging.DEBUG)

# Setup API keys (Replace with your actual keys)
aai.settings.api_key = "d519e68d269f4b6eb755212f2b408b2d"
client = OpenAI(api_key="********************************************************************************************************************************************************************")

transcript_queue = queue.Queue()
response_queue = queue.Queue()
transcriber = None
stop_event = threading.Event()
response_thread = None
buffered_transcript = ""
last_transcript = ""
project_details = None
is_transcribing = False
manual_input = ""  # New global variable for manual input

executor = ThreadPoolExecutor(max_workers=4)

# In-memory storage for transcripts and responses using deque
transcripts = deque(maxlen=100)  # Keep only the latest 100 transcripts
responses = deque(maxlen=100)  # Keep only the latest 100 responses
last_questions = deque(maxlen=2)  # Keep only the latest 2 questions

class ProjectDetails(BaseModel):
  details: str

class ManualInput(BaseModel):
  input: str

def get_session_filename():
  now = datetime.datetime.now()
  filename = f"session_{now.strftime('%Y%m%d_%H%M%S')}.txt"
  filepath = os.path.join("session_logs", filename)
  return filename, filepath

# Create global variables to store the session filename and filepath
session_filename, session_filepath = get_session_filename()

def write_question_to_file(question):
  global session_filepath
  os.makedirs("session_logs", exist_ok=True)
  with open(session_filepath, "a") as f:
      f.write(f"{datetime.datetime.now()}: {question}\n")

@app.post('/set_project_details')
async def set_project_details(details: ProjectDetails):
  global project_details
  project_details = details.details
  return JSONResponse(content={'status': 'project details set'})

@app.post('/set_manual_input')
async def set_manual_input(input_data: ManualInput):
  global manual_input
  manual_input = input_data.input
  return JSONResponse(content={'status': 'manual input set'})

def on_data(transcript: aai.RealtimeTranscript):
  global buffered_transcript, last_transcript
  if not transcript.text or transcript.text == last_transcript:
      return
  last_transcript = transcript.text
  buffered_transcript += " " + transcript.text.strip()  # Append the transcript text to the buffer

def on_error(error: aai.RealtimeError):
  logging.error(f"An error occurred: {error}")

def handle_transcription():
  global transcriber, is_transcribing
  try:
      transcriber = aai.RealtimeTranscriber(
          on_data=on_data,
          on_error=on_error,
          sample_rate=44_100,
      )
      transcriber.connect()
      microphone_stream = aai.extras.MicrophoneStream()
      while not stop_event.is_set():
          if is_transcribing:
              transcriber.stream(microphone_stream)
          else:
              time.sleep(0.1)  # Small delay to prevent busy-waiting
  except Exception as e:
      logging.error(f"Failed to connect to real-time service: {e}")

def handle_responses():
  global stop_event, buffered_transcript, project_details, last_questions
  prompt =  """You are participating in an interview for a Senior ROBOTIC FrameworkPython Automation Engineer, Selinium, Test Engineer position. Based on the provided "project_details", respond to the interview questions in a structured format consisting of two parts:

Part 1: Concise Answer

Provide a direct and precise answer to the interview question, demonstrating your expertise in Python test automation.
Tailor your explanation to the question, showcasing your comprehensive knowledge across all aspects of testing and your technical depth as a Senior Python Automation Engineer
Part 2: Detailed Explanation

Deliver a highly technical, question-specific explanation relevant to the "project_details".
Discuss applicable Python automation technologies and tools, covering a broad range of solutions for web, mobile, API, and database testing, as well as framework development.
Address a variety of testing methodologies and tailoring the discussion to the problem at hand.
Mention integration with build and deployment pipelines or version control systems only if directly relevant to the question, emphasizing practical application.
Detail the end-to-end process where applicable, focusing on critical steps like planning, environment configuration, test case creation, script development, data handling, execution strategies, error management, and result reporting, all specific to the scenario.
For code-based answers, Dont write any comments only write production based code with executable print, to show output explain each line thoroughly, its purpose, and how it ensures robust automation, scalability, maintainability, and quality assurance, tying back to the "project_details" as needed.
Tailor your explanation to the question, showcasing your comprehensive knowledge across all aspects of testing and your technical depth as a Senior Python Automation Engineer.
"""

  messages = [
        {"role": "system", "content": prompt},
        {"role": "system", "content": f"Project Details: {project_details}"}
    ]
  while not stop_event.is_set():
      try:
          transcript = transcript_queue.get(timeout=0.1)
          response_queue.put(transcript)  # Display the user message first

          # Add the new question to the last_questions deque
          if transcript["role"] == "user":
              last_questions.append(transcript["content"])

          # Include the last 2 questions in the context
          context = list(last_questions)
          context_message = {"role": "system", "content": f"Previous questions: {' | '.join(context)}"}
          messages.append(context_message)
          messages.append(transcript)

          # Save the question to file
          if transcript["role"] == "user":
              write_question_to_file(transcript["content"])
          
          response = client.chat.completions.create(
              model='gpt-4o-mini',
              messages=messages,
              stream=True
          )
          collected_text = ""
          for chunk in response:
              if stop_event.is_set():
                  break  # Exit the loop immediately if stop_event is set
              if chunk.choices[0].delta.content is not None:
                  text = chunk.choices[0].delta.content
                  collected_text += text
                  # Send immediately for real-time display
                  response_queue.put({"role": "ai", "content": collected_text})
          response_queue.put({"role": "ai", "content": "END_OF_RESPONSE"})
          messages.append({"role": "assistant", "content": collected_text})
          if len(messages) > 12:  # Limit context to last 10 messages (including the 2 context messages)
              messages = messages[-12:]
      except queue.Empty:
          continue
      buffered_transcript = ""  # Clear the buffer after handling responses

@app.get('/')
async def index():
  try:
      with open(os.path.join(os.path.dirname(__file__), 'templates', 'index.html'), 'r', encoding='utf-8') as file:
          html_code = file.read()
      return Response(content=html_code, media_type="text/html")
  except Exception as e:
      logging.error(f"Error reading frontend.html: {e}")
      return JSONResponse(content={'error': 'Failed to load the front-end'}, status_code=500)

@app.post('/start_transcription')
async def start_transcription():
  global transcriber, is_transcribing
  is_transcribing = True
  if transcriber is None:
      executor.submit(handle_transcription)
  return JSONResponse(content={'status': 'transcription started'})

@app.post('/stop_transcription')
async def stop_transcription():
  global transcriber, buffered_transcript, is_transcribing, last_questions
  is_transcribing = False
  if buffered_transcript.strip():
      transcript_queue.put({"role": "user", "content": buffered_transcript.strip()})
      transcripts.append(buffered_transcript.strip())
      last_questions.append(buffered_transcript.strip())
      write_question_to_file(buffered_transcript.strip())
  buffered_transcript = ""  # Clear the buffer after stopping transcription
  return JSONResponse(content={'status': 'transcription stopped'})

@app.post('/send_to_ai')
async def send_to_ai():
  global stop_event, response_thread
  stop_event.clear()
  response_thread = threading.Thread(target=handle_responses)
  response_thread.daemon = True
  response_thread.start()
  return JSONResponse(content={'status': 'sent to AI'})

@app.post('/stop_ai_response')
async def stop_ai_response():
  global stop_event, response_thread, is_transcribing
  stop_event.set()
  if response_thread is not None:
      response_thread.join(timeout=0.1)  # Use a shorter timeout to avoid blocking for too long
  is_transcribing = True  # Start transcription after stopping AI response
  if transcriber is None:
      executor.submit(handle_transcription)
  return JSONResponse(content={'status': 'AI response stopped and transcription started'})

@app.get('/get_response')
async def get_response():
  def generate():
      while True:
          try:
              response_text = response_queue.get(timeout=0.01)  # Much faster timeout
              yield f"data: {json.dumps(response_text)}\n\n"
          except queue.Empty:
              continue
  return StreamingResponse(generate(), media_type='text/event-stream')



def signal_handler(sig, frame):
  global stop_event, response_thread
  print('Interrupt received, stopping...')
  stop_event.set()
  if response_thread is not None:
      response_thread.join(timeout=0.1)  # Use a shorter timeout to avoid blocking for too long
  sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

if __name__ == '__main__':
  try:
    uvicorn.run(app, host="127.0.0.1", port=5002)
  except KeyboardInterrupt:
    print("Server stopped gracefully")
  except Exception as e:
    print(f"Server error: {e}")
  finally:
    print("Cleanup complete")