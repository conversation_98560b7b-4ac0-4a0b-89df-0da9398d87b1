2024-08-27 11:28:48.613050: can you explain me can you explain me how did you can you explain me how did you find can you explain me how did you find the can you explain me how did you find the outlay can you explain me how did you find the outlays can you explain me how did you find the outlays in your first can you explain me how did you find the outlays in your first project can you explain me how did you find the outlays in your first project like Can you explain me? How did you find the outlays in your first project like. in the in the image in the image detection In the image detection. and and in the wast and in the waste
2024-08-27 11:28:48.619182: can you explain me can you explain me how did you can you explain me how did you find can you explain me how did you find the can you explain me how did you find the outlay can you explain me how did you find the outlays can you explain me how did you find the outlays in your first can you explain me how did you find the outlays in your first project can you explain me how did you find the outlays in your first project like Can you explain me? How did you find the outlays in your first project like. in the in the image in the image detection In the image detection. and and in the wast and in the waste
2024-08-27 11:29:43.596868: and in the waste management And in the waste management. how did you how did you find how did you find the how did you find the outlays how did you find the outlays in the imag how did you find the outlays in the images how did you find the outlays in the images on what how did you find the outlays in the images on what techniques how did you find the outlays in the images on what techniques you have how did you find the outlays in the images on what techniques you have used How did you find the outlays in the images on what techniques you have used? in your in your first in your first project In your first project.
2024-08-27 11:29:43.596868: and in the waste management And in the waste management. how did you how did you find how did you find the how did you find the outlays how did you find the outlays in the imag how did you find the outlays in the images how did you find the outlays in the images on what how did you find the outlays in the images on what techniques how did you find the outlays in the images on what techniques you have how did you find the outlays in the images on what techniques you have used How did you find the outlays in the images on what techniques you have used? in your in your first in your first project In your first project.
2024-08-27 11:41:17.052739: hello Hello. good morning good morning how much yeah good morning how much yeah hi good morning how much yeah hi gumpa good morning how much yeah hi gumpa good good morning how much yeah hi gumpa good morning Good morning. How much? Yeah. Hi, Gumpa. Good morning. let's get let's get started let's get started yeah let's get started yeah sure Let's get started. Yeah, sure. hi hi kara hi kara do you hi kara do you want hi kara do you want nothing hi kara do you want nothing no pali hi kara do you want nothing no palikin hi kara do you want nothing no palikin go ahead hi kara do you want nothing no palikin go ahead yeah hi kara do you want nothing no palikin go ahead yeah so hi kara do you want nothing no palikin go ahead yeah so amar hi kara do you want nothing no palikin go ahead yeah so amar just hi kara do you want nothing no palikin go ahead yeah so amar just give you hi kara do you want nothing no palikin go ahead yeah so amar just give you a short hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll go for hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll go for question answer hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll go for question answer okay fine Hi, Kara. Do you want nothing? No. Palikin? Go ahead. Yeah, so, Amar just give you a. Short introduction, then we'll go for question answer. Okay, fine. i'm having i'm having muscle form i'm having muscle former and i'm having muscle former and then i have i'm having muscle former and then i have total i'm having muscle former and then i have totally eight point i'm having muscle former and then i have totally eight point one i'm having muscle former and then i have totally eight point one years of experience i'm having muscle former and then i have totally eight point one years of experience ninety i'm having muscle former and then i have totally eight point one years of experience ninety and i'm having muscle former and then i have totally eight point one years of experience ninety and then i'm having muscle former and then i have totally eight point one years of experience ninety and then i have i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with py i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and py i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk ten i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflow i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows bas i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of things i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of things for i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of things for data I'm having muscle former and then I have totally 8.1 years of experience. 90. And then I have nearly four plus experience with the data science and AML I worked with mostly with Python, like the library school and Python Sci-Fi NLTK tensorflows basic and cyclone, those kind of things for data vis. visualization visualization i have visualization i have used like visualization i have used like ma visualization i have used like macroli visualization i have used like macrolip sib visualization i have used like macrolip sibon visualization i have used like macrolip sibon plotly visualization i have used like macrolip sibon plotly and power visualization i have used like macrolip sibon plotly and power bi Visualization I have used like macrolip Sibon plotly and power Bi. and and work and work with the one and work with the one statical anal and work with the one statical analysis and work with the one statical analysis unlike and work with the one statical analysis unlike including and work with the one statical analysis unlike including the hypoth and work with the one statical analysis unlike including the hypothes and work with the one statical analysis unlike including the hypothesis and probability and work with the one statical analysis unlike including the hypothesis and probability theory and work with the one statical analysis unlike including the hypothesis and probability theory electrode and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in mach and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning i and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning i worked and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning i worked with And work with the one statical analysis unlike including the hypothesis and probability theory, electrodesion analysis and the risk assessment, those kind of things. Also, I worked on in machine learning, I worked with. projects projects involved projects involved with projects involved with the classification projects involved with the classification clust projects involved with the classification clustering and projects involved with the classification clustering and recommend projects involved with the classification clustering and recommendation system projects involved with the classification clustering and recommendation systems projects involved with the classification clustering and recommendation systems and projects involved with the classification clustering and recommendation systems and submitting method projects involved with the classification clustering and recommendation systems and submitting methods projects involved with the classification clustering and recommendation systems and submitting methods and projects involved with the classification clustering and recommendation systems and submitting methods and those projects involved with the classification clustering and recommendation systems and submitting methods and those things also projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like kera projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tenso projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflow projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and py projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv those things projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv those things i mostly projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv those things i mostly work with Projects involved with the classification, clustering and recommendation systems and submitting methods and those things also have worked on, like keras tensorflows, basic bird and pytorch Opencv those things I mostly work with. and and i worked and i worked on one and i worked on one ap and i worked on one apa and i worked on one apa development and i worked on one apa development part of and i worked on one apa development part of the project and i worked on one apa development part of the project actually and i worked on one apa development part of the project actually that's also and i worked on one apa development part of the project actually that's also in databases and i worked on one apa development part of the project actually that's also in databases i worked with and i worked on one apa development part of the project actually that's also in databases i worked with mysql and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and no and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and no scale data and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and no scale databases And I worked on one APA development, part of the project. Actually, that's also in databases. I worked with MySQL and SQL servers and no scale databases. in In. actually actually i'll do the anal actually i'll do the analysis actually i'll do the analysis part actually i'll do the analysis part and actually i'll do the analysis part and coming to actually i'll do the analysis part and coming to our actually i'll do the analysis part and coming to our recent project actually i'll do the analysis part and coming to our recent project like Actually, I'll do the analysis part. And coming to our recent project, like. we have develop we have developed a system we have developed a system that we have developed a system that we could we have developed a system that we could quickly ident we have developed a system that we could quickly identify we have developed a system that we could quickly identify and we have developed a system that we could quickly identify and supply we have developed a system that we could quickly identify and supply the we have developed a system that we could quickly identify and supply the different We have developed a system that we could quickly identify and supply the different. plastic plastic types when plastic types when they are mix plastic types when they are mixed together plastic types when they are mixed together and also plastic types when they are mixed together and also ident plastic types when they are mixed together and also identify the chem plastic types when they are mixed together and also identify the chemical cont plastic types when they are mixed together and also identify the chemical contamina plastic types when they are mixed together and also identify the chemical contamination plastic types when they are mixed together and also identify the chemical contamination have or not plastic types when they are mixed together and also identify the chemical contamination have or not in plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic object plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the feature plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and pattern plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plast plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plastic type plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plastic type so Plastic types when they are mixed together and also identify the chemical contamination, have or not in the plastic objects. So we need to find the features and patterns and distribution between the plastic type. So. that's what that's what happened That's what happened. what is what is a what is a queries what is a queries like to efficient what is a queries like to efficiently rec what is a queries like to efficiently recycle the what is a queries like to efficiently recycle the waste what is a queries like to efficiently recycle the waste management what is a queries like to efficiently recycle the waste management and to decrease what is a queries like to efficiently recycle the waste management and to decrease the oper what is a queries like to efficiently recycle the waste management and to decrease the operational cost what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the rec what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last one which what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last one which you are what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last one which you are taking What is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling. So that's the main thing. So these are the total have done. If you want to explain further, I can go ahead and explain that process. Yeah. The last one, which you are taking classification. of of plastics of plastics right of plastics right yes Of plastics, right? Yes. okay Okay. can you can you tell me can you tell me more on can you tell me more on it can you tell me more on it okay fine Can you tell me more on it? Okay, fine. if you little bit if you little bit slow if you little bit slow your speed if you little bit slow your speed right if you little bit slow your speed right that would be if you little bit slow your speed right that would be good if you little bit slow your speed right that would be good okay if you little bit slow your speed right that would be good okay that if you little bit slow your speed right that would be good okay that's fine If you little bit slow your speed. Right, that would be good. Okay, that's fine. in between words in between words i in between words i am not able to in between words i am not able to understand in between words i am not able to understand okay in between words i am not able to understand okay slowly in between words i am not able to understand okay slowly yes in between words i am not able to understand okay slowly yes i have total in between words i am not able to understand okay slowly yes i have total kind of In between words I am not able to understand. Okay, slowly. Yes, I have total kind of. in in industrial in industrial waste in industrial waste like total in industrial waste like totally i in industrial waste like totally i have like in industrial waste like totally i have like eight types in industrial waste like totally i have like eight types of recy in industrial waste like totally i have like eight types of recycled plast in industrial waste like totally i have like eight types of recycled plastics will be in industrial waste like totally i have like eight types of recycled plastics will be there in industrial waste like totally i have like eight types of recycled plastics will be there the kind of in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd htt in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe p in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ld in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pp in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depend in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling kind in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling kind of pl in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling kind of plastic In industrial waste. Like, totally. I have, like, eight types of recycled plastics will be there. The kind of Pd, httpe, PVC, LDP, pps, depends upon the recycling kind of plastic. there there there there we need to there there we need to add in there there we need to add in the main there there we need to add in the main goal is like there there we need to add in the main goal is like some plastics there there we need to add in the main goal is like some plastics can be recyc there there we need to add in the main goal is like some plastics can be recyclable some there there we need to add in the main goal is like some plastics can be recyclable some plastics there there we need to add in the main goal is like some plastics can be recyclable some plastics are can't there there we need to add in the main goal is like some plastics can be recyclable some plastics are can't be recyclable There, there. We need to add in. The main goal is like, some plastics can be recyclable. Some plastics are. Can't be recyclable. and some within and some within the and some within the plastics And some within the plastics. they have they have kind of they have kind of chemical they have kind of chemical contaminat they have kind of chemical contamination also they have kind of chemical contamination also will be there they have kind of chemical contamination also will be there so they have kind of chemical contamination also will be there so in the they have kind of chemical contamination also will be there so in the conveyor they have kind of chemical contamination also will be there so in the conveyor belt they have kind of chemical contamination also will be there so in the conveyor belt when they have kind of chemical contamination also will be there so in the conveyor belt when going they have kind of chemical contamination also will be there so in the conveyor belt when going the objects they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify the they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify the object they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify the objects and then They have kind of chemical contamination also will be there. So in the conveyor belt when going the objects. So we need to identify the objects, and then. we have to disting we have to distinguish we have to distinguish like we have to distinguish like what kind of we have to distinguish like what kind of plastic we have to distinguish like what kind of plastic it is and we have to distinguish like what kind of plastic it is and then we have to distinguish like what kind of plastic it is and then we need to we have to distinguish like what kind of plastic it is and then we need to class we have to distinguish like what kind of plastic it is and then we need to classify we have to distinguish like what kind of plastic it is and then we need to classify them we have to distinguish like what kind of plastic it is and then we need to classify them based we have to distinguish like what kind of plastic it is and then we need to classify them based on the we have to distinguish like what kind of plastic it is and then we need to classify them based on the images we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of like general we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of like generally we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of like generally we'll get We have to distinguish, like, what kind of plastic it is, and then we need to classify them. Based on the images, actually. So what, we'll get the kind of, like, generally we'll. Get. the the source is the source is like we will get the source is like we will get kind of the source is like we will get kind of twenty the source is like we will get kind of twenty seconds of the source is like we will get kind of twenty seconds of video the source is like we will get kind of twenty seconds of video the conveyor bel the source is like we will get kind of twenty seconds of video the conveyor belt whenever the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to ident the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the imag the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of random the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the imag the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detect the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection al the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available which is the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available which is in open the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available which is in opencv The source is like, we will get kind of 20 seconds of video, the conveyor belt, whenever going with that. So we need to identify, we need to capture the images, which is going with instead of randomly taking the images, we have used the kind of motion detection algorithm like such a background subtraction and optical flow available, which is in OpenCV to. identify identify the identify the frames Identify the frames. we have we have implemented we have implemented like a frame we have implemented like a frame scoring system we have implemented like a frame scoring system as we have implemented like a frame scoring system as well We have implemented like, a frame scoring system as well. which had which had kind of which had kind of which had which had kind of which had object clar which had kind of which had object clarity is high which had kind of which had object clarity is high and over which had kind of which had object clarity is high and overlap which had kind of which had object clarity is high and overlap and which had kind of which had object clarity is high and overlap and the angle which had kind of which had object clarity is high and overlap and the angle cover which had kind of which had object clarity is high and overlap and the angle coverage Which had kind of, which had object clarity is high and overlap, and the angle coverage. it will give it will give the prior it will give the prioritize it will give the prioritize the frames actually it will give the prioritize the frames actually so it will give the prioritize the frames actually so which is it will give the prioritize the frames actually so which is at the highest it will give the prioritize the frames actually so which is at the highest score so it will give the prioritize the frames actually so which is at the highest score so then it will give the prioritize the frames actually so which is at the highest score so then it will it will give the prioritize the frames actually so which is at the highest score so then it will take that it will give the prioritize the frames actually so which is at the highest score so then it will take that imag it will give the prioritize the frames actually so which is at the highest score so then it will take that images actually It will give the prioritize the frames, actually. So which is at the highest score, so. Then it will take that images actually. from the from the video from the video so from the video so like that from the video so like that we need to from the video so like that we need to collect from the video so like that we need to collect the from the video so like that we need to collect the images and from the video so like that we need to collect the images and there has from the video so like that we need to collect the images and there has a limitation from the video so like that we need to collect the images and there has a limitation also we from the video so like that we need to collect the images and there has a limitation also we have to take from the video so like that we need to collect the images and there has a limitation also we have to take maximum from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen imag from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after the object from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after the object deduction from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after the object deduction done and From the video. So, like that, we need to collect the images. And there has a limitation also. We have to take maximum 16 images only. We have to take from ten to 16 images. We can take that images, not more than that. So after the object deduction done and. we have we have to do the we have to do the clarification we have to do the clarification part and then we have to do the clarification part and then we we have to do the clarification part and then we implement We have to do the clarification part and then we implement. module module like module like mask module like mask rlc module like mask rlcn like module like mask rlcn like this model module like mask rlcn like this model like mostly module like mask rlcn like this model like mostly why module like mask rlcn like this model like mostly why we choose module like mask rlcn like this model like mostly why we choose this module like mask rlcn like this model like mostly why we choose this it's like module like mask rlcn like this model like mostly why we choose this it's like this model module like mask rlcn like this model like mostly why we choose this it's like this model have module like mask rlcn like this model like mostly why we choose this it's like this model have bounding module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise mark module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the same thing module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the same thing and then after module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the same thing and then after that Module, like mask, RLCN, like this model, like, mostly why we choose this. It's. Like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this. The same thing, and then after that. in in a in a single in a single image In a single image. we have like we have like one hundred we have like one hundred kind of we have like one hundred kind of in we have like one hundred kind of in conver we have like one hundred kind of in convert belt like we have like one hundred kind of in convert belt like one hundred objects we have like one hundred kind of in convert belt like one hundred objects will be we have like one hundred kind of in convert belt like one hundred objects will be there so we have like one hundred kind of in convert belt like one hundred objects will be there so each we have like one hundred kind of in convert belt like one hundred objects will be there so each object we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be different we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding box we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with pretrial we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with pretrial models we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with pretrial models like that We have like, 100 kind of in convert belt. Like 100 objects will be there. So each object will be differentiated by the bonding boxes, actually. So, like that, we need to try the model and. After that we are done with the efficient turn. It also like to transfer learning with pretrial. Models like that. and then and then we need to and then we need to identify and then we need to identify the adjusting and then we need to identify the adjusting the lay and then we need to identify the adjusting the layers and then we need to identify the adjusting the layers like better and then we need to identify the adjusting the layers like better to capture and then we need to identify the adjusting the layers like better to capture the features and then we need to identify the adjusting the layers like better to capture the features like kind of and then we need to identify the adjusting the layers like better to capture the features like kind of specific and then we need to identify the adjusting the layers like better to capture the features like kind of specific cover And then we need to identify the adjusting the layers like better to capture the features like kind of specific cover. do you have used do you have used like do you have used like efficient do you have used like efficient net Do you have used, like efficient net? so so after so after that So after that. we have we have faced kind of we have faced kind of out we have faced kind of outlays we have faced kind of outlays like we have faced kind of outlays like the imag we have faced kind of outlays like the images have we have faced kind of outlays like the images have kind of we have faced kind of outlays like the images have kind of while going We have faced kind of outlays like the images have kind of while going. with the with the movement with the movement actually with the movement actually we can't take with the movement actually we can't take the with the movement actually we can't take the clarity with the movement actually we can't take the clarity imag with the movement actually we can't take the clarity images with the movement actually we can't take the clarity images so we have with the movement actually we can't take the clarity images so we have kind of with the movement actually we can't take the clarity images so we have kind of outliers with the movement actually we can't take the clarity images so we have kind of outliers data with the movement actually we can't take the clarity images so we have kind of outliers data so with the movement actually we can't take the clarity images so we have kind of outliers data so that with the movement actually we can't take the clarity images so we have kind of outliers data so that's with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blur with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry imag with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry images so with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry images so we applied with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry images so we applied kind of With the movement. Actually, we can't take the clarity images, so we have kind of outliers data. So that's finding that blurry images. So we applied kind of. some some techniques to some techniques to improve some techniques to improve that some techniques to improve that deep learning Some techniques to improve that deep learning. images images we use like images we use like binary images we use like binary filtering images we use like binary filtering and images we use like binary filtering and motion images we use like binary filtering and motion deep blur images we use like binary filtering and motion deep blurring techniques images we use like binary filtering and motion deep blurring techniques like which images we use like binary filtering and motion deep blurring techniques like which is available in images we use like binary filtering and motion deep blurring techniques like which is available in open images we use like binary filtering and motion deep blurring techniques like which is available in opencv images we use like binary filtering and motion deep blurring techniques like which is available in opencv so images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have worked with images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have worked with estimated images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have worked with estimated like Images we use like binary filtering and motion deep blurring techniques like, which is available in OpenCV so, like, we have worked with estimated, like, blur ke blur kernel blur kernel and blur kernel and reversing blur kernel and reversing that blur kernel and reversing that blurring blur kernel and reversing that blurring process blur kernel and reversing that blurring process and blur kernel and reversing that blurring process and sharpening blur kernel and reversing that blurring process and sharpening the blur kernel and reversing that blurring process and sharpening the images blur kernel and reversing that blurring process and sharpening the images that's the thing blur kernel and reversing that blurring process and sharpening the images that's the thing where we blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done after that blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done after that we are done blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done after that we are done with Blur kernel and reversing that blurring process and sharpening the images. That's the thing where we have done after that, we are done with. one one more one more deep learning one more deep learning technique like one more deep learning technique like residual One more deep learning technique like residual. neural neural network neural network that's what neural network that's what we have done neural network that's what we have done is here neural network that's what we have done is here is neural network that's what we have done is here is like neural network that's what we have done is here is like the same neural network that's what we have done is here is like the same images neural network that's what we have done is here is like the same images with neural network that's what we have done is here is like the same images with a low quality neural network that's what we have done is here is like the same images with a low quality image neural network that's what we have done is here is like the same images with a low quality image and then neural network that's what we have done is here is like the same images with a low quality image and then with the same imag neural network that's what we have done is here is like the same images with a low quality image and then with the same image with neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provide neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both the data neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both the data and neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both the data and then Neural network. That's what we have done is here is like the same images with a low quality image and then with the same image with high quality image. So like that we provided with both the data and then. make it make it done make it done that the same make it done that the same with make it done that the same with a low resol make it done that the same with a low resolution with the make it done that the same with a low resolution with the high resolution make it done that the same with a low resolution with the high resolution so Make it done. That the same with a low resolution. With the high resolution. So. we're trying we're trying to update we're trying to update the up we're trying to update the upscale we're trying to update the upscale that images we're trying to update the upscale that images enhance we're trying to update the upscale that images enhancements we're trying to update the upscale that images enhancements resolution we're trying to update the upscale that images enhancements resolutions we're trying to update the upscale that images enhancements resolutions so we're trying to update the upscale that images enhancements resolutions so what it will we're trying to update the upscale that images enhancements resolutions so what it will do is we're trying to update the upscale that images enhancements resolutions so what it will do is like we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality imag we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it autom we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enh we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhance we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to high res we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to high resolution we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to high resolution generally We're trying to update the upscale that images, enhancements, resolutions. So what it will do? Is like whenever you get the image with the low quality image, like, it automatically enhances that. Resolution to high resolution generally. we can we can make it we can make it the threshold we can make it the threshold values we can make it the threshold values as like we can make it the threshold values as like nineteen we can make it the threshold values as like nineteen twenty we can make it the threshold values as like nineteen twenty by We can make it the threshold values as like 1920 by. twelve twenty twelve twenty like that twelve twenty like that based twelve twenty like that based on twelve twenty like that based on the twelve twenty like that based on the pixel twelve twenty like that based on the pixel sizes twelve twenty like that based on the pixel sizes actually so twelve twenty like that based on the pixel sizes actually so like that twelve twenty like that based on the pixel sizes actually so like that we twelve twenty like that based on the pixel sizes actually so like that we define twelve twenty like that based on the pixel sizes actually so like that we define some kind twelve twenty like that based on the pixel sizes actually so like that we define some kind of twelve twenty like that based on the pixel sizes actually so like that we define some kind of value twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatic twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enh twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that we have twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that we have one more twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that we have one more problem 1220 like that based on the pixel sizes, actually. So, like that we define some kind of. Value there. So whichever coming that low to that and then we have to and it will automatically. Enhance the images and everything like that. We hang in. After that, we have one more problem. is is like overlap is like overlapping is like overlapping the objects is like overlapping the objects like is like overlapping the objects like in is like overlapping the objects like in a single is like overlapping the objects like in a single object is like overlapping the objects like in a single object it looks like is like overlapping the objects like in a single object it looks like a single is like overlapping the objects like in a single object it looks like a single object is like overlapping the objects like in a single object it looks like a single object but Is like overlapping the objects, like in a single object. It looks like a single object, but. it had like it had like three it had like three or four objects it had like three or four objects combined it had like three or four objects combined together it had like three or four objects combined together it looks like it had like three or four objects combined together it looks like as a single it had like three or four objects combined together it looks like as a single object it had like three or four objects combined together it looks like as a single object so it had like three or four objects combined together it looks like as a single object so that's also it had like three or four objects combined together it looks like as a single object so that's also we have it had like three or four objects combined together it looks like as a single object so that's also we have faced the it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non ma it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum separation techn it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum separation techniques it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum separation techniques so that It had, like, three or four objects combined together, it looks like as a single object, so that's also we have faced the same thing, so implemented like a soft nms, like non maximum separation techniques so that. what you do what you do is like it what you do is like it reduce what you do is like it reduce the what you do is like it reduce the confidence what you do is like it reduce the confidence codes What you do is like it. Reduce the confidence codes. and whenever And whenever. it detect it detected overlap it detected overlapping det it detected overlapping detection It detected overlapping detection. like within like within the object like within the object like like within the object like whenever like within the object like whenever there's sharpening like within the object like whenever there's sharpening imag like within the object like whenever there's sharpening images not like within the object like whenever there's sharpening images not detected like within the object like whenever there's sharpening images not detected it automatically like within the object like whenever there's sharpening images not detected it automatically move like within the object like whenever there's sharpening images not detected it automatically move to like within the object like whenever there's sharpening images not detected it automatically move to that like within the object like whenever there's sharpening images not detected it automatically move to that will consider like within the object like whenever there's sharpening images not detected it automatically move to that will consider as Like within the object, like whenever there's sharpening images not detected, it automatically move to that will consider as. there is there is no kind of there is no kind of boundary there is no kind of boundary lines there is no kind of boundary lines will be There is no kind of boundary lines will be. not very not very clear not very clear so not very clear so in that time Not very clear. So in that time, that that object that object will consider that object will consider as that object will consider as like object that object will consider as like object in object that object will consider as like object in object and then that object will consider as like object in object and then this model that object will consider as like object in object and then this model will apply that object will consider as like object in object and then this model will apply like that object will consider as like object in object and then this model will apply like software that object will consider as like object in object and then this model will apply like software ms that object will consider as like object in object and then this model will apply like software ms so that object will consider as like object in object and then this model will apply like software ms so it will go that object will consider as like object in object and then this model will apply like software ms so it will go with that object will consider as like object in object and then this model will apply like software ms so it will go with mostly that object will consider as like object in object and then this model will apply like software ms so it will go with mostly with That object will consider as like object in object, and then this model will apply like software. Ms. So it will go with mostly with. conver converted neural converted neural networks converted neural networks what converted neural networks what it do is converted neural networks what it do is like it will converted neural networks what it do is like it will give the sharpness converted neural networks what it do is like it will give the sharpness of the converted neural networks what it do is like it will give the sharpness of the image converted neural networks what it do is like it will give the sharpness of the image it improves converted neural networks what it do is like it will give the sharpness of the image it improves the detection converted neural networks what it do is like it will give the sharpness of the image it improves the detection of the accur converted neural networks what it do is like it will give the sharpness of the image it improves the detection of the accuracy Converted neural networks. What it do is like it will give the sharpness of the image. It improves the detection of the accuracy. like that like that it will go like that it will go with like that it will go with it like that it will go with it after that like that it will go with it after that we like that it will go with it after that we face like like that it will go with it after that we face like chemical like that it will go with it after that we face like chemical detection like that it will go with it after that we face like chemical detection like like that it will go with it after that we face like chemical detection like most like that it will go with it after that we face like chemical detection like mostly with an like that it will go with it after that we face like chemical detection like mostly with an army detect like that it will go with it after that we face like chemical detection like mostly with an army detection by using like that it will go with it after that we face like chemical detection like mostly with an army detection by using the senso like that it will go with it after that we face like chemical detection like mostly with an army detection by using the sensors and like that it will go with it after that we face like chemical detection like mostly with an army detection by using the sensors and with the imag like that it will go with it after that we face like chemical detection like mostly with an army detection by using the sensors and with the image data Like that, it will go with it. After that, we face, like, chemical detection, like mostly with an army detection by using the sensors and with the image data. like in the like in the chemical like in the chemical data like in the chemical data contamina like in the chemical data contamination is like in the chemical data contamination is there or not like in the chemical data contamination is there or not we have to like in the chemical data contamination is there or not we have to identify like in the chemical data contamination is there or not we have to identify with Like in the chemical data. Contamination is there or not? We have to identify with. while going while going with the while going with the conveyor while going with the conveyor belt while going with the conveyor belt we have while going with the conveyor belt we have ident while going with the conveyor belt we have identified While going with the conveyor belt, we have identified. integration integration of the real integration of the real time integration of the real time chemical integration of the real time chemical sensors integration of the real time chemical sensors with the imag integration of the real time chemical sensors with the image integration of the real time chemical sensors with the image analysis techn integration of the real time chemical sensors with the image analysis techniques integration of the real time chemical sensors with the image analysis techniques both actually integration of the real time chemical sensors with the image analysis techniques both actually so integration of the real time chemical sensors with the image analysis techniques both actually so we have integration of the real time chemical sensors with the image analysis techniques both actually so we have used Integration of the real time chemical sensors with the image analysis techniques. Both, actually. So we have used. like like ph like pho m like pho mqt like pho mqtds like pho mqtds library like pho mqtds library will be there in like pho mqtds library will be there in the python like pho mqtds library will be there in the python so what like pho mqtds library will be there in the python so what it like pho mqtds library will be there in the python so what it do is like pho mqtds library will be there in the python so what it do is like it like pho mqtds library will be there in the python so what it do is like it will communic like pho mqtds library will be there in the python so what it do is like it will communicate like pho mqtds library will be there in the python so what it do is like it will communicate with the like pho mqtds library will be there in the python so what it do is like it will communicate with the senso like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors senso like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an i like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot dev like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rp like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rpa and like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rpa and the like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rpa and the taraso Like pho MqtDS library will be there in the python. So what it do? Is like it will communicate with the sensors. Sensor data. It had an IoT device, so we have uses the RPA and the Taraso. it will give it will give so it will give so gpio it will give so gpio pins it will give so gpio pins will be it will give so gpio pins will be there it will give so gpio pins will be there so it will give so gpio pins will be there so that whatever It will give. So Gpio pins will be there so that whatever. the the sensor the sensor will the sensor will trigger The sensor will trigger. but the python but the python is but the python is identified But the python is identified. by By. particular particular techn particular technique particular technique with particular technique with the raso particular technique with the raso it will particular technique with the raso it will give particular technique with the raso it will give that there particular technique with the raso it will give that there is a message particular technique with the raso it will give that there is a message will particular technique with the raso it will give that there is a message will be sent particular technique with the raso it will give that there is a message will be sent to particular technique with the raso it will give that there is a message will be sent to our particular technique with the raso it will give that there is a message will be sent to our library particular technique with the raso it will give that there is a message will be sent to our library so mq particular technique with the raso it will give that there is a message will be sent to our library so mqtt so particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue the message particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue the message and particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue the message and whenever Particular technique with the Raso it will give that there is a message will be sent to our library. So. Mqtt so what it uses, like simply it will queue the message and whenever. sometimes sometimes in sometimes in a canv sometimes in a canville sometimes in a canville continuously sometimes in a canville continuously it will give sometimes in a canville continuously it will give the arms sometimes in a canville continuously it will give the arms like sometimes in a canville continuously it will give the arms like four or five times sometimes in a canville continuously it will give the arms like four or five times it sometimes in a canville continuously it will give the arms like four or five times it will give sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mq sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqt sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechan sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechanism sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechanism after sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechanism after that Sometimes in a canville, continuously. It will give the arms like four or five times, it will. Give the trigger. So that's why we have used this MQTT mechanism after that. once it is once it is done and then once it is done and then we train once it is done and then we train the model like once it is done and then we train the model like whichever once it is done and then we train the model like whichever contain once it is done and then we train the model like whichever contains once it is done and then we train the model like whichever contains chem once it is done and then we train the model like whichever contains chemical once it is done and then we train the model like whichever contains chemical contam once it is done and then we train the model like whichever contains chemical contamination object once it is done and then we train the model like whichever contains chemical contamination objects and once it is done and then we train the model like whichever contains chemical contamination objects and it will compare once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have class once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that we once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that we gone with once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that we gone with that data Once it is done. And then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination, is there not? Like that we have classified that also. And after that, like this we have done and then we mostly with the data. After that, we gone with that data. processing techn processing techniques and processing techniques and everything after processing techniques and everything after that processing techniques and everything after that we processing techniques and everything after that we test processing techniques and everything after that we test the things processing techniques and everything after that we test the things like processing techniques and everything after that we test the things like we processing techniques and everything after that we test the things like we drag the processing techniques and everything after that we test the things like we drag the tools with processing techniques and everything after that we test the things like we drag the tools with the tenso processing techniques and everything after that we test the things like we drag the tools with the tensor board processing techniques and everything after that we test the things like we drag the tools with the tensor board which processing techniques and everything after that we test the things like we drag the tools with the tensor board which is processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparame processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tun processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like that we processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like that we have in processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like that we have in these Processing techniques and everything after that, we test the things like we drag the tools with the tensor. Board, which is provide the various interface to monitor metrics such as loss, accuracy and other hyperparameter tune techniques we have used like that we have in these. there were there were basically two there were basically two classes there were basically two classes right there were basically two classes right one there were basically two classes right one is There were basically two classes, right? One is. degradable degradable plast degradable plastic or degradable plastic or it is degradable plastic or it is non degradable Degradable plastic or it is non degradable. plastics plastics within plastics within the pl plastics within the plastics itself plastics within the plastics itself there plastics within the plastics itself there are multip plastics within the plastics itself there are multiple plastics within the plastics itself there are multiple actually plastics within the plastics itself there are multiple actually they have Plastics. Within the plastics itself. There are multiple, actually. They have. like like eight kind of like eight kind of plastic like eight kind of plastic basically like eight kind of plastic basically the like eight kind of plastic basically the purpose of like eight kind of plastic basically the purpose of this exerci like eight kind of plastic basically the purpose of this exercise like eight kind of plastic basically the purpose of this exercise is like eight kind of plastic basically the purpose of this exercise is to like eight kind of plastic basically the purpose of this exercise is to identify like eight kind of plastic basically the purpose of this exercise is to identify the like eight kind of plastic basically the purpose of this exercise is to identify the plast like eight kind of plastic basically the purpose of this exercise is to identify the plastic which like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be rec like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled
2024-08-27 11:41:17.054883: hello Hello. good morning good morning how much yeah good morning how much yeah hi good morning how much yeah hi gumpa good morning how much yeah hi gumpa good good morning how much yeah hi gumpa good morning Good morning. How much? Yeah. Hi, Gumpa. Good morning. let's get let's get started let's get started yeah let's get started yeah sure Let's get started. Yeah, sure. hi hi kara hi kara do you hi kara do you want hi kara do you want nothing hi kara do you want nothing no pali hi kara do you want nothing no palikin hi kara do you want nothing no palikin go ahead hi kara do you want nothing no palikin go ahead yeah hi kara do you want nothing no palikin go ahead yeah so hi kara do you want nothing no palikin go ahead yeah so amar hi kara do you want nothing no palikin go ahead yeah so amar just hi kara do you want nothing no palikin go ahead yeah so amar just give you hi kara do you want nothing no palikin go ahead yeah so amar just give you a short hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll go for hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll go for question answer hi kara do you want nothing no palikin go ahead yeah so amar just give you a short introduction then we'll go for question answer okay fine Hi, Kara. Do you want nothing? No. Palikin? Go ahead. Yeah, so, Amar just give you a. Short introduction, then we'll go for question answer. Okay, fine. i'm having i'm having muscle form i'm having muscle former and i'm having muscle former and then i have i'm having muscle former and then i have total i'm having muscle former and then i have totally eight point i'm having muscle former and then i have totally eight point one i'm having muscle former and then i have totally eight point one years of experience i'm having muscle former and then i have totally eight point one years of experience ninety i'm having muscle former and then i have totally eight point one years of experience ninety and i'm having muscle former and then i have totally eight point one years of experience ninety and then i'm having muscle former and then i have totally eight point one years of experience ninety and then i have i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with py i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and py i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk ten i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflow i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows bas i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of things i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of things for i'm having muscle former and then i have totally eight point one years of experience ninety and then i have nearly four plus experience with the data science and aml i worked with mostly with python like the library school and python sci fi nltk tensorflows basic and cyclone those kind of things for data I'm having muscle former and then I have totally 8.1 years of experience. 90. And then I have nearly four plus experience with the data science and AML I worked with mostly with Python, like the library school and Python Sci-Fi NLTK tensorflows basic and cyclone, those kind of things for data vis. visualization visualization i have visualization i have used like visualization i have used like ma visualization i have used like macroli visualization i have used like macrolip sib visualization i have used like macrolip sibon visualization i have used like macrolip sibon plotly visualization i have used like macrolip sibon plotly and power visualization i have used like macrolip sibon plotly and power bi Visualization I have used like macrolip Sibon plotly and power Bi. and and work and work with the one and work with the one statical anal and work with the one statical analysis and work with the one statical analysis unlike and work with the one statical analysis unlike including and work with the one statical analysis unlike including the hypoth and work with the one statical analysis unlike including the hypothes and work with the one statical analysis unlike including the hypothesis and probability and work with the one statical analysis unlike including the hypothesis and probability theory and work with the one statical analysis unlike including the hypothesis and probability theory electrode and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in mach and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning i and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning i worked and work with the one statical analysis unlike including the hypothesis and probability theory electrodesion analysis and the risk assessment those kind of things also i worked on in machine learning i worked with And work with the one statical analysis unlike including the hypothesis and probability theory, electrodesion analysis and the risk assessment, those kind of things. Also, I worked on in machine learning, I worked with. projects projects involved projects involved with projects involved with the classification projects involved with the classification clust projects involved with the classification clustering and projects involved with the classification clustering and recommend projects involved with the classification clustering and recommendation system projects involved with the classification clustering and recommendation systems projects involved with the classification clustering and recommendation systems and projects involved with the classification clustering and recommendation systems and submitting method projects involved with the classification clustering and recommendation systems and submitting methods projects involved with the classification clustering and recommendation systems and submitting methods and projects involved with the classification clustering and recommendation systems and submitting methods and those projects involved with the classification clustering and recommendation systems and submitting methods and those things also projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like kera projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tenso projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflow projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and py projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv those things projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv those things i mostly projects involved with the classification clustering and recommendation systems and submitting methods and those things also have worked on like keras tensorflows basic bird and pytorch opencv those things i mostly work with Projects involved with the classification, clustering and recommendation systems and submitting methods and those things also have worked on, like keras tensorflows, basic bird and pytorch Opencv those things I mostly work with. and and i worked and i worked on one and i worked on one ap and i worked on one apa and i worked on one apa development and i worked on one apa development part of and i worked on one apa development part of the project and i worked on one apa development part of the project actually and i worked on one apa development part of the project actually that's also and i worked on one apa development part of the project actually that's also in databases and i worked on one apa development part of the project actually that's also in databases i worked with and i worked on one apa development part of the project actually that's also in databases i worked with mysql and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and no and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and no scale data and i worked on one apa development part of the project actually that's also in databases i worked with mysql and sql servers and no scale databases And I worked on one APA development, part of the project. Actually, that's also in databases. I worked with MySQL and SQL servers and no scale databases. in In. actually actually i'll do the anal actually i'll do the analysis actually i'll do the analysis part actually i'll do the analysis part and actually i'll do the analysis part and coming to actually i'll do the analysis part and coming to our actually i'll do the analysis part and coming to our recent project actually i'll do the analysis part and coming to our recent project like Actually, I'll do the analysis part. And coming to our recent project, like. we have develop we have developed a system we have developed a system that we have developed a system that we could we have developed a system that we could quickly ident we have developed a system that we could quickly identify we have developed a system that we could quickly identify and we have developed a system that we could quickly identify and supply we have developed a system that we could quickly identify and supply the we have developed a system that we could quickly identify and supply the different We have developed a system that we could quickly identify and supply the different. plastic plastic types when plastic types when they are mix plastic types when they are mixed together plastic types when they are mixed together and also plastic types when they are mixed together and also ident plastic types when they are mixed together and also identify the chem plastic types when they are mixed together and also identify the chemical cont plastic types when they are mixed together and also identify the chemical contamina plastic types when they are mixed together and also identify the chemical contamination plastic types when they are mixed together and also identify the chemical contamination have or not plastic types when they are mixed together and also identify the chemical contamination have or not in plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic object plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the feature plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and pattern plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plast plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plastic type plastic types when they are mixed together and also identify the chemical contamination have or not in the plastic objects so we need to find the features and patterns and distribution between the plastic type so Plastic types when they are mixed together and also identify the chemical contamination, have or not in the plastic objects. So we need to find the features and patterns and distribution between the plastic type. So. that's what that's what happened That's what happened. what is what is a what is a queries what is a queries like to efficient what is a queries like to efficiently rec what is a queries like to efficiently recycle the what is a queries like to efficiently recycle the waste what is a queries like to efficiently recycle the waste management what is a queries like to efficiently recycle the waste management and to decrease what is a queries like to efficiently recycle the waste management and to decrease the oper what is a queries like to efficiently recycle the waste management and to decrease the operational cost what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the rec what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last one which what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last one which you are what is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling so that's the main thing so these are the total have done if you want to explain further i can go ahead and explain that process yeah the last one which you are taking What is a queries like to efficiently recycle the waste management and to decrease the operational cost while before going to the recycling. So that's the main thing. So these are the total have done. If you want to explain further, I can go ahead and explain that process. Yeah. The last one, which you are taking classification. of of plastics of plastics right of plastics right yes Of plastics, right? Yes. okay Okay. can you can you tell me can you tell me more on can you tell me more on it can you tell me more on it okay fine Can you tell me more on it? Okay, fine. if you little bit if you little bit slow if you little bit slow your speed if you little bit slow your speed right if you little bit slow your speed right that would be if you little bit slow your speed right that would be good if you little bit slow your speed right that would be good okay if you little bit slow your speed right that would be good okay that if you little bit slow your speed right that would be good okay that's fine If you little bit slow your speed. Right, that would be good. Okay, that's fine. in between words in between words i in between words i am not able to in between words i am not able to understand in between words i am not able to understand okay in between words i am not able to understand okay slowly in between words i am not able to understand okay slowly yes in between words i am not able to understand okay slowly yes i have total in between words i am not able to understand okay slowly yes i have total kind of In between words I am not able to understand. Okay, slowly. Yes, I have total kind of. in in industrial in industrial waste in industrial waste like total in industrial waste like totally i in industrial waste like totally i have like in industrial waste like totally i have like eight types in industrial waste like totally i have like eight types of recy in industrial waste like totally i have like eight types of recycled plast in industrial waste like totally i have like eight types of recycled plastics will be in industrial waste like totally i have like eight types of recycled plastics will be there in industrial waste like totally i have like eight types of recycled plastics will be there the kind of in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd htt in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe p in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ld in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pp in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depend in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling kind in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling kind of pl in industrial waste like totally i have like eight types of recycled plastics will be there the kind of pd httpe pvc ldp pps depends upon the recycling kind of plastic In industrial waste. Like, totally. I have, like, eight types of recycled plastics will be there. The kind of Pd, httpe, PVC, LDP, pps, depends upon the recycling kind of plastic. there there there there we need to there there we need to add in there there we need to add in the main there there we need to add in the main goal is like there there we need to add in the main goal is like some plastics there there we need to add in the main goal is like some plastics can be recyc there there we need to add in the main goal is like some plastics can be recyclable some there there we need to add in the main goal is like some plastics can be recyclable some plastics there there we need to add in the main goal is like some plastics can be recyclable some plastics are can't there there we need to add in the main goal is like some plastics can be recyclable some plastics are can't be recyclable There, there. We need to add in. The main goal is like, some plastics can be recyclable. Some plastics are. Can't be recyclable. and some within and some within the and some within the plastics And some within the plastics. they have they have kind of they have kind of chemical they have kind of chemical contaminat they have kind of chemical contamination also they have kind of chemical contamination also will be there they have kind of chemical contamination also will be there so they have kind of chemical contamination also will be there so in the they have kind of chemical contamination also will be there so in the conveyor they have kind of chemical contamination also will be there so in the conveyor belt they have kind of chemical contamination also will be there so in the conveyor belt when they have kind of chemical contamination also will be there so in the conveyor belt when going they have kind of chemical contamination also will be there so in the conveyor belt when going the objects they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify the they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify the object they have kind of chemical contamination also will be there so in the conveyor belt when going the objects so we need to identify the objects and then They have kind of chemical contamination also will be there. So in the conveyor belt when going the objects. So we need to identify the objects, and then. we have to disting we have to distinguish we have to distinguish like we have to distinguish like what kind of we have to distinguish like what kind of plastic we have to distinguish like what kind of plastic it is and we have to distinguish like what kind of plastic it is and then we have to distinguish like what kind of plastic it is and then we need to we have to distinguish like what kind of plastic it is and then we need to class we have to distinguish like what kind of plastic it is and then we need to classify we have to distinguish like what kind of plastic it is and then we need to classify them we have to distinguish like what kind of plastic it is and then we need to classify them based we have to distinguish like what kind of plastic it is and then we need to classify them based on the we have to distinguish like what kind of plastic it is and then we need to classify them based on the images we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of like general we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of like generally we have to distinguish like what kind of plastic it is and then we need to classify them based on the images actually so what we'll get the kind of like generally we'll get We have to distinguish, like, what kind of plastic it is, and then we need to classify them. Based on the images, actually. So what, we'll get the kind of, like, generally we'll. Get. the the source is the source is like we will get the source is like we will get kind of the source is like we will get kind of twenty the source is like we will get kind of twenty seconds of the source is like we will get kind of twenty seconds of video the source is like we will get kind of twenty seconds of video the conveyor bel the source is like we will get kind of twenty seconds of video the conveyor belt whenever the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to ident the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the imag the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of random the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the imag the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detect the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection al the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available which is the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available which is in open the source is like we will get kind of twenty seconds of video the conveyor belt whenever going with that so we need to identify we need to capture the images which is going with instead of randomly taking the images we have used the kind of motion detection algorithm like such a background subtraction and optical flow available which is in opencv The source is like, we will get kind of 20 seconds of video, the conveyor belt, whenever going with that. So we need to identify, we need to capture the images, which is going with instead of randomly taking the images, we have used the kind of motion detection algorithm like such a background subtraction and optical flow available, which is in OpenCV to. identify identify the identify the frames Identify the frames. we have we have implemented we have implemented like a frame we have implemented like a frame scoring system we have implemented like a frame scoring system as we have implemented like a frame scoring system as well We have implemented like, a frame scoring system as well. which had which had kind of which had kind of which had which had kind of which had object clar which had kind of which had object clarity is high which had kind of which had object clarity is high and over which had kind of which had object clarity is high and overlap which had kind of which had object clarity is high and overlap and which had kind of which had object clarity is high and overlap and the angle which had kind of which had object clarity is high and overlap and the angle cover which had kind of which had object clarity is high and overlap and the angle coverage Which had kind of, which had object clarity is high and overlap, and the angle coverage. it will give it will give the prior it will give the prioritize it will give the prioritize the frames actually it will give the prioritize the frames actually so it will give the prioritize the frames actually so which is it will give the prioritize the frames actually so which is at the highest it will give the prioritize the frames actually so which is at the highest score so it will give the prioritize the frames actually so which is at the highest score so then it will give the prioritize the frames actually so which is at the highest score so then it will it will give the prioritize the frames actually so which is at the highest score so then it will take that it will give the prioritize the frames actually so which is at the highest score so then it will take that imag it will give the prioritize the frames actually so which is at the highest score so then it will take that images actually It will give the prioritize the frames, actually. So which is at the highest score, so. Then it will take that images actually. from the from the video from the video so from the video so like that from the video so like that we need to from the video so like that we need to collect from the video so like that we need to collect the from the video so like that we need to collect the images and from the video so like that we need to collect the images and there has from the video so like that we need to collect the images and there has a limitation from the video so like that we need to collect the images and there has a limitation also we from the video so like that we need to collect the images and there has a limitation also we have to take from the video so like that we need to collect the images and there has a limitation also we have to take maximum from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen imag from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after the object from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after the object deduction from the video so like that we need to collect the images and there has a limitation also we have to take maximum sixteen images only we have to take from ten to sixteen images we can take that images not more than that so after the object deduction done and From the video. So, like that, we need to collect the images. And there has a limitation also. We have to take maximum 16 images only. We have to take from ten to 16 images. We can take that images, not more than that. So after the object deduction done and. we have we have to do the we have to do the clarification we have to do the clarification part and then we have to do the clarification part and then we we have to do the clarification part and then we implement We have to do the clarification part and then we implement. module module like module like mask module like mask rlc module like mask rlcn like module like mask rlcn like this model module like mask rlcn like this model like mostly module like mask rlcn like this model like mostly why module like mask rlcn like this model like mostly why we choose module like mask rlcn like this model like mostly why we choose this module like mask rlcn like this model like mostly why we choose this it's like module like mask rlcn like this model like mostly why we choose this it's like this model module like mask rlcn like this model like mostly why we choose this it's like this model have module like mask rlcn like this model like mostly why we choose this it's like this model have bounding module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise mark module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the same thing module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the same thing and then after module like mask rlcn like this model like mostly why we choose this it's like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this the same thing and then after that Module, like mask, RLCN, like this model, like, mostly why we choose this. It's. Like this model have bounding boxes and the pixel wise marks for each detected object so that's why we go on with this. The same thing, and then after that. in in a in a single in a single image In a single image. we have like we have like one hundred we have like one hundred kind of we have like one hundred kind of in we have like one hundred kind of in conver we have like one hundred kind of in convert belt like we have like one hundred kind of in convert belt like one hundred objects we have like one hundred kind of in convert belt like one hundred objects will be we have like one hundred kind of in convert belt like one hundred objects will be there so we have like one hundred kind of in convert belt like one hundred objects will be there so each we have like one hundred kind of in convert belt like one hundred objects will be there so each object we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be different we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding box we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with pretrial we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with pretrial models we have like one hundred kind of in convert belt like one hundred objects will be there so each object will be differentiated by the bonding boxes actually so like that we need to try the model and after that we are done with the efficient turn it also like to transfer learning with pretrial models like that We have like, 100 kind of in convert belt. Like 100 objects will be there. So each object will be differentiated by the bonding boxes, actually. So, like that, we need to try the model and. After that we are done with the efficient turn. It also like to transfer learning with pretrial. Models like that. and then and then we need to and then we need to identify and then we need to identify the adjusting and then we need to identify the adjusting the lay and then we need to identify the adjusting the layers and then we need to identify the adjusting the layers like better and then we need to identify the adjusting the layers like better to capture and then we need to identify the adjusting the layers like better to capture the features and then we need to identify the adjusting the layers like better to capture the features like kind of and then we need to identify the adjusting the layers like better to capture the features like kind of specific and then we need to identify the adjusting the layers like better to capture the features like kind of specific cover And then we need to identify the adjusting the layers like better to capture the features like kind of specific cover. do you have used do you have used like do you have used like efficient do you have used like efficient net Do you have used, like efficient net? so so after so after that So after that. we have we have faced kind of we have faced kind of out we have faced kind of outlays we have faced kind of outlays like we have faced kind of outlays like the imag we have faced kind of outlays like the images have we have faced kind of outlays like the images have kind of we have faced kind of outlays like the images have kind of while going We have faced kind of outlays like the images have kind of while going. with the with the movement with the movement actually with the movement actually we can't take with the movement actually we can't take the with the movement actually we can't take the clarity with the movement actually we can't take the clarity imag with the movement actually we can't take the clarity images with the movement actually we can't take the clarity images so we have with the movement actually we can't take the clarity images so we have kind of with the movement actually we can't take the clarity images so we have kind of outliers with the movement actually we can't take the clarity images so we have kind of outliers data with the movement actually we can't take the clarity images so we have kind of outliers data so with the movement actually we can't take the clarity images so we have kind of outliers data so that with the movement actually we can't take the clarity images so we have kind of outliers data so that's with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blur with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry imag with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry images so with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry images so we applied with the movement actually we can't take the clarity images so we have kind of outliers data so that's finding that blurry images so we applied kind of With the movement. Actually, we can't take the clarity images, so we have kind of outliers data. So that's finding that blurry images. So we applied kind of. some some techniques to some techniques to improve some techniques to improve that some techniques to improve that deep learning Some techniques to improve that deep learning. images images we use like images we use like binary images we use like binary filtering images we use like binary filtering and images we use like binary filtering and motion images we use like binary filtering and motion deep blur images we use like binary filtering and motion deep blurring techniques images we use like binary filtering and motion deep blurring techniques like which images we use like binary filtering and motion deep blurring techniques like which is available in images we use like binary filtering and motion deep blurring techniques like which is available in open images we use like binary filtering and motion deep blurring techniques like which is available in opencv images we use like binary filtering and motion deep blurring techniques like which is available in opencv so images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have worked with images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have worked with estimated images we use like binary filtering and motion deep blurring techniques like which is available in opencv so like we have worked with estimated like Images we use like binary filtering and motion deep blurring techniques like, which is available in OpenCV so, like, we have worked with estimated, like, blur ke blur kernel blur kernel and blur kernel and reversing blur kernel and reversing that blur kernel and reversing that blurring blur kernel and reversing that blurring process blur kernel and reversing that blurring process and blur kernel and reversing that blurring process and sharpening blur kernel and reversing that blurring process and sharpening the blur kernel and reversing that blurring process and sharpening the images blur kernel and reversing that blurring process and sharpening the images that's the thing blur kernel and reversing that blurring process and sharpening the images that's the thing where we blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done after that blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done after that we are done blur kernel and reversing that blurring process and sharpening the images that's the thing where we have done after that we are done with Blur kernel and reversing that blurring process and sharpening the images. That's the thing where we have done after that, we are done with. one one more one more deep learning one more deep learning technique like one more deep learning technique like residual One more deep learning technique like residual. neural neural network neural network that's what neural network that's what we have done neural network that's what we have done is here neural network that's what we have done is here is neural network that's what we have done is here is like neural network that's what we have done is here is like the same neural network that's what we have done is here is like the same images neural network that's what we have done is here is like the same images with neural network that's what we have done is here is like the same images with a low quality neural network that's what we have done is here is like the same images with a low quality image neural network that's what we have done is here is like the same images with a low quality image and then neural network that's what we have done is here is like the same images with a low quality image and then with the same imag neural network that's what we have done is here is like the same images with a low quality image and then with the same image with neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provide neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both the data neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both the data and neural network that's what we have done is here is like the same images with a low quality image and then with the same image with high quality image so like that we provided with both the data and then Neural network. That's what we have done is here is like the same images with a low quality image and then with the same image with high quality image. So like that we provided with both the data and then. make it make it done make it done that the same make it done that the same with make it done that the same with a low resol make it done that the same with a low resolution with the make it done that the same with a low resolution with the high resolution make it done that the same with a low resolution with the high resolution so Make it done. That the same with a low resolution. With the high resolution. So. we're trying we're trying to update we're trying to update the up we're trying to update the upscale we're trying to update the upscale that images we're trying to update the upscale that images enhance we're trying to update the upscale that images enhancements we're trying to update the upscale that images enhancements resolution we're trying to update the upscale that images enhancements resolutions we're trying to update the upscale that images enhancements resolutions so we're trying to update the upscale that images enhancements resolutions so what it will we're trying to update the upscale that images enhancements resolutions so what it will do is we're trying to update the upscale that images enhancements resolutions so what it will do is like we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality imag we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it autom we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enh we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhance we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to high res we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to high resolution we're trying to update the upscale that images enhancements resolutions so what it will do is like whenever you get the image with the low quality image like it automatically enhances that resolution to high resolution generally We're trying to update the upscale that images, enhancements, resolutions. So what it will do? Is like whenever you get the image with the low quality image, like, it automatically enhances that. Resolution to high resolution generally. we can we can make it we can make it the threshold we can make it the threshold values we can make it the threshold values as like we can make it the threshold values as like nineteen we can make it the threshold values as like nineteen twenty we can make it the threshold values as like nineteen twenty by We can make it the threshold values as like 1920 by. twelve twenty twelve twenty like that twelve twenty like that based twelve twenty like that based on twelve twenty like that based on the twelve twenty like that based on the pixel twelve twenty like that based on the pixel sizes twelve twenty like that based on the pixel sizes actually so twelve twenty like that based on the pixel sizes actually so like that twelve twenty like that based on the pixel sizes actually so like that we twelve twenty like that based on the pixel sizes actually so like that we define twelve twenty like that based on the pixel sizes actually so like that we define some kind twelve twenty like that based on the pixel sizes actually so like that we define some kind of twelve twenty like that based on the pixel sizes actually so like that we define some kind of value twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatic twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enh twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that we have twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that we have one more twelve twenty like that based on the pixel sizes actually so like that we define some kind of value there so whichever coming that low to that and then we have to and it will automatically enhance the images and everything like that we hang in after that we have one more problem 1220 like that based on the pixel sizes, actually. So, like that we define some kind of. Value there. So whichever coming that low to that and then we have to and it will automatically. Enhance the images and everything like that. We hang in. After that, we have one more problem. is is like overlap is like overlapping is like overlapping the objects is like overlapping the objects like is like overlapping the objects like in is like overlapping the objects like in a single is like overlapping the objects like in a single object is like overlapping the objects like in a single object it looks like is like overlapping the objects like in a single object it looks like a single is like overlapping the objects like in a single object it looks like a single object is like overlapping the objects like in a single object it looks like a single object but Is like overlapping the objects, like in a single object. It looks like a single object, but. it had like it had like three it had like three or four objects it had like three or four objects combined it had like three or four objects combined together it had like three or four objects combined together it looks like it had like three or four objects combined together it looks like as a single it had like three or four objects combined together it looks like as a single object it had like three or four objects combined together it looks like as a single object so it had like three or four objects combined together it looks like as a single object so that's also it had like three or four objects combined together it looks like as a single object so that's also we have it had like three or four objects combined together it looks like as a single object so that's also we have faced the it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non ma it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum separation techn it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum separation techniques it had like three or four objects combined together it looks like as a single object so that's also we have faced the same thing so implemented like a soft nms like non maximum separation techniques so that It had, like, three or four objects combined together, it looks like as a single object, so that's also we have faced the same thing, so implemented like a soft nms, like non maximum separation techniques so that. what you do what you do is like it what you do is like it reduce what you do is like it reduce the what you do is like it reduce the confidence what you do is like it reduce the confidence codes What you do is like it. Reduce the confidence codes. and whenever And whenever. it detect it detected overlap it detected overlapping det it detected overlapping detection It detected overlapping detection. like within like within the object like within the object like like within the object like whenever like within the object like whenever there's sharpening like within the object like whenever there's sharpening imag like within the object like whenever there's sharpening images not like within the object like whenever there's sharpening images not detected like within the object like whenever there's sharpening images not detected it automatically like within the object like whenever there's sharpening images not detected it automatically move like within the object like whenever there's sharpening images not detected it automatically move to like within the object like whenever there's sharpening images not detected it automatically move to that like within the object like whenever there's sharpening images not detected it automatically move to that will consider like within the object like whenever there's sharpening images not detected it automatically move to that will consider as Like within the object, like whenever there's sharpening images not detected, it automatically move to that will consider as. there is there is no kind of there is no kind of boundary there is no kind of boundary lines there is no kind of boundary lines will be There is no kind of boundary lines will be. not very not very clear not very clear so not very clear so in that time Not very clear. So in that time, that that object that object will consider that object will consider as that object will consider as like object that object will consider as like object in object that object will consider as like object in object and then that object will consider as like object in object and then this model that object will consider as like object in object and then this model will apply that object will consider as like object in object and then this model will apply like that object will consider as like object in object and then this model will apply like software that object will consider as like object in object and then this model will apply like software ms that object will consider as like object in object and then this model will apply like software ms so that object will consider as like object in object and then this model will apply like software ms so it will go that object will consider as like object in object and then this model will apply like software ms so it will go with that object will consider as like object in object and then this model will apply like software ms so it will go with mostly that object will consider as like object in object and then this model will apply like software ms so it will go with mostly with That object will consider as like object in object, and then this model will apply like software. Ms. So it will go with mostly with. conver converted neural converted neural networks converted neural networks what converted neural networks what it do is converted neural networks what it do is like it will converted neural networks what it do is like it will give the sharpness converted neural networks what it do is like it will give the sharpness of the converted neural networks what it do is like it will give the sharpness of the image converted neural networks what it do is like it will give the sharpness of the image it improves converted neural networks what it do is like it will give the sharpness of the image it improves the detection converted neural networks what it do is like it will give the sharpness of the image it improves the detection of the accur converted neural networks what it do is like it will give the sharpness of the image it improves the detection of the accuracy Converted neural networks. What it do is like it will give the sharpness of the image. It improves the detection of the accuracy. like that like that it will go like that it will go with like that it will go with it like that it will go with it after that like that it will go with it after that we like that it will go with it after that we face like like that it will go with it after that we face like chemical like that it will go with it after that we face like chemical detection like that it will go with it after that we face like chemical detection like like that it will go with it after that we face like chemical detection like most like that it will go with it after that we face like chemical detection like mostly with an like that it will go with it after that we face like chemical detection like mostly with an army detect like that it will go with it after that we face like chemical detection like mostly with an army detection by using like that it will go with it after that we face like chemical detection like mostly with an army detection by using the senso like that it will go with it after that we face like chemical detection like mostly with an army detection by using the sensors and like that it will go with it after that we face like chemical detection like mostly with an army detection by using the sensors and with the imag like that it will go with it after that we face like chemical detection like mostly with an army detection by using the sensors and with the image data Like that, it will go with it. After that, we face, like, chemical detection, like mostly with an army detection by using the sensors and with the image data. like in the like in the chemical like in the chemical data like in the chemical data contamina like in the chemical data contamination is like in the chemical data contamination is there or not like in the chemical data contamination is there or not we have to like in the chemical data contamination is there or not we have to identify like in the chemical data contamination is there or not we have to identify with Like in the chemical data. Contamination is there or not? We have to identify with. while going while going with the while going with the conveyor while going with the conveyor belt while going with the conveyor belt we have while going with the conveyor belt we have ident while going with the conveyor belt we have identified While going with the conveyor belt, we have identified. integration integration of the real integration of the real time integration of the real time chemical integration of the real time chemical sensors integration of the real time chemical sensors with the imag integration of the real time chemical sensors with the image integration of the real time chemical sensors with the image analysis techn integration of the real time chemical sensors with the image analysis techniques integration of the real time chemical sensors with the image analysis techniques both actually integration of the real time chemical sensors with the image analysis techniques both actually so integration of the real time chemical sensors with the image analysis techniques both actually so we have integration of the real time chemical sensors with the image analysis techniques both actually so we have used Integration of the real time chemical sensors with the image analysis techniques. Both, actually. So we have used. like like ph like pho m like pho mqt like pho mqtds like pho mqtds library like pho mqtds library will be there in like pho mqtds library will be there in the python like pho mqtds library will be there in the python so what like pho mqtds library will be there in the python so what it like pho mqtds library will be there in the python so what it do is like pho mqtds library will be there in the python so what it do is like it like pho mqtds library will be there in the python so what it do is like it will communic like pho mqtds library will be there in the python so what it do is like it will communicate like pho mqtds library will be there in the python so what it do is like it will communicate with the like pho mqtds library will be there in the python so what it do is like it will communicate with the senso like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors senso like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an i like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot dev like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rp like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rpa and like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rpa and the like pho mqtds library will be there in the python so what it do is like it will communicate with the sensors sensor data it had an iot device so we have uses the rpa and the taraso Like pho MqtDS library will be there in the python. So what it do? Is like it will communicate with the sensors. Sensor data. It had an IoT device, so we have uses the RPA and the Taraso. it will give it will give so it will give so gpio it will give so gpio pins it will give so gpio pins will be it will give so gpio pins will be there it will give so gpio pins will be there so it will give so gpio pins will be there so that whatever It will give. So Gpio pins will be there so that whatever. the the sensor the sensor will the sensor will trigger The sensor will trigger. but the python but the python is but the python is identified But the python is identified. by By. particular particular techn particular technique particular technique with particular technique with the raso particular technique with the raso it will particular technique with the raso it will give particular technique with the raso it will give that there particular technique with the raso it will give that there is a message particular technique with the raso it will give that there is a message will particular technique with the raso it will give that there is a message will be sent particular technique with the raso it will give that there is a message will be sent to particular technique with the raso it will give that there is a message will be sent to our particular technique with the raso it will give that there is a message will be sent to our library particular technique with the raso it will give that there is a message will be sent to our library so mq particular technique with the raso it will give that there is a message will be sent to our library so mqtt so particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue the message particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue the message and particular technique with the raso it will give that there is a message will be sent to our library so mqtt so what it uses like simply it will queue the message and whenever Particular technique with the Raso it will give that there is a message will be sent to our library. So. Mqtt so what it uses, like simply it will queue the message and whenever. sometimes sometimes in sometimes in a canv sometimes in a canville sometimes in a canville continuously sometimes in a canville continuously it will give sometimes in a canville continuously it will give the arms sometimes in a canville continuously it will give the arms like sometimes in a canville continuously it will give the arms like four or five times sometimes in a canville continuously it will give the arms like four or five times it sometimes in a canville continuously it will give the arms like four or five times it will give sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mq sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqt sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechan sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechanism sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechanism after sometimes in a canville continuously it will give the arms like four or five times it will give the trigger so that's why we have used this mqtt mechanism after that Sometimes in a canville, continuously. It will give the arms like four or five times, it will. Give the trigger. So that's why we have used this MQTT mechanism after that. once it is once it is done and then once it is done and then we train once it is done and then we train the model like once it is done and then we train the model like whichever once it is done and then we train the model like whichever contain once it is done and then we train the model like whichever contains once it is done and then we train the model like whichever contains chem once it is done and then we train the model like whichever contains chemical once it is done and then we train the model like whichever contains chemical contam once it is done and then we train the model like whichever contains chemical contamination object once it is done and then we train the model like whichever contains chemical contamination objects and once it is done and then we train the model like whichever contains chemical contamination objects and it will compare once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have class once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that we once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that we gone with once it is done and then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination is there not like that we have classified that also and after that like this we have done and then we mostly with the data after that we gone with that data Once it is done. And then we train the model like whichever contains chemical contamination objects and it will compare with these things and it will identify there is a chemical contamination, is there not? Like that we have classified that also. And after that, like this we have done and then we mostly with the data. After that, we gone with that data. processing techn processing techniques and processing techniques and everything after processing techniques and everything after that processing techniques and everything after that we processing techniques and everything after that we test processing techniques and everything after that we test the things processing techniques and everything after that we test the things like processing techniques and everything after that we test the things like we processing techniques and everything after that we test the things like we drag the processing techniques and everything after that we test the things like we drag the tools with processing techniques and everything after that we test the things like we drag the tools with the tenso processing techniques and everything after that we test the things like we drag the tools with the tensor board processing techniques and everything after that we test the things like we drag the tools with the tensor board which processing techniques and everything after that we test the things like we drag the tools with the tensor board which is processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparame processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tun processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like that we processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like that we have in processing techniques and everything after that we test the things like we drag the tools with the tensor board which is provide the various interface to monitor metrics such as loss accuracy and other hyperparameter tune techniques we have used like that we have in these Processing techniques and everything after that, we test the things like we drag the tools with the tensor. Board, which is provide the various interface to monitor metrics such as loss, accuracy and other hyperparameter tune techniques we have used like that we have in these. there were there were basically two there were basically two classes there were basically two classes right there were basically two classes right one there were basically two classes right one is There were basically two classes, right? One is. degradable degradable plast degradable plastic or degradable plastic or it is degradable plastic or it is non degradable Degradable plastic or it is non degradable. plastics plastics within plastics within the pl plastics within the plastics itself plastics within the plastics itself there plastics within the plastics itself there are multip plastics within the plastics itself there are multiple plastics within the plastics itself there are multiple actually plastics within the plastics itself there are multiple actually they have Plastics. Within the plastics itself. There are multiple, actually. They have. like like eight kind of like eight kind of plastic like eight kind of plastic basically like eight kind of plastic basically the like eight kind of plastic basically the purpose of like eight kind of plastic basically the purpose of this exerci like eight kind of plastic basically the purpose of this exercise like eight kind of plastic basically the purpose of this exercise is like eight kind of plastic basically the purpose of this exercise is to like eight kind of plastic basically the purpose of this exercise is to identify like eight kind of plastic basically the purpose of this exercise is to identify the like eight kind of plastic basically the purpose of this exercise is to identify the plast like eight kind of plastic basically the purpose of this exercise is to identify the plastic which like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be rec like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled
2024-08-27 11:42:53.508319: like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right yes like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right yes that is like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right yes that is perfect Like eight kind of plastic. Basically, the purpose of this exercise is to identify the plastic which can be recycled. Yes, exactly. Okay, so they are interested to identify the plastic which they can recycle. Right? Yes. That is perfect. so so basically So, basically. there there can be there can be multiple there can be multiple plast there can be multiple plastics also there can be multiple plastics also yeah There can be multiple plastics also. Yeah. in the in the second grid in the second grid the main thing in the second grid the main thing is in the second grid the main thing is like in the second grid the main thing is like the plastics in the second grid the main thing is like the plastics within in the second grid the main thing is like the plastics within the plastic in the second grid the main thing is like the plastics within the plastic itself In the second grid. The main thing is like the plastics within the plastic itself. they want they want to ident they want to identify they want to identify the they want to identify the different they want to identify the different kind of they want to identify the different kind of plastics they want to identify the different kind of plastics the same they want to identify the different kind of plastics the same plastic they want to identify the different kind of plastics the same plastic itself they want to identify the different kind of plastics the same plastic itself there are they want to identify the different kind of plastics the same plastic itself there are any kind of plastics they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there before going to they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there before going to the rec they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there before going to the recycling They want to identify the different kind of plastics, the same plastic itself. There are any kind of plastics are there before going to the recycling. basically basically what is basically what is waste management basically what is waste management do is basically what is waste management do is like Basically, what is waste management do is like. they have they have like they have like before going they have like before going to the land they have like before going to the landfill they have like before going to the landfill and they have like before going to the landfill and the trucks they have like before going to the landfill and the trucks whatever they have like before going to the landfill and the trucks whatever the corrected the they have like before going to the landfill and the trucks whatever the corrected the trucks they have like before going to the landfill and the trucks whatever the corrected the trucks by the was they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste manage they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that land they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that landfill they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that landfill data They have, like before going to the landfill and the trucks, whatever the corrected the trucks by the waste management that landfill data. should should be should be sent should be sent to the Should be sent to the. recycling Recycling. plants plants or something plants or something is there Plants or something, is there? so So. before before going before going to the before going to the recyc before going to the recycling before going to the recycling process before going to the recycling process they want to before going to the recycling process they want to identify Before going to the recycling process, they want to identify. what kind of What kind of. plastics plastics they have plastics they have collected plastics they have collected and then plastics they have collected and then in plastics they have collected and then in that plast plastics they have collected and then in that plastic plastics they have collected and then in that plastic some plastics they have collected and then in that plastic some cont plastics they have collected and then in that plastic some containers plastics they have collected and then in that plastic some containers this plastics they have collected and then in that plastic some containers this process should plastics they have collected and then in that plastic some containers this process should happen plastics they have collected and then in that plastic some containers this process should happen in the rec plastics they have collected and then in that plastic some containers this process should happen in the recycle plastics they have collected and then in that plastic some containers this process should happen in the recycle actually plastics they have collected and then in that plastic some containers this process should happen in the recycle actually in the recycle plastics they have collected and then in that plastic some containers this process should happen in the recycle actually in the recycle plan plastics they have collected and then in that plastic some containers this process should happen in the recycle actually in the recycle plant but Plastics they have collected and then in that plastic, some containers. This process should happen in the recycle actually in the recycle plant, but. they want to they want to identify they want to identify and segreg they want to identify and segregate them before they want to identify and segregate them before going to they want to identify and segregate them before going to that they want to identify and segregate them before going to that and then They want to identify and segregate them before going to that, and then. i don't know i don't know the exact purpose i don't know the exact purpose and why i don't know the exact purpose and why they i don't know the exact purpose and why they want i don't know the exact purpose and why they want separate i don't know the exact purpose and why they want separate like eight i don't know the exact purpose and why they want separate like eight types of i don't know the exact purpose and why they want separate like eight types of plast i don't know the exact purpose and why they want separate like eight types of plastics i don't know the exact purpose and why they want separate like eight types of plastics and then i don't know the exact purpose and why they want separate like eight types of plastics and then maybe i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial benefit i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial benefit will be there i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial benefit will be there actually I don't know the exact purpose and why they want separate, like, eight types of plastics. And then maybe there is a kind of, kind of commercial benefit will be there, actually. okay Okay. let's go let's go to let's go to some bas let's go to some basics let's go to some basics okay let's go to some basics okay i will let's go to some basics okay i will ask some bas let's go to some basics okay i will ask some basic questions let's go to some basics okay i will ask some basic questions to let's go to some basics okay i will ask some basic questions to you Let's go to some basics. Okay? I will ask some basic questions to you. okay okay tell me okay tell me what okay tell me what is difference between okay tell me what is difference between a supervised okay tell me what is difference between a supervised and okay tell me what is difference between a supervised and unsupervised okay tell me what is difference between a supervised and unsupervised lending okay tell me what is difference between a supervised and unsupervised lending algorith
2024-08-27 11:42:53.508319: like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right yes like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right yes that is like eight kind of plastic basically the purpose of this exercise is to identify the plastic which can be recycled yes exactly okay so they are interested to identify the plastic which they can recycle right yes that is perfect Like eight kind of plastic. Basically, the purpose of this exercise is to identify the plastic which can be recycled. Yes, exactly. Okay, so they are interested to identify the plastic which they can recycle. Right? Yes. That is perfect. so so basically So, basically. there there can be there can be multiple there can be multiple plast there can be multiple plastics also there can be multiple plastics also yeah There can be multiple plastics also. Yeah. in the in the second grid in the second grid the main thing in the second grid the main thing is in the second grid the main thing is like in the second grid the main thing is like the plastics in the second grid the main thing is like the plastics within in the second grid the main thing is like the plastics within the plastic in the second grid the main thing is like the plastics within the plastic itself In the second grid. The main thing is like the plastics within the plastic itself. they want they want to ident they want to identify they want to identify the they want to identify the different they want to identify the different kind of they want to identify the different kind of plastics they want to identify the different kind of plastics the same they want to identify the different kind of plastics the same plastic they want to identify the different kind of plastics the same plastic itself they want to identify the different kind of plastics the same plastic itself there are they want to identify the different kind of plastics the same plastic itself there are any kind of plastics they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there before going to they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there before going to the rec they want to identify the different kind of plastics the same plastic itself there are any kind of plastics are there before going to the recycling They want to identify the different kind of plastics, the same plastic itself. There are any kind of plastics are there before going to the recycling. basically basically what is basically what is waste management basically what is waste management do is basically what is waste management do is like Basically, what is waste management do is like. they have they have like they have like before going they have like before going to the land they have like before going to the landfill they have like before going to the landfill and they have like before going to the landfill and the trucks they have like before going to the landfill and the trucks whatever they have like before going to the landfill and the trucks whatever the corrected the they have like before going to the landfill and the trucks whatever the corrected the trucks they have like before going to the landfill and the trucks whatever the corrected the trucks by the was they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste manage they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that land they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that landfill they have like before going to the landfill and the trucks whatever the corrected the trucks by the waste management that landfill data They have, like before going to the landfill and the trucks, whatever the corrected the trucks by the waste management that landfill data. should should be should be sent should be sent to the Should be sent to the. recycling Recycling. plants plants or something plants or something is there Plants or something, is there? so So. before before going before going to the before going to the recyc before going to the recycling before going to the recycling process before going to the recycling process they want to before going to the recycling process they want to identify Before going to the recycling process, they want to identify. what kind of What kind of. plastics plastics they have plastics they have collected plastics they have collected and then plastics they have collected and then in plastics they have collected and then in that plast plastics they have collected and then in that plastic plastics they have collected and then in that plastic some plastics they have collected and then in that plastic some cont plastics they have collected and then in that plastic some containers plastics they have collected and then in that plastic some containers this plastics they have collected and then in that plastic some containers this process should plastics they have collected and then in that plastic some containers this process should happen plastics they have collected and then in that plastic some containers this process should happen in the rec plastics they have collected and then in that plastic some containers this process should happen in the recycle plastics they have collected and then in that plastic some containers this process should happen in the recycle actually plastics they have collected and then in that plastic some containers this process should happen in the recycle actually in the recycle plastics they have collected and then in that plastic some containers this process should happen in the recycle actually in the recycle plan plastics they have collected and then in that plastic some containers this process should happen in the recycle actually in the recycle plant but Plastics they have collected and then in that plastic, some containers. This process should happen in the recycle actually in the recycle plant, but. they want to they want to identify they want to identify and segreg they want to identify and segregate them before they want to identify and segregate them before going to they want to identify and segregate them before going to that they want to identify and segregate them before going to that and then They want to identify and segregate them before going to that, and then. i don't know i don't know the exact purpose i don't know the exact purpose and why i don't know the exact purpose and why they i don't know the exact purpose and why they want i don't know the exact purpose and why they want separate i don't know the exact purpose and why they want separate like eight i don't know the exact purpose and why they want separate like eight types of i don't know the exact purpose and why they want separate like eight types of plast i don't know the exact purpose and why they want separate like eight types of plastics i don't know the exact purpose and why they want separate like eight types of plastics and then i don't know the exact purpose and why they want separate like eight types of plastics and then maybe i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial benefit i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial benefit will be there i don't know the exact purpose and why they want separate like eight types of plastics and then maybe there is a kind of kind of commercial benefit will be there actually I don't know the exact purpose and why they want separate, like, eight types of plastics. And then maybe there is a kind of, kind of commercial benefit will be there, actually. okay Okay. let's go let's go to let's go to some bas let's go to some basics let's go to some basics okay let's go to some basics okay i will let's go to some basics okay i will ask some bas let's go to some basics okay i will ask some basic questions let's go to some basics okay i will ask some basic questions to let's go to some basics okay i will ask some basic questions to you Let's go to some basics. Okay? I will ask some basic questions to you. okay okay tell me okay tell me what okay tell me what is difference between okay tell me what is difference between a supervised okay tell me what is difference between a supervised and okay tell me what is difference between a supervised and unsupervised okay tell me what is difference between a supervised and unsupervised lending okay tell me what is difference between a supervised and unsupervised lending algorith
2024-08-27 11:43:24.388126: okay tell me what is difference between a supervised and unsupervised lending algorithms Okay, tell me, what is difference between a supervised and unsupervised lending algorithms? super supervised supervised and unsuperv supervised and unsupervised Supervised and unsupervised. like Like. have you have you understood have you understood question Have you understood question? do you want me do you want me to rep do you want me to repeat Do you want me to repeat? hello hello hello Hello. Hello. i'm audible i'm audible yes I'm audible? Yes. yeah i think yeah i think something yeah i think something is missing yeah i think something is missing sorry yeah i think something is missing sorry you're asking yeah i think something is missing sorry you're asking about yeah i think something is missing sorry you're asking about supervised yeah i think something is missing sorry you're asking about supervised and unsupervised yeah i think something is missing sorry you're asking about supervised and unsupervised learning
2024-08-27 11:43:24.389135: okay tell me what is difference between a supervised and unsupervised lending algorithms Okay, tell me, what is difference between a supervised and unsupervised lending algorithms? super supervised supervised and unsuperv supervised and unsupervised Supervised and unsupervised. like Like. have you have you understood have you understood question Have you understood question? do you want me do you want me to rep do you want me to repeat Do you want me to repeat? hello hello hello Hello. Hello. i'm audible i'm audible yes I'm audible? Yes. yeah i think yeah i think something yeah i think something is missing yeah i think something is missing sorry yeah i think something is missing sorry you're asking yeah i think something is missing sorry you're asking about yeah i think something is missing sorry you're asking about supervised yeah i think something is missing sorry you're asking about supervised and unsupervised yeah i think something is missing sorry you're asking about supervised and unsupervised learning
2024-08-27 11:44:29.541349: yeah i think something is missing sorry you're asking about supervised and unsupervised learning right yeah i think something is missing sorry you're asking about supervised and unsupervised learning right yes Yeah, I think something is missing. Sorry, you're asking about supervised and unsupervised. Learning, right? Yes. okay okay fine super okay fine supervised learning okay fine supervised learning like okay fine supervised learning like involves like okay fine supervised learning like involves like training the okay fine supervised learning like involves like training the models okay fine supervised learning like involves like training the models on okay fine supervised learning like involves like training the models on label data okay fine supervised learning like involves like training the models on label data where the okay fine supervised learning like involves like training the models on label data where the input okay fine supervised learning like involves like training the models on label data where the input features okay fine supervised learning like involves like training the models on label data where the input features and okay fine supervised learning like involves like training the models on label data where the input features and correspond okay fine supervised learning like involves like training the models on label data where the input features and corresponding okay fine supervised learning like involves like training the models on label data where the input features and corresponding output okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like outp okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output lab okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like example okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples includ okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the class okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the classification okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the classification regression okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the classification regression tasks Okay, fine. Supervised learning, like, involves, like, training the models on label data where the input features and corresponding output like output labels are known like examples including the classification regression tasks. like like predicting like predicting the type like predicting the type of like predicting the type of in our like predicting the type of in our case like plast like predicting the type of in our case like plastic like predicting the type of in our case like plastic are forecasting like predicting the type of in our case like plastic are forecasting the like predicting the type of in our case like plastic are forecasting the incident like predicting the type of in our case like plastic are forecasting the incident surveys like predicting the type of in our case like plastic are forecasting the incident surveys which i have like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are under like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are undercome un like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are undercome unsupervised like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are undercome unsupervised learning Like predicting the type of in our case, like plastic are forecasting the incident surveys which I have previously worked on. Those are undercome unsupervised learning. like like in superv like in supervised learning Like in supervised learning. like most like mostly like mostly designed like mostly designed the like mostly designed the length from the like mostly designed the length from the label like mostly designed the length from the label data like mostly designed the length from the label data where each like mostly designed the length from the label data where each trained like mostly designed the length from the label data where each trained training like mostly designed the length from the label data where each trained training sample like mostly designed the length from the label data where each trained training sample is like mostly designed the length from the label data where each trained training sample is associated like mostly designed the length from the label data where each trained training sample is associated with Like mostly designed. The length from the label data where each trained training sample is associated with. kind of Kind of. like like lab like label or outcome Like label or outcome. is Is. where where the main where the main goal where the main goal is like to where the main goal is like to learn where the main goal is like to learn a mapping where the main goal is like to learn a mapping from the where the main goal is like to learn a mapping from the inputs where the main goal is like to learn a mapping from the inputs to the where the main goal is like to learn a mapping from the inputs to the outputs where the main goal is like to learn a mapping from the inputs to the outputs can be where the main goal is like to learn a mapping from the inputs to the outputs can be accurately where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go with where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go with that where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go with that actually Where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that. We can go with that, actually. does superv does supervised does supervised learning Does supervised learning. can you can you tell me can you tell me the difference can you tell me the difference between can you tell me the difference between linear reg can you tell me the difference between linear regression and can you tell me the difference between linear regression and logistics can you tell me the difference between linear regression and logistics reg can you tell me the difference between linear regression and logistics regression Can you tell me the difference between linear regression and logistics regression? difference difference between difference between line difference between linear difference between linear and Difference between linear and. linear linear regression linear regression and log linear regression and logistic
2024-08-27 11:44:29.541872: yeah i think something is missing sorry you're asking about supervised and unsupervised learning right yeah i think something is missing sorry you're asking about supervised and unsupervised learning right yes Yeah, I think something is missing. Sorry, you're asking about supervised and unsupervised. Learning, right? Yes. okay okay fine super okay fine supervised learning okay fine supervised learning like okay fine supervised learning like involves like okay fine supervised learning like involves like training the okay fine supervised learning like involves like training the models okay fine supervised learning like involves like training the models on okay fine supervised learning like involves like training the models on label data okay fine supervised learning like involves like training the models on label data where the okay fine supervised learning like involves like training the models on label data where the input okay fine supervised learning like involves like training the models on label data where the input features okay fine supervised learning like involves like training the models on label data where the input features and okay fine supervised learning like involves like training the models on label data where the input features and correspond okay fine supervised learning like involves like training the models on label data where the input features and corresponding okay fine supervised learning like involves like training the models on label data where the input features and corresponding output okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like outp okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output lab okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like example okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples includ okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the class okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the classification okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the classification regression okay fine supervised learning like involves like training the models on label data where the input features and corresponding output like output labels are known like examples including the classification regression tasks Okay, fine. Supervised learning, like, involves, like, training the models on label data where the input features and corresponding output like output labels are known like examples including the classification regression tasks. like like predicting like predicting the type like predicting the type of like predicting the type of in our like predicting the type of in our case like plast like predicting the type of in our case like plastic like predicting the type of in our case like plastic are forecasting like predicting the type of in our case like plastic are forecasting the like predicting the type of in our case like plastic are forecasting the incident like predicting the type of in our case like plastic are forecasting the incident surveys like predicting the type of in our case like plastic are forecasting the incident surveys which i have like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are under like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are undercome un like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are undercome unsupervised like predicting the type of in our case like plastic are forecasting the incident surveys which i have previously worked on those are undercome unsupervised learning Like predicting the type of in our case, like plastic are forecasting the incident surveys which I have previously worked on. Those are undercome unsupervised learning. like like in superv like in supervised learning Like in supervised learning. like most like mostly like mostly designed like mostly designed the like mostly designed the length from the like mostly designed the length from the label like mostly designed the length from the label data like mostly designed the length from the label data where each like mostly designed the length from the label data where each trained like mostly designed the length from the label data where each trained training like mostly designed the length from the label data where each trained training sample like mostly designed the length from the label data where each trained training sample is like mostly designed the length from the label data where each trained training sample is associated like mostly designed the length from the label data where each trained training sample is associated with Like mostly designed. The length from the label data where each trained training sample is associated with. kind of Kind of. like like lab like label or outcome Like label or outcome. is Is. where where the main where the main goal where the main goal is like to where the main goal is like to learn where the main goal is like to learn a mapping where the main goal is like to learn a mapping from the where the main goal is like to learn a mapping from the inputs where the main goal is like to learn a mapping from the inputs to the where the main goal is like to learn a mapping from the inputs to the outputs where the main goal is like to learn a mapping from the inputs to the outputs can be where the main goal is like to learn a mapping from the inputs to the outputs can be accurately where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go with where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go with that where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that we can go with that actually Where the main goal is like to learn a mapping from the inputs to the outputs can be accurately predict the label like that. We can go with that, actually. does superv does supervised does supervised learning Does supervised learning. can you can you tell me can you tell me the difference can you tell me the difference between can you tell me the difference between linear reg can you tell me the difference between linear regression and can you tell me the difference between linear regression and logistics can you tell me the difference between linear regression and logistics reg can you tell me the difference between linear regression and logistics regression Can you tell me the difference between linear regression and logistics regression? difference difference between difference between line difference between linear difference between linear and Difference between linear and. linear linear regression linear regression and log linear regression and logistic
2024-08-27 11:46:06.338012: linear regression and logistic logistic regress linear regression and logistic logistic regression linear regression and logistic logistic regression okay linear regression and logistic logistic regression okay fine linear regression and logistic logistic regression okay fine so linear regression and logistic logistic regression okay fine so mainly linear regression and logistic logistic regression okay fine so mainly like linear regression and logistic logistic regression okay fine so mainly like linear linear regression and logistic logistic regression okay fine so mainly like linear regression met linear regression and logistic logistic regression okay fine so mainly like linear regression method linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in log linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regress linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for binary class linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for binary classification like Linear regression and logistic. Logistic regression. Okay, fine. So mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for binary classification like. a predict a predict probability A predict probability. of of discrete Of discrete. out outcome actually outcome actually so outcome actually so this outcome actually so this if you come outcome actually so this if you come with an example Outcome, actually. So this, if you come with an example. it is it is designed to it is designed to predict it is designed to predict continuous it is designed to predict continuously the it is designed to predict continuously the linear it is designed to predict continuously the linear regression it is designed to predict continuously the linear regression like it is designed to predict continuously the linear regression like a dependent it is designed to predict continuously the linear regression like a dependent variable based it is designed to predict continuously the linear regression like a dependent variable based on it is designed to predict continuously the linear regression like a dependent variable based on one it is designed to predict continuously the linear regression like a dependent variable based on one or multiple it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent variable it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent variables it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent variables so like that It is designed to predict continuously. The linear regression, like a dependent variable based on one or multiple independent variables. So, like that. it will work in the it will work in the linear it will work in the linear regress it will work in the linear regression It will work in the linear regression. if if you go the large if you go the large scale regress if you go the large scale regression it used if you go the large scale regression it used for the if you go the large scale regression it used for the binary class if you go the large scale regression it used for the binary classification if you go the large scale regression it used for the binary classification problems to if you go the large scale regression it used for the binary classification problems to predict if you go the large scale regression it used for the binary classification problems to predict the prob if you go the large scale regression it used for the binary classification problems to predict the probability of if you go the large scale regression it used for the binary classification problems to predict the probability of like if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go with if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go with the input if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go with the inputs If you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that. We can go with the inputs. which which indicates which indicates that prob which indicates that probability which indicates that probability of which indicates that probability of certain which indicates that probability of certain class which indicates that probability of certain class like that Which indicates that probability of certain class like that. in in that probability In that probability. and then And then. logistics in line logistics in linear regress logistics in linear regression we logistics in linear regression we used for logistics in linear regression we used for the matrix Logistics. In linear regression, we used for the matrix. like like mean square like mean square error Like mean square error. now now tell me now tell me in now tell me in both case now tell me in both case we now tell me in both case we use regress now tell me in both case we use regression now tell me in both case we use regression as a word now tell me in both case we use regression as a worded line now tell me in both case we use regression as a worded linear regress now tell me in both case we use regression as a worded linear regression now tell me in both case we use regression as a worded linear regression logistic now tell me in both case we use regression as a worded linear regression logistic regress now tell me in both case we use regression as a worded linear regression logistic regression Now tell me. In both case, we use regression as a worded linear regression, logistic regression. why there is why there is a regress why there is a regression why there is a regression award in why there is a regression award in logist why there is a regression award in logistic reg why there is a regression award in logistic regression why there is a regression award in logistic regression even why there is a regression award in logistic regression even it is why there is a regression award in logistic regression even it is used why there is a regression award in logistic regression even it is used for why there is a regression award in logistic regression even it is used for the class why there is a regression award in logistic regression even it is used for the classification why there is a regression award in logistic regression even it is used for the classification tas why there is a regression award in logistic regression even it is used for the classification tasks right why there is a regression award in logistic regression even it is used for the classification tasks right so why there is a regression award in logistic regression even it is used for the classification tasks right so use why there is a regression award in logistic regression even it is used for the classification tasks right so use it in class why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regress why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is a bit confus why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is a bit confusing right why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is a bit confusing right sometimes Why there is a regression award in logistic regression, even it is used for the classification tasks. Right, so use it in classified task. But the word is logistic regression, which is a bit. Confusing, right? Sometimes. when we when we hear about when we hear about reg when we hear about regression when we hear about regression we'll when we hear about regression we'll think of when we hear about regression we'll think of like contin when we hear about regression we'll think of like continuous var when we hear about regression we'll think of like continuous variable pre when we hear about regression we'll think of like continuous variable prediction right when we hear about regression we'll think of like continuous variable prediction right so why when we hear about regression we'll think of like continuous variable prediction right so why is there when we hear about regression we'll think of like continuous variable prediction right so why is there regress when we hear about regression we'll think of like continuous variable prediction right so why is there regression When we hear about regression, we'll think of, like, continuous variable prediction. Right? So why is there. Regression.
2024-08-27 11:46:06.338012: linear regression and logistic logistic regress linear regression and logistic logistic regression linear regression and logistic logistic regression okay linear regression and logistic logistic regression okay fine linear regression and logistic logistic regression okay fine so linear regression and logistic logistic regression okay fine so mainly linear regression and logistic logistic regression okay fine so mainly like linear regression and logistic logistic regression okay fine so mainly like linear linear regression and logistic logistic regression okay fine so mainly like linear regression met linear regression and logistic logistic regression okay fine so mainly like linear regression method linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in log linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regress linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for binary class linear regression and logistic logistic regression okay fine so mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for binary classification like Linear regression and logistic. Logistic regression. Okay, fine. So mainly like linear regression method uses to predict a continuous numerical value based on the input features and in logistic regression is used for binary classification like. a predict a predict probability A predict probability. of of discrete Of discrete. out outcome actually outcome actually so outcome actually so this outcome actually so this if you come outcome actually so this if you come with an example Outcome, actually. So this, if you come with an example. it is it is designed to it is designed to predict it is designed to predict continuous it is designed to predict continuously the it is designed to predict continuously the linear it is designed to predict continuously the linear regression it is designed to predict continuously the linear regression like it is designed to predict continuously the linear regression like a dependent it is designed to predict continuously the linear regression like a dependent variable based it is designed to predict continuously the linear regression like a dependent variable based on it is designed to predict continuously the linear regression like a dependent variable based on one it is designed to predict continuously the linear regression like a dependent variable based on one or multiple it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent variable it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent variables it is designed to predict continuously the linear regression like a dependent variable based on one or multiple independent variables so like that It is designed to predict continuously. The linear regression, like a dependent variable based on one or multiple independent variables. So, like that. it will work in the it will work in the linear it will work in the linear regress it will work in the linear regression It will work in the linear regression. if if you go the large if you go the large scale regress if you go the large scale regression it used if you go the large scale regression it used for the if you go the large scale regression it used for the binary class if you go the large scale regression it used for the binary classification if you go the large scale regression it used for the binary classification problems to if you go the large scale regression it used for the binary classification problems to predict if you go the large scale regression it used for the binary classification problems to predict the prob if you go the large scale regression it used for the binary classification problems to predict the probability of if you go the large scale regression it used for the binary classification problems to predict the probability of like if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go with if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go with the input if you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that we can go with the inputs If you go the large scale regression it used for the binary classification problems to predict the probability of like a discrete outcome like that. We can go with the inputs. which which indicates which indicates that prob which indicates that probability which indicates that probability of which indicates that probability of certain which indicates that probability of certain class which indicates that probability of certain class like that Which indicates that probability of certain class like that. in in that probability In that probability. and then And then. logistics in line logistics in linear regress logistics in linear regression we logistics in linear regression we used for logistics in linear regression we used for the matrix Logistics. In linear regression, we used for the matrix. like like mean square like mean square error Like mean square error. now now tell me now tell me in now tell me in both case now tell me in both case we now tell me in both case we use regress now tell me in both case we use regression now tell me in both case we use regression as a word now tell me in both case we use regression as a worded line now tell me in both case we use regression as a worded linear regress now tell me in both case we use regression as a worded linear regression now tell me in both case we use regression as a worded linear regression logistic now tell me in both case we use regression as a worded linear regression logistic regress now tell me in both case we use regression as a worded linear regression logistic regression Now tell me. In both case, we use regression as a worded linear regression, logistic regression. why there is why there is a regress why there is a regression why there is a regression award in why there is a regression award in logist why there is a regression award in logistic reg why there is a regression award in logistic regression why there is a regression award in logistic regression even why there is a regression award in logistic regression even it is why there is a regression award in logistic regression even it is used why there is a regression award in logistic regression even it is used for why there is a regression award in logistic regression even it is used for the class why there is a regression award in logistic regression even it is used for the classification why there is a regression award in logistic regression even it is used for the classification tas why there is a regression award in logistic regression even it is used for the classification tasks right why there is a regression award in logistic regression even it is used for the classification tasks right so why there is a regression award in logistic regression even it is used for the classification tasks right so use why there is a regression award in logistic regression even it is used for the classification tasks right so use it in class why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regress why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is a bit confus why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is a bit confusing right why there is a regression award in logistic regression even it is used for the classification tasks right so use it in classified task but the word is logistic regression which is a bit confusing right sometimes Why there is a regression award in logistic regression, even it is used for the classification tasks. Right, so use it in classified task. But the word is logistic regression, which is a bit. Confusing, right? Sometimes. when we when we hear about when we hear about reg when we hear about regression when we hear about regression we'll when we hear about regression we'll think of when we hear about regression we'll think of like contin when we hear about regression we'll think of like continuous var when we hear about regression we'll think of like continuous variable pre when we hear about regression we'll think of like continuous variable prediction right when we hear about regression we'll think of like continuous variable prediction right so why when we hear about regression we'll think of like continuous variable prediction right so why is there when we hear about regression we'll think of like continuous variable prediction right so why is there regress when we hear about regression we'll think of like continuous variable prediction right so why is there regression When we hear about regression, we'll think of, like, continuous variable prediction. Right? So why is there. Regression.
2024-08-27 11:48:35.551702: why why this why this color why this color depression Why this color depression? yeah Yeah. like like linear like linear regression Like linear regression. predicts Predicts. the regress the regression the regression mostly the regression mostly like the regression mostly like linear reg the regression mostly like linear regression the regression mostly like linear regression and loss reg the regression mostly like linear regression and loss regression the regression mostly like linear regression and loss regression are both the regression mostly like linear regression and loss regression are both type of reg the regression mostly like linear regression and loss regression are both type of regression the regression mostly like linear regression and loss regression are both type of regression techniques the regression mostly like linear regression and loss regression are both type of regression techniques only the regression mostly like linear regression and loss regression are both type of regression techniques only but the regression mostly like linear regression and loss regression are both type of regression techniques only but they are the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear regression the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear regression used the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear regression used for The regression mostly like linear regression and loss regression are both type of regression techniques only, but they are used for different types of predictive tasks. Right? So that's why it is used. Like that linear regression used for. just just a second just a second i just a second i got a call Just a second. I got a call. yes yes sorry Yes, sorry. okay Okay. like like the regression like the regression term actually Like the regression term, actually. the the term like most the term like mostly the term like mostly with the refer the term like mostly with the refers like the term like mostly with the refers like here in the the term like mostly with the refers like here in the logistic the term like mostly with the refers like here in the logistic regress the term like mostly with the refers like here in the logistic regression the term like mostly with the refers like here in the logistic regression like linear the term like mostly with the refers like here in the logistic regression like linear regress the term like mostly with the refers like here in the logistic regression like linear regression The term like mostly with the refers, like here in the logistic regression, like linear regression. mostly because mostly because of mostly because of the mostly because of the relationship between mostly because of the relationship between the mostly because of the relationship between the input mostly because of the relationship between the input variable mostly because of the relationship between the input variables mostly because of the relationship between the input variables like mostly because of the relationship between the input variables like x mostly because of the relationship between the input variables like x and mostly because of the relationship between the input variables like x and y Mostly because of the relationship between the input variables like x and y. will be will be the target will be the target variable will be the target variable and will be the target variable and then x will be the target variable and then x will be will be the target variable and then x will be the input Will be the target variable, and then X will be the input. inp input variable input variable in input variable in that time actually input variable in that time actually however the input variable in that time actually however the linear input variable in that time actually however the linear regression model input variable in that time actually however the linear regression models input variable in that time actually however the linear regression models are like input variable in that time actually however the linear regression models are like direct input variable in that time actually however the linear regression models are like directly linear input variable in that time actually however the linear regression models are like directly linear relationship input variable in that time actually however the linear regression models are like directly linear relationship and in input variable in that time actually however the linear regression models are like directly linear relationship and in the logist input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression model input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability of input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability of binary out input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability of binary outcome like Input variable in that time. Actually, however, the linear regression models are like directly linear relationship and in the logistic regression models are a probability of binary outcome like. using sigmo using sigmoid using sigmoid function to using sigmoid function to map using sigmoid function to map the using sigmoid function to map the predictive using sigmoid function to map the predictive values using sigmoid function to map the predictive values that's why using sigmoid function to map the predictive values that's why we use that using sigmoid function to map the predictive values that's why we use that regression Using sigmoid function to map the predictive values. That's why we use that regression. okay Okay. tell me Tell me. how how you how you identify how you identify outli how you identify outlier how you identify outlier in the data How you identify outlier in the data. whatever whatever techniques whatever techniques to handle whatever techniques to handle outli whatever techniques to handle outliers Whatever techniques to handle outliers.
2024-08-27 11:48:35.551702: why why this why this color why this color depression Why this color depression? yeah Yeah. like like linear like linear regression Like linear regression. predicts Predicts. the regress the regression the regression mostly the regression mostly like the regression mostly like linear reg the regression mostly like linear regression the regression mostly like linear regression and loss reg the regression mostly like linear regression and loss regression the regression mostly like linear regression and loss regression are both the regression mostly like linear regression and loss regression are both type of reg the regression mostly like linear regression and loss regression are both type of regression the regression mostly like linear regression and loss regression are both type of regression techniques the regression mostly like linear regression and loss regression are both type of regression techniques only the regression mostly like linear regression and loss regression are both type of regression techniques only but the regression mostly like linear regression and loss regression are both type of regression techniques only but they are the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear regression the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear regression used the regression mostly like linear regression and loss regression are both type of regression techniques only but they are used for different types of predictive tasks right so that's why it is used like that linear regression used for The regression mostly like linear regression and loss regression are both type of regression techniques only, but they are used for different types of predictive tasks. Right? So that's why it is used. Like that linear regression used for. just just a second just a second i just a second i got a call Just a second. I got a call. yes yes sorry Yes, sorry. okay Okay. like like the regression like the regression term actually Like the regression term, actually. the the term like most the term like mostly the term like mostly with the refer the term like mostly with the refers like the term like mostly with the refers like here in the the term like mostly with the refers like here in the logistic the term like mostly with the refers like here in the logistic regress the term like mostly with the refers like here in the logistic regression the term like mostly with the refers like here in the logistic regression like linear the term like mostly with the refers like here in the logistic regression like linear regress the term like mostly with the refers like here in the logistic regression like linear regression The term like mostly with the refers, like here in the logistic regression, like linear regression. mostly because mostly because of mostly because of the mostly because of the relationship between mostly because of the relationship between the mostly because of the relationship between the input mostly because of the relationship between the input variable mostly because of the relationship between the input variables mostly because of the relationship between the input variables like mostly because of the relationship between the input variables like x mostly because of the relationship between the input variables like x and mostly because of the relationship between the input variables like x and y Mostly because of the relationship between the input variables like x and y. will be will be the target will be the target variable will be the target variable and will be the target variable and then x will be the target variable and then x will be will be the target variable and then x will be the input Will be the target variable, and then X will be the input. inp input variable input variable in input variable in that time actually input variable in that time actually however the input variable in that time actually however the linear input variable in that time actually however the linear regression model input variable in that time actually however the linear regression models input variable in that time actually however the linear regression models are like input variable in that time actually however the linear regression models are like direct input variable in that time actually however the linear regression models are like directly linear input variable in that time actually however the linear regression models are like directly linear relationship input variable in that time actually however the linear regression models are like directly linear relationship and in input variable in that time actually however the linear regression models are like directly linear relationship and in the logist input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression model input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability of input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability of binary out input variable in that time actually however the linear regression models are like directly linear relationship and in the logistic regression models are a probability of binary outcome like Input variable in that time. Actually, however, the linear regression models are like directly linear relationship and in the logistic regression models are a probability of binary outcome like. using sigmo using sigmoid using sigmoid function to using sigmoid function to map using sigmoid function to map the using sigmoid function to map the predictive using sigmoid function to map the predictive values using sigmoid function to map the predictive values that's why using sigmoid function to map the predictive values that's why we use that using sigmoid function to map the predictive values that's why we use that regression Using sigmoid function to map the predictive values. That's why we use that regression. okay Okay. tell me Tell me. how how you how you identify how you identify outli how you identify outlier how you identify outlier in the data How you identify outlier in the data. whatever whatever techniques whatever techniques to handle whatever techniques to handle outli whatever techniques to handle outliers Whatever techniques to handle outliers.
2024-08-27 11:50:21.118565: okay Okay. outliers outliers okay Outliers, okay? we have we have statical we have statical methods like we have statical methods like mostly We have statical methods, like mostly. you go to the i you go to the ip you go to the ip address like you go to the ip address like inter you go to the ip address like interquartile you go to the ip address like interquartile range you go to the ip address like interquartile range that's one thing you go to the ip address like interquartile range that's one thing and jet score you go to the ip address like interquartile range that's one thing and jet score is you go to the ip address like interquartile range that's one thing and jet score is there You go to the IP address like interquartile range, that's one thing. And. Jet score is there? that's one that's one more and then that's one more and then visual that's one more and then visual for that's one more and then visual for visual that's one more and then visual for visual method that's one more and then visual for visual methods That's one more. And then visual for visual methods. let me just let me just mention Let me just mention. how how iqr how iqr would work how iqr would work in this how iqr would work in this case How IQR would work in this case. okay fine okay fine the iq okay fine the iqr method okay fine the iqr method invol okay fine the iqr method involves okay fine the iqr method involves calculating okay fine the iqr method involves calculating the first quart okay fine the iqr method involves calculating the first quartile like okay fine the iqr method involves calculating the first quartile like q one okay fine the iqr method involves calculating the first quartile like q one and the third okay fine the iqr method involves calculating the first quartile like q one and the third quart okay fine the iqr method involves calculating the first quartile like q one and the third quartile q okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the ra okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable value okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of ipr okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of ipr from okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of ipr from q one Okay, fine. The IQR method involves calculating the first quartile, like q one, and the. Third quartile q three, and then using these to find the range of acceptable values like values between like beyond one five 1.5 times of IPR from Q one. and and q and q three and q three q three and q three q three here and q three q three here consider and q three q three here consider as and q three q three here consider as outlay and q three q three here consider as outlayers actually and q three q three here consider as outlayers actually so and q three q three here consider as outlayers actually so in that and q three q three here consider as outlayers actually so in that way and q three q three here consider as outlayers actually so in that way it will be and q three q three here consider as outlayers actually so in that way it will be okay and q three q three here consider as outlayers actually so in that way it will be okay so And Q three. Q three. Here, consider as outlayers, actually. So in that way it will. Be okay, so. that's that's where that's where the high care that's where the high care of that's where the high care of interest That's where the high care of interest. what about what about z What about Z? square square like square like z square square like z square by computing square like z square by computing the z square like z square by computing the z square square like z square by computing the z square which measure square like z square by computing the z square which measures square like z square by computing the z square which measures how many square like z square by computing the z square which measures how many standard dev square like z square by computing the z square which measures how many standard deviations square like z square by computing the z square which measures how many standard deviations in a data square like z square by computing the z square which measures how many standard deviations in a data point square like z square by computing the z square which measures how many standard deviations in a data point from square like z square by computing the z square which measures how many standard deviations in a data point from the mean square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify outlayers square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify outlayers as the value square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify outlayers as the value of Square like Z Square, by computing the z square, which measures how many standard deviations in a data point from the mean, actually, so we can identify outlayers as the value of. laying laying beyond the laying beyond the threshold laying beyond the threshold values laying beyond the threshold values like laying beyond the threshold values like between Laying beyond the threshold values like between. generally generally like generally like if it is generally like if it is go with generally like if it is go with the curve Generally like if it is, go with the curve. between between negative between negative to positive between negative to positive like between negative to positive like minus between negative to positive like minus one between negative to positive like minus one two one between negative to positive like minus one two one like minus between negative to positive like minus one two one like minus two to between negative to positive like minus one two one like minus two to two between negative to positive like minus one two one like minus two to two like that we can between negative to positive like minus one two one like minus two to two like that we can go between negative to positive like minus one two one like minus two to two like that we can go with Between negative to positive, like minus one, two one. Like minus two to two. Like that. We can. Go with. okay okay ass okay assume that okay assume that you have okay assume that you have outlier okay assume that you have outlier in okay assume that you have outlier in data okay assume that you have outlier in data and Okay, assume that you have outlier in data and. you are you are training you are training a linear you are training a linear assume you are training a linear assume or logistic you are training a linear assume or logistic regression you are training a linear assume or logistic regression model you are training a linear assume or logistic regression model how will you are training a linear assume or logistic regression model how will you affect your you are training a linear assume or logistic regression model how will you affect your model You are training a linear assume or logistic regression model, how will you affect your model? assume that assume that it will assume that it will be assume that it will be outlined Assume that it will be outlined.
2024-08-27 11:50:21.119076: okay Okay. outliers outliers okay Outliers, okay? we have we have statical we have statical methods like we have statical methods like mostly We have statical methods, like mostly. you go to the i you go to the ip you go to the ip address like you go to the ip address like inter you go to the ip address like interquartile you go to the ip address like interquartile range you go to the ip address like interquartile range that's one thing you go to the ip address like interquartile range that's one thing and jet score you go to the ip address like interquartile range that's one thing and jet score is you go to the ip address like interquartile range that's one thing and jet score is there You go to the IP address like interquartile range, that's one thing. And. Jet score is there? that's one that's one more and then that's one more and then visual that's one more and then visual for that's one more and then visual for visual that's one more and then visual for visual method that's one more and then visual for visual methods That's one more. And then visual for visual methods. let me just let me just mention Let me just mention. how how iqr how iqr would work how iqr would work in this how iqr would work in this case How IQR would work in this case. okay fine okay fine the iq okay fine the iqr method okay fine the iqr method invol okay fine the iqr method involves okay fine the iqr method involves calculating okay fine the iqr method involves calculating the first quart okay fine the iqr method involves calculating the first quartile like okay fine the iqr method involves calculating the first quartile like q one okay fine the iqr method involves calculating the first quartile like q one and the third okay fine the iqr method involves calculating the first quartile like q one and the third quart okay fine the iqr method involves calculating the first quartile like q one and the third quartile q okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the ra okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable value okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of ipr okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of ipr from okay fine the iqr method involves calculating the first quartile like q one and the third quartile q three and then using these to find the range of acceptable values like values between like beyond one five one point five times of ipr from q one Okay, fine. The IQR method involves calculating the first quartile, like q one, and the. Third quartile q three, and then using these to find the range of acceptable values like values between like beyond one five 1.5 times of IPR from Q one. and and q and q three and q three q three and q three q three here and q three q three here consider and q three q three here consider as and q three q three here consider as outlay and q three q three here consider as outlayers actually and q three q three here consider as outlayers actually so and q three q three here consider as outlayers actually so in that and q three q three here consider as outlayers actually so in that way and q three q three here consider as outlayers actually so in that way it will be and q three q three here consider as outlayers actually so in that way it will be okay and q three q three here consider as outlayers actually so in that way it will be okay so And Q three. Q three. Here, consider as outlayers, actually. So in that way it will. Be okay, so. that's that's where that's where the high care that's where the high care of that's where the high care of interest That's where the high care of interest. what about what about z What about Z? square square like square like z square square like z square by computing square like z square by computing the z square like z square by computing the z square square like z square by computing the z square which measure square like z square by computing the z square which measures square like z square by computing the z square which measures how many square like z square by computing the z square which measures how many standard dev square like z square by computing the z square which measures how many standard deviations square like z square by computing the z square which measures how many standard deviations in a data square like z square by computing the z square which measures how many standard deviations in a data point square like z square by computing the z square which measures how many standard deviations in a data point from square like z square by computing the z square which measures how many standard deviations in a data point from the mean square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify outlayers square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify outlayers as the value square like z square by computing the z square which measures how many standard deviations in a data point from the mean actually so we can identify outlayers as the value of Square like Z Square, by computing the z square, which measures how many standard deviations in a data point from the mean, actually, so we can identify outlayers as the value of. laying laying beyond the laying beyond the threshold laying beyond the threshold values laying beyond the threshold values like laying beyond the threshold values like between Laying beyond the threshold values like between. generally generally like generally like if it is generally like if it is go with generally like if it is go with the curve Generally like if it is, go with the curve. between between negative between negative to positive between negative to positive like between negative to positive like minus between negative to positive like minus one between negative to positive like minus one two one between negative to positive like minus one two one like minus between negative to positive like minus one two one like minus two to between negative to positive like minus one two one like minus two to two between negative to positive like minus one two one like minus two to two like that we can between negative to positive like minus one two one like minus two to two like that we can go between negative to positive like minus one two one like minus two to two like that we can go with Between negative to positive, like minus one, two one. Like minus two to two. Like that. We can. Go with. okay okay ass okay assume that okay assume that you have okay assume that you have outlier okay assume that you have outlier in okay assume that you have outlier in data okay assume that you have outlier in data and Okay, assume that you have outlier in data and. you are you are training you are training a linear you are training a linear assume you are training a linear assume or logistic you are training a linear assume or logistic regression you are training a linear assume or logistic regression model you are training a linear assume or logistic regression model how will you are training a linear assume or logistic regression model how will you affect your you are training a linear assume or logistic regression model how will you affect your model You are training a linear assume or logistic regression model, how will you affect your model? assume that assume that it will assume that it will be assume that it will be outlined Assume that it will be outlined.
2024-08-27 11:50:56.572850: okay Okay. so so in so in iqr so in iqr you are so in iqr you are saying So in Iqr you are saying. no no i think that you are no i think that you are training a no i think that you are training a model no i think that you are training a model and that model no i think that you are training a model and that model you have no i think that you are training a model and that model you have outline no i think that you are training a model and that model you have outline okay No, I think that you are training a model, and that model you have outline, okay? how how it will affect how it will affect your how it will affect your decision how it will affect your decision what how it will affect your decision what logistic how it will affect your decision what logistic regulation how it will affect your decision what logistic regulation how How it will affect your decision. What? Logistic regulation? How? it will it will affect it will affect the line it will affect the line of it will affect the line of best fit it will affect the line of best fitting your it will affect the line of best fitting your supplier it will affect the line of best fitting your supplieration model It will affect the line of best fitting your supplieration model. choose Choose. any Any. how how it will imp how it will impact how it will impact how how it will impact how it will impact
2024-08-27 11:50:56.572850: okay Okay. so so in so in iqr so in iqr you are so in iqr you are saying So in Iqr you are saying. no no i think that you are no i think that you are training a no i think that you are training a model no i think that you are training a model and that model no i think that you are training a model and that model you have no i think that you are training a model and that model you have outline no i think that you are training a model and that model you have outline okay No, I think that you are training a model, and that model you have outline, okay? how how it will affect how it will affect your how it will affect your decision how it will affect your decision what how it will affect your decision what logistic how it will affect your decision what logistic regulation how it will affect your decision what logistic regulation how How it will affect your decision. What? Logistic regulation? How? it will it will affect it will affect the line it will affect the line of it will affect the line of best fit it will affect the line of best fitting your it will affect the line of best fitting your supplier it will affect the line of best fitting your supplieration model It will affect the line of best fitting your supplieration model. choose Choose. any Any. how how it will imp how it will impact how it will impact how how it will impact how it will impact
2024-08-27 11:52:09.746102: How it will impact. How it will impact. generally generally outli generally outliers generally outliers can generally outliers can mostly generally outliers can mostly go with the generally outliers can mostly go with the skew generally outliers can mostly go with the skew while going generally outliers can mostly go with the skew while going the generally outliers can mostly go with the skew while going the parameter generally outliers can mostly go with the skew while going the parameter estimation generally outliers can mostly go with the skew while going the parameter estimation like generally outliers can mostly go with the skew while going the parameter estimation like leading to poor generally outliers can mostly go with the skew while going the parameter estimation like leading to poor perform generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic reg generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression reg generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regress generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regression generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regression outlayer generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regression outlayer can be Generally, outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression. Regression like in linear regression. Outlayer can be. mostly mostly the influen mostly the influence of mostly the influence of the mostly the influence of the slope mostly the influence of the slope is Mostly the influence of the slope is. intersect intersect the intersect the values intersect the values actually intersect the values actually so intersect the values actually so like that intersect the values actually so like that it will intersect the values actually so like that it will go with intersect the values actually so like that it will go with and intersect the values actually so like that it will go with and you want intersect the values actually so like that it will go with and you want to be explain intersect the values actually so like that it will go with and you want to be explained completely intersect the values actually so like that it will go with and you want to be explained completely when intersect the values actually so like that it will go with and you want to be explained completely when going intersect the values actually so like that it will go with and you want to be explained completely when going that intersect the values actually so like that it will go with and you want to be explained completely when going that how it intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like signific intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation in the intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation in the data set intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation in the data set like Intersect the values, actually. So like that it will go with. And you want to be explained completely. When going that how it will impact the things is like when going with the data points are there, like, significantly deviate from the other observation in the data set, like. the measurements the measurements of the the measurements of the errors the measurements of the errors and The measurements of the errors and. i want to i want to know the imp i want to know the impact i want to know the impact of out i want to know the impact of outlier i want to know the impact of outlier on your i want to know the impact of outlier on your weight i want to know the impact of outlier on your weight of the model i want to know the impact of outlier on your weight of the model weight i want to know the impact of outlier on your weight of the model weights of the model i want to know the impact of outlier on your weight of the model weights of the model basically I want to know the impact of outlier on your weight of the model weights of the model, basically. understanding understanding your understanding your point understanding your point the impact understanding your point the impact of outline understanding your point the impact of outline on weight Understanding your point. The impact of outline on weight. not not other not other way around you not other way around you you are not other way around you you are telling me not other way around you you are telling me the imp not other way around you you are telling me the impact not other way around you you are telling me the impact of Not other way around. You. You are telling me the impact of. model model on outcome Model on outcome. that's the thing That's the thing. okay
2024-08-27 11:52:09.747677: How it will impact. How it will impact. generally generally outli generally outliers generally outliers can generally outliers can mostly generally outliers can mostly go with the generally outliers can mostly go with the skew generally outliers can mostly go with the skew while going generally outliers can mostly go with the skew while going the generally outliers can mostly go with the skew while going the parameter generally outliers can mostly go with the skew while going the parameter estimation generally outliers can mostly go with the skew while going the parameter estimation like generally outliers can mostly go with the skew while going the parameter estimation like leading to poor generally outliers can mostly go with the skew while going the parameter estimation like leading to poor perform generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic reg generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression reg generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regress generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regression generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regression outlayer generally outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression regression like in linear regression outlayer can be Generally, outliers can mostly go with the skew while going the parameter estimation like leading to poor performance like in linear regression or in logic regression. Regression like in linear regression. Outlayer can be. mostly mostly the influen mostly the influence of mostly the influence of the mostly the influence of the slope mostly the influence of the slope is Mostly the influence of the slope is. intersect intersect the intersect the values intersect the values actually intersect the values actually so intersect the values actually so like that intersect the values actually so like that it will intersect the values actually so like that it will go with intersect the values actually so like that it will go with and intersect the values actually so like that it will go with and you want intersect the values actually so like that it will go with and you want to be explain intersect the values actually so like that it will go with and you want to be explained completely intersect the values actually so like that it will go with and you want to be explained completely when intersect the values actually so like that it will go with and you want to be explained completely when going intersect the values actually so like that it will go with and you want to be explained completely when going that intersect the values actually so like that it will go with and you want to be explained completely when going that how it intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like signific intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation in the intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation in the data set intersect the values actually so like that it will go with and you want to be explained completely when going that how it will impact the things is like when going with the data points are there like significantly deviate from the other observation in the data set like Intersect the values, actually. So like that it will go with. And you want to be explained completely. When going that how it will impact the things is like when going with the data points are there, like, significantly deviate from the other observation in the data set, like. the measurements the measurements of the the measurements of the errors the measurements of the errors and The measurements of the errors and. i want to i want to know the imp i want to know the impact i want to know the impact of out i want to know the impact of outlier i want to know the impact of outlier on your i want to know the impact of outlier on your weight i want to know the impact of outlier on your weight of the model i want to know the impact of outlier on your weight of the model weight i want to know the impact of outlier on your weight of the model weights of the model i want to know the impact of outlier on your weight of the model weights of the model basically I want to know the impact of outlier on your weight of the model weights of the model, basically. understanding understanding your understanding your point understanding your point the impact understanding your point the impact of outline understanding your point the impact of outline on weight Understanding your point. The impact of outline on weight. not not other not other way around you not other way around you you are not other way around you you are telling me not other way around you you are telling me the imp not other way around you you are telling me the impact not other way around you you are telling me the impact of Not other way around. You. You are telling me the impact of. model model on outcome Model on outcome. that's the thing That's the thing. okay
2024-08-27 11:52:45.864835: Okay. so so in so in outlet sign so in outlet significantly so in outlet significantly weights of so in outlet significantly weights of a model so in outlet significantly weights of a model for linear so in outlet significantly weights of a model for linear regression so in outlet significantly weights of a model for linear regression outliers so in outlet significantly weights of a model for linear regression outliers can so in outlet significantly weights of a model for linear regression outliers can heavily so in outlet significantly weights of a model for linear regression outliers can heavily influence So in outlet significantly, weights of a model for linear regression outliers can heavily influence. on the slope on the slope and on the slope and going on the slope and going with On the slope and going with. the benefit the benefit line the benefit line won't be the benefit line won't be fit actually The benefit line won't be fit, actually. outline outliners can outliners can skew outliners can skew skew outliners can skew skew the outliners can skew skew the decision boundar outliners can skew skew the decision boundaries Outliners can skew skew the decision boundaries. and And. going with going with the accurac going with the accuracy going with the accuracy and then going with the accuracy and then that will going with the accuracy and then that will impact going with the accuracy and then that will impact mostly going with the accuracy and then that will impact mostly and then going with the accuracy and then that will impact mostly and then the going with the accuracy and then that will impact mostly and then the reliability Going with the accuracy, and then that will impact mostly, and then the reliability. ten power ten power hyper ten power hyperlinks test ten power hyperlinks testing Ten power hyperlinks. Testing. hypoth hypothesis
2024-08-27 11:52:45.864835: Okay. so so in so in outlet sign so in outlet significantly so in outlet significantly weights of so in outlet significantly weights of a model so in outlet significantly weights of a model for linear so in outlet significantly weights of a model for linear regression so in outlet significantly weights of a model for linear regression outliers so in outlet significantly weights of a model for linear regression outliers can so in outlet significantly weights of a model for linear regression outliers can heavily so in outlet significantly weights of a model for linear regression outliers can heavily influence So in outlet significantly, weights of a model for linear regression outliers can heavily influence. on the slope on the slope and on the slope and going on the slope and going with On the slope and going with. the benefit the benefit line the benefit line won't be the benefit line won't be fit actually The benefit line won't be fit, actually. outline outliners can outliners can skew outliners can skew skew outliners can skew skew the outliners can skew skew the decision boundar outliners can skew skew the decision boundaries Outliners can skew skew the decision boundaries. and And. going with going with the accurac going with the accuracy going with the accuracy and then going with the accuracy and then that will going with the accuracy and then that will impact going with the accuracy and then that will impact mostly going with the accuracy and then that will impact mostly and then going with the accuracy and then that will impact mostly and then the going with the accuracy and then that will impact mostly and then the reliability Going with the accuracy, and then that will impact mostly, and then the reliability. ten power ten power hyper ten power hyperlinks test ten power hyperlinks testing Ten power hyperlinks. Testing. hypoth hypothesis
2024-08-27 11:53:09.985822: hypothesis test hypothesis testing Hypothesis testing. like like hypoth like hypothesis Like hypothesis. to to the point to the pointer to the pointer yes to the pointer yes okay To the pointer. Yes. Okay. fine Fine. exactly Exactly. i mean i mean what is i mean what is why i mean what is why we i mean what is why we are using i mean what is why we are using hypothes i mean what is why we are using hypothesis test i mean what is why we are using hypothesis testing I mean, what is why we are using hypothesis testing? what what is what is abd what is abdic What is abdic?
2024-08-27 11:54:08.427701: okay okay like okay like hypothesis okay like hypothesis testing okay like hypothesis testing is like okay like hypothesis testing is like used okay like hypothesis testing is like used for most okay like hypothesis testing is like used for mostly okay like hypothesis testing is like used for mostly to inter okay like hypothesis testing is like used for mostly to interface okay like hypothesis testing is like used for mostly to interface like okay like hypothesis testing is like used for mostly to interface like a conclusion about Okay, like hypothesis testing is like used for mostly to interface like a conclusion about. a population a population based a population based on a population based on a sample a population based on a sample data a population based on a sample data like it a population based on a sample data like it helps us a population based on a sample data like it helps us to a population based on a sample data like it helps us to deter a population based on a sample data like it helps us to deter like a population based on a sample data like it helps us to deter like go through a population based on a sample data like it helps us to deter like go through the evide a population based on a sample data like it helps us to deter like go through the evidence like a population based on a sample data like it helps us to deter like go through the evidence like kind a population based on a sample data like it helps us to deter like go through the evidence like kind of A population based on a sample data, like it helps us to deter, like go through the evidence. Like, kind of. particular particular benef particular benefit particular benefit of particular benefit of the hyp particular benefit of the hypothesis Particular benefit of the hypothesis. like like it goes like it goes a standard like it goes a standard term like it goes a standard term like most like it goes a standard term like mostly like it goes a standard term like mostly with the test like it goes a standard term like mostly with the testing Like it goes a standard term, like mostly with the testing. with With. ab testing ab testing and ab testing and those ab testing and those kind of things ab testing and those kind of things we can ab testing and those kind of things we can use with ab testing and those kind of things we can use with the hyp ab testing and those kind of things we can use with the hypothesis ab testing and those kind of things we can use with the hypothesis testing Ab testing and those kind of things we can use with the hypothesis testing. i like it i like it mostly i like it mostly what's the population i like it mostly what's the population parameters i like it mostly what's the population parameters based on i like it mostly what's the population parameters based on a sample i like it mostly what's the population parameters based on a sample data like i like it mostly what's the population parameters based on a sample data like it is i like it mostly what's the population parameters based on a sample data like it is simple you can i like it mostly what's the population parameters based on a sample data like it is simple you can say i like it mostly what's the population parameters based on a sample data like it is simple you can say that I like it. Mostly. What's the population parameters based on a sample data like it is. Simple. You can say that. so what so what is p value so what is p value in hybrid so what is p value in hybrid is testing So what is p value in hybrid is testing. what is what is p value What is p value? like like p value like p value measure like p value measures that prob like p value measures that probability like p value measures that probability of like p value measures that probability of like ob like p value measures that probability of like obtained like p value measures that probability of like obtained the obser like p value measures that probability of like obtained the observes like like p value measures that probability of like obtained the observes like result like p value measures that probability of like obtained the observes like results like p value measures that probability of like obtained the observes like results assuming that like p value measures that probability of like obtained the observes like results assuming that null hypothes like p value measures that probability of like obtained the observes like results assuming that null hypothesis is like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's consider like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's considered as like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's considered as a p like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's considered as a p value
2024-08-27 11:54:08.428829: okay okay like okay like hypothesis okay like hypothesis testing okay like hypothesis testing is like okay like hypothesis testing is like used okay like hypothesis testing is like used for most okay like hypothesis testing is like used for mostly okay like hypothesis testing is like used for mostly to inter okay like hypothesis testing is like used for mostly to interface okay like hypothesis testing is like used for mostly to interface like okay like hypothesis testing is like used for mostly to interface like a conclusion about Okay, like hypothesis testing is like used for mostly to interface like a conclusion about. a population a population based a population based on a population based on a sample a population based on a sample data a population based on a sample data like it a population based on a sample data like it helps us a population based on a sample data like it helps us to a population based on a sample data like it helps us to deter a population based on a sample data like it helps us to deter like a population based on a sample data like it helps us to deter like go through a population based on a sample data like it helps us to deter like go through the evide a population based on a sample data like it helps us to deter like go through the evidence like a population based on a sample data like it helps us to deter like go through the evidence like kind a population based on a sample data like it helps us to deter like go through the evidence like kind of A population based on a sample data, like it helps us to deter, like go through the evidence. Like, kind of. particular particular benef particular benefit particular benefit of particular benefit of the hyp particular benefit of the hypothesis Particular benefit of the hypothesis. like like it goes like it goes a standard like it goes a standard term like it goes a standard term like most like it goes a standard term like mostly like it goes a standard term like mostly with the test like it goes a standard term like mostly with the testing Like it goes a standard term, like mostly with the testing. with With. ab testing ab testing and ab testing and those ab testing and those kind of things ab testing and those kind of things we can ab testing and those kind of things we can use with ab testing and those kind of things we can use with the hyp ab testing and those kind of things we can use with the hypothesis ab testing and those kind of things we can use with the hypothesis testing Ab testing and those kind of things we can use with the hypothesis testing. i like it i like it mostly i like it mostly what's the population i like it mostly what's the population parameters i like it mostly what's the population parameters based on i like it mostly what's the population parameters based on a sample i like it mostly what's the population parameters based on a sample data like i like it mostly what's the population parameters based on a sample data like it is i like it mostly what's the population parameters based on a sample data like it is simple you can i like it mostly what's the population parameters based on a sample data like it is simple you can say i like it mostly what's the population parameters based on a sample data like it is simple you can say that I like it. Mostly. What's the population parameters based on a sample data like it is. Simple. You can say that. so what so what is p value so what is p value in hybrid so what is p value in hybrid is testing So what is p value in hybrid is testing. what is what is p value What is p value? like like p value like p value measure like p value measures that prob like p value measures that probability like p value measures that probability of like p value measures that probability of like ob like p value measures that probability of like obtained like p value measures that probability of like obtained the obser like p value measures that probability of like obtained the observes like like p value measures that probability of like obtained the observes like result like p value measures that probability of like obtained the observes like results like p value measures that probability of like obtained the observes like results assuming that like p value measures that probability of like obtained the observes like results assuming that null hypothes like p value measures that probability of like obtained the observes like results assuming that null hypothesis is like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's consider like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's considered as like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's considered as a p like p value measures that probability of like obtained the observes like results assuming that null hypothesis is true that's considered as a p value
2024-08-27 11:54:21.850300: and then
2024-08-27 11:54:21.852874: and then
2024-08-27 11:54:35.075642: which Which. hybrid is testing hybrid is testing algorith hybrid is testing algorithm hybrid is testing algorithm which you use hybrid is testing algorithm which you use in this case Hybrid is testing algorithm which you use in this case. you are you are comparing the you are comparing the sample you are comparing the sample with the you are comparing the sample with the population you are comparing the sample with the population population you are comparing the sample with the population population yes You are comparing the sample with the population. Population, yes.
2024-08-27 11:54:35.081836: which Which. hybrid is testing hybrid is testing algorith hybrid is testing algorithm hybrid is testing algorithm which you use hybrid is testing algorithm which you use in this case Hybrid is testing algorithm which you use in this case. you are you are comparing the you are comparing the sample you are comparing the sample with the you are comparing the sample with the population you are comparing the sample with the population population you are comparing the sample with the population population yes You are comparing the sample with the population. Population, yes.
2024-08-27 11:55:04.889172: which hyp which hypothesis which hypothesis testing which hypothesis testing we use like which hypothesis testing we use like hypothes which hypothesis testing we use like hypothesis al which hypothesis testing we use like hypothesis algorithm which hypothesis testing we use like hypothesis algorithm this which hypothesis testing we use like hypothesis algorithm this grass val which hypothesis testing we use like hypothesis algorithm this grass validation which hypothesis testing we use like hypothesis algorithm this grass validation like which hypothesis testing we use like hypothesis algorithm this grass validation like combined the which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyper which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evalu which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model acc which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case we use which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case we use the which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case we use the grass violation Which hypothesis testing we use like hypothesis algorithm, this grass validation, like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy. So in that case, we use the grass violation. do you do you heard about do you heard about kf do you heard about kfold also do you heard about kfold also we can hear do you heard about kfold also we can hear kfold do you heard about kfold also we can hear kfold grass do you heard about kfold also we can hear kfold grass foundation Do you heard about Kfold also? We can hear Kfold grass foundation. t t test t test independent T test independent. yes yes actually Yes, actually. you want to explain you want to explain on the t test you want to explain on the t test inde
2024-08-27 11:55:04.890166: which hyp which hypothesis which hypothesis testing which hypothesis testing we use like which hypothesis testing we use like hypothes which hypothesis testing we use like hypothesis al which hypothesis testing we use like hypothesis algorithm which hypothesis testing we use like hypothesis algorithm this which hypothesis testing we use like hypothesis algorithm this grass val which hypothesis testing we use like hypothesis algorithm this grass validation which hypothesis testing we use like hypothesis algorithm this grass validation like which hypothesis testing we use like hypothesis algorithm this grass validation like combined the which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyper which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evalu which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model acc which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case we use which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case we use the which hypothesis testing we use like hypothesis algorithm this grass validation like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy so in that case we use the grass violation Which hypothesis testing we use like hypothesis algorithm, this grass validation, like combined the transfer learning and that hyperparameter tuning to evaluate that improve the model accuracy. So in that case, we use the grass violation. do you do you heard about do you heard about kf do you heard about kfold also do you heard about kfold also we can hear do you heard about kfold also we can hear kfold do you heard about kfold also we can hear kfold grass do you heard about kfold also we can hear kfold grass foundation Do you heard about Kfold also? We can hear Kfold grass foundation. t t test t test independent T test independent. yes yes actually Yes, actually. you want to explain you want to explain on the t test you want to explain on the t test inde
2024-08-27 11:55:22.562187: you want to explain on the t test independent You want to explain on the t test? Independent? i've i've done i've done it very i've done it very recently i've done it very recently have done i've done it very recently have done so okay i've done it very recently have done so okay fine I've done it very recently. Have done so. Okay, fine. app App. apple apple i remember Apple. I remember. tdest
2024-08-27 11:55:22.562187: you want to explain on the t test independent You want to explain on the t test? Independent? i've i've done i've done it very i've done it very recently i've done it very recently have done i've done it very recently have done so okay i've done it very recently have done so okay fine I've done it very recently. Have done so. Okay, fine. app App. apple apple i remember Apple. I remember. tdest
2024-08-27 11:56:06.971093: tdest hyper tdest hyperpower method tdest hyperpower method you may tdest hyperpower method you may have tdest hyperpower method you may have heard Tdest hyperpower method. You may have heard. mostly Mostly. i got i got some i got some basic i got some basic idea I got some basic idea. like like when going Like when going. with with us with us accuracy with us accuracy perform with us accuracy performance with us accuracy performance where we can go with us accuracy performance where we can go with with us accuracy performance where we can go with when we with us accuracy performance where we can go with when we apply with us accuracy performance where we can go with when we apply this transfer with us accuracy performance where we can go with when we apply this transfer learning with us accuracy performance where we can go with when we apply this transfer learning and with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with that with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with that t test actually with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with that t test actually like with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with that t test actually like pair t test with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with that t test actually like pair t test was used with us accuracy performance where we can go with when we apply this transfer learning and hyperparameter tune that's where go with that t test actually like pair t test was used actually With us accuracy, performance where we can go with when we apply this transfer learning and hyperparameter tune. That's where go with that t test. Actually like pair t test was used. Actually. yeah Yeah. okay okay assuming that Okay, assuming that. you have data You have data? points points which are points which are greater than points which are greater than thirty Points which are greater than 30. okay okay m great okay m greater than okay m greater than thirty okay m greater than thirty so okay m greater than thirty so in that okay m greater than thirty so in that case okay m greater than thirty so in that case what okay m greater than thirty so in that case what is better Okay. M greater than 30. So in that case, what is better? way way you can use way you can use z test way you can use z test or you can way you can use z test or you can use t way you can use z test or you can use t test or way you can use z test or you can use t test or you can use way you can use z test or you can use t test or you can use both way you can use z test or you can use t test or you can use both also
2024-08-27 11:57:59.998955: Way. You can use Z test or you can use t test or you can use both also. number of data points number of data points are more than number of data points are more than thirty number of data points are more than thirty okay number of data points are more than thirty okay like number of data points are more than thirty okay like generally number of data points are more than thirty okay like generally we can number of data points are more than thirty okay like generally we can go with number of data points are more than thirty okay like generally we can go with g test number of data points are more than thirty okay like generally we can go with g test actually number of data points are more than thirty okay like generally we can go with g test actually due to number of data points are more than thirty okay like generally we can go with g test actually due to the number of data points are more than thirty okay like generally we can go with g test actually due to the central limit number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like in that case number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like in that case actually number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like in that case actually we go with Number of data points are more than 30. Okay, like, generally, we can go with g test, actually. Due to the central limit theorem. Like, mostly we go with the population. Standard deviation is unknown. Like, in that case, actually, we go with. the popul the population the population is unknown the population is unknown we can the population is unknown we can go with the the population is unknown we can go with the t test The population is unknown. We can go with the t test. if if these were already if these were already know if these were already know the things if these were already know the things data points if these were already know the things data points are there if these were already know the things data points are there then if these were already know the things data points are there then we go with if these were already know the things data points are there then we go with the if these were already know the things data points are there then we go with the z test If these were already know the things data points are there. Then we go with the Z test. okay Okay. mostly mostly in the transfer mostly in the transfer level like mostly in the transfer level like in hyper mostly in the transfer level like in hyperparameter mostly in the transfer level like in hyperparameter training mostly in the transfer level like in hyperparameter training to improve mostly in the transfer level like in hyperparameter training to improve the model mostly in the transfer level like in hyperparameter training to improve the model accurac mostly in the transfer level like in hyperparameter training to improve the model accuracy mostly in the transfer level like in hyperparameter training to improve the model accuracy we go mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the eval mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the evaluation perform mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the evaluation performance mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the evaluation performance gains through Mostly in the transfer level, like in hyperparameter training. To improve the model accuracy, we go with the evaluation, performance gains through. static static methods static methods only mostly static methods only mostly we static methods only mostly we go static methods only mostly we go with that static methods only mostly we go with that and Static methods only. Mostly we go with that and. when when performing the when performing the metrics when performing the metrics before going when performing the metrics before going to the applying when performing the metrics before going to the applying the transfer when performing the metrics before going to the applying the transfer learning When performing the metrics before going to the applying the transfer learning. or Or. hyper hyperparameter Hyperparameter. in the in the jet test in the jet test actually what in the jet test actually what is happening in the jet test actually what is happening is in the jet test actually what is happening is like in the jet test actually what is happening is like the sample in the jet test actually what is happening is like the sample size in the jet test actually what is happening is like the sample size is in the jet test actually what is happening is like the sample size is number of in the jet test actually what is happening is like the sample size is number of n it is in the jet test actually what is happening is like the sample size is number of n it is greater than in the jet test actually what is happening is like the sample size is number of n it is greater than thirty in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit the in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we go with the in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we go with the z in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we go with the z test In the jet test. Actually, what is happening is like the sample size is number of n. It is greater than 30. As per the center limit theorem, we go with the Z test. that's where that's where it that's where it suits that's where it suits actually That's where it suits, actually. share your share your screen share your screen and share your screen and i will share your screen and i will tell you one share your screen and i will tell you one python share your screen and i will tell you one python code Share your screen and I will tell you one python code. okay sure Okay, sure. can you open can you open collab or can you open collab or jupyter Can you open collab or Jupyter? okay Okay. write write a code write a code which write a code which will write a code which will find write a code which will find the maximum write a code which will find the maximum from write a code which will find the maximum from a list Write a code which will find the maximum from a list. and and you cannot and you cannot use the and you cannot use the inbuilt and you cannot use the inbuilt maps and you cannot use the inbuilt maps function and you cannot use the inbuilt maps function on py and you cannot use the inbuilt maps function on python And you cannot use the inbuilt maps function on python. okay Okay. l equal to l equal to list l equal to list and you have to find l equal to list and you have to find the maximum l equal to list and you have to find the maximum from l equal to list and you have to find the maximum from that list l equal to list and you have to find the maximum from that list and l equal to list and you have to find the maximum from that list and you cannot l equal to list and you have to find the maximum from that list and you cannot use l equal to list and you have to find the maximum from that list and you cannot use the l equal to list and you have to find the maximum from that list and you cannot use the inbuilt l equal to list and you have to find the maximum from that list and you cannot use the inbuilt function l equal to list and you have to find the maximum from that list and you cannot use the inbuilt function max L equal to list. And you have to find the maximum from that list. And you cannot use the inbuilt function, max.
2024-08-27 11:58:00.000053: Way. You can use Z test or you can use t test or you can use both also. number of data points number of data points are more than number of data points are more than thirty number of data points are more than thirty okay number of data points are more than thirty okay like number of data points are more than thirty okay like generally number of data points are more than thirty okay like generally we can number of data points are more than thirty okay like generally we can go with number of data points are more than thirty okay like generally we can go with g test number of data points are more than thirty okay like generally we can go with g test actually number of data points are more than thirty okay like generally we can go with g test actually due to number of data points are more than thirty okay like generally we can go with g test actually due to the number of data points are more than thirty okay like generally we can go with g test actually due to the central limit number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like in that case number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like in that case actually number of data points are more than thirty okay like generally we can go with g test actually due to the central limit theorem like mostly we go with the population standard deviation is unknown like in that case actually we go with Number of data points are more than 30. Okay, like, generally, we can go with g test, actually. Due to the central limit theorem. Like, mostly we go with the population. Standard deviation is unknown. Like, in that case, actually, we go with. the popul the population the population is unknown the population is unknown we can the population is unknown we can go with the the population is unknown we can go with the t test The population is unknown. We can go with the t test. if if these were already if these were already know if these were already know the things if these were already know the things data points if these were already know the things data points are there if these were already know the things data points are there then if these were already know the things data points are there then we go with if these were already know the things data points are there then we go with the if these were already know the things data points are there then we go with the z test If these were already know the things data points are there. Then we go with the Z test. okay Okay. mostly mostly in the transfer mostly in the transfer level like mostly in the transfer level like in hyper mostly in the transfer level like in hyperparameter mostly in the transfer level like in hyperparameter training mostly in the transfer level like in hyperparameter training to improve mostly in the transfer level like in hyperparameter training to improve the model mostly in the transfer level like in hyperparameter training to improve the model accurac mostly in the transfer level like in hyperparameter training to improve the model accuracy mostly in the transfer level like in hyperparameter training to improve the model accuracy we go mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the eval mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the evaluation perform mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the evaluation performance mostly in the transfer level like in hyperparameter training to improve the model accuracy we go with the evaluation performance gains through Mostly in the transfer level, like in hyperparameter training. To improve the model accuracy, we go with the evaluation, performance gains through. static static methods static methods only mostly static methods only mostly we static methods only mostly we go static methods only mostly we go with that static methods only mostly we go with that and Static methods only. Mostly we go with that and. when when performing the when performing the metrics when performing the metrics before going when performing the metrics before going to the applying when performing the metrics before going to the applying the transfer when performing the metrics before going to the applying the transfer learning When performing the metrics before going to the applying the transfer learning. or Or. hyper hyperparameter Hyperparameter. in the in the jet test in the jet test actually what in the jet test actually what is happening in the jet test actually what is happening is in the jet test actually what is happening is like in the jet test actually what is happening is like the sample in the jet test actually what is happening is like the sample size in the jet test actually what is happening is like the sample size is in the jet test actually what is happening is like the sample size is number of in the jet test actually what is happening is like the sample size is number of n it is in the jet test actually what is happening is like the sample size is number of n it is greater than in the jet test actually what is happening is like the sample size is number of n it is greater than thirty in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit the in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we go with the in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we go with the z in the jet test actually what is happening is like the sample size is number of n it is greater than thirty as per the center limit theorem we go with the z test In the jet test. Actually, what is happening is like the sample size is number of n. It is greater than 30. As per the center limit theorem, we go with the Z test. that's where that's where it that's where it suits that's where it suits actually That's where it suits, actually. share your share your screen share your screen and share your screen and i will share your screen and i will tell you one share your screen and i will tell you one python share your screen and i will tell you one python code Share your screen and I will tell you one python code. okay sure Okay, sure. can you open can you open collab or can you open collab or jupyter Can you open collab or Jupyter? okay Okay. write write a code write a code which write a code which will write a code which will find write a code which will find the maximum write a code which will find the maximum from write a code which will find the maximum from a list Write a code which will find the maximum from a list. and and you cannot and you cannot use the and you cannot use the inbuilt and you cannot use the inbuilt maps and you cannot use the inbuilt maps function and you cannot use the inbuilt maps function on py and you cannot use the inbuilt maps function on python And you cannot use the inbuilt maps function on python. okay Okay. l equal to l equal to list l equal to list and you have to find l equal to list and you have to find the maximum l equal to list and you have to find the maximum from l equal to list and you have to find the maximum from that list l equal to list and you have to find the maximum from that list and l equal to list and you have to find the maximum from that list and you cannot l equal to list and you have to find the maximum from that list and you cannot use l equal to list and you have to find the maximum from that list and you cannot use the l equal to list and you have to find the maximum from that list and you cannot use the inbuilt l equal to list and you have to find the maximum from that list and you cannot use the inbuilt function l equal to list and you have to find the maximum from that list and you cannot use the inbuilt function max L equal to list. And you have to find the maximum from that list. And you cannot use the inbuilt function, max.
2024-08-27 12:01:41.166575: function function max Function, max. okay consider okay consider i'm taking okay consider i'm taking like okay consider i'm taking like max value Okay, consider I'm taking, like, max value. i can't i can't use the i can't use the max value i can't use the max value also I can't use the max value also. yeah yeah you cannot yeah you cannot use Yeah. You cannot use. okay Okay. x x function x function max value X function max value. can i can i go with can i go with for loop can i go with for loop right can i go with for loop right for can i go with for loop right for an int can i go with for loop right for an integer Can I go with for loop? Right for an integer? yeah yeah for the p can yeah for the p can go yeah for the p can go right Yeah, for the p can go right. okay okay just okay just don't okay just don't use inbuilt okay just don't use inbuilt function okay just don't use inbuilt function okay fine okay just don't use inbuilt function okay fine got it Okay. Just don't use inbuilt function. Okay, fine. Got it. so so i'm taking so i'm taking a sample list so i'm taking a sample list here So I'm taking a sample list here. that's the same thing That's the same thing. so so after that so after that we go to so after that we go to the next so after that we go to the next value So after that, we go to the next value. in in this case In this case. i will i will go with an i will go with an almond I will go with an almond. execute execute it Execute it. yeah yeah i think yeah i think this will yeah i think this will work yeah i think this will work so yeah i think this will work so it will yeah i think this will work so it will what it do Yeah, I think this will work. So it will. What it do? yeah Yeah. what it do what it do is here what it do is here like what it do is here like using What it do is here, like using. the the max the max function The max function. so like So, like. in in sharing the in sharing the max value in sharing the max values in sharing the max values for the first element In sharing the max values for the first element. of the value of the value and of the value and then of the value and then iter of the value and then iterate the of the value and then iterate the remaining of the value and then iterate the remaining elements of the value and then iterate the remaining elements from of the value and then iterate the remaining elements from the score of the value and then iterate the remaining elements from the scored values of the value and then iterate the remaining elements from the scored values whatever it of the value and then iterate the remaining elements from the scored values whatever it will go of the value and then iterate the remaining elements from the scored values whatever it will go into the loop Of the value and then iterate the remaining elements from the scored values, whatever it will go. Into the loop. which it will which it will go with whatever which it will go with whatever the which it will go with whatever the twelve which it will go with whatever the twelve is there which it will go with whatever the twelve is there and then which it will go with whatever the twelve is there and then if we store Which it will go with whatever the twelve is there. And then if we store. in the in the first value in the first value and then it will in the first value and then it will compare in the first value and then it will compare with the next in the first value and then it will compare with the next value in the first value and then it will compare with the next value and then like in the first value and then it will compare with the next value and then like that it will in the first value and then it will compare with the next value and then like that it will go with in the first value and then it will compare with the next value and then like that it will go with till in the first value and then it will compare with the next value and then like that it will go with till until in the first value and then it will compare with the next value and then like that it will go with till until the find in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine the for in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine the for loop will in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine the for loop will be used In the first value, and then it will compare with the next value, and then, like that, it will go with till until the find the maximum value. It will be fine. The for loop will be used. okay Okay. what do you know about what do you know about ll what do you know about llms What do you know about llms? i'm i'm sorry I'm sorry. what what lns what lns large what lns large language What? Lns large language. llms Llms. what we know What we know. i've used
2024-08-27 12:01:41.166575: function function max Function, max. okay consider okay consider i'm taking okay consider i'm taking like okay consider i'm taking like max value Okay, consider I'm taking, like, max value. i can't i can't use the i can't use the max value i can't use the max value also I can't use the max value also. yeah yeah you cannot yeah you cannot use Yeah. You cannot use. okay Okay. x x function x function max value X function max value. can i can i go with can i go with for loop can i go with for loop right can i go with for loop right for can i go with for loop right for an int can i go with for loop right for an integer Can I go with for loop? Right for an integer? yeah yeah for the p can yeah for the p can go yeah for the p can go right Yeah, for the p can go right. okay okay just okay just don't okay just don't use inbuilt okay just don't use inbuilt function okay just don't use inbuilt function okay fine okay just don't use inbuilt function okay fine got it Okay. Just don't use inbuilt function. Okay, fine. Got it. so so i'm taking so i'm taking a sample list so i'm taking a sample list here So I'm taking a sample list here. that's the same thing That's the same thing. so so after that so after that we go to so after that we go to the next so after that we go to the next value So after that, we go to the next value. in in this case In this case. i will i will go with an i will go with an almond I will go with an almond. execute execute it Execute it. yeah yeah i think yeah i think this will yeah i think this will work yeah i think this will work so yeah i think this will work so it will yeah i think this will work so it will what it do Yeah, I think this will work. So it will. What it do? yeah Yeah. what it do what it do is here what it do is here like what it do is here like using What it do is here, like using. the the max the max function The max function. so like So, like. in in sharing the in sharing the max value in sharing the max values in sharing the max values for the first element In sharing the max values for the first element. of the value of the value and of the value and then of the value and then iter of the value and then iterate the of the value and then iterate the remaining of the value and then iterate the remaining elements of the value and then iterate the remaining elements from of the value and then iterate the remaining elements from the score of the value and then iterate the remaining elements from the scored values of the value and then iterate the remaining elements from the scored values whatever it of the value and then iterate the remaining elements from the scored values whatever it will go of the value and then iterate the remaining elements from the scored values whatever it will go into the loop Of the value and then iterate the remaining elements from the scored values, whatever it will go. Into the loop. which it will which it will go with whatever which it will go with whatever the which it will go with whatever the twelve which it will go with whatever the twelve is there which it will go with whatever the twelve is there and then which it will go with whatever the twelve is there and then if we store Which it will go with whatever the twelve is there. And then if we store. in the in the first value in the first value and then it will in the first value and then it will compare in the first value and then it will compare with the next in the first value and then it will compare with the next value in the first value and then it will compare with the next value and then like in the first value and then it will compare with the next value and then like that it will in the first value and then it will compare with the next value and then like that it will go with in the first value and then it will compare with the next value and then like that it will go with till in the first value and then it will compare with the next value and then like that it will go with till until in the first value and then it will compare with the next value and then like that it will go with till until the find in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine the for in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine the for loop will in the first value and then it will compare with the next value and then like that it will go with till until the find the maximum value it will be fine the for loop will be used In the first value, and then it will compare with the next value, and then, like that, it will go with till until the find the maximum value. It will be fine. The for loop will be used. okay Okay. what do you know about what do you know about ll what do you know about llms What do you know about llms? i'm i'm sorry I'm sorry. what what lns what lns large what lns large language What? Lns large language. llms Llms. what we know What we know. i've used
2024-08-27 12:01:51.279070: i've used for one i've used for one project i've used for one project only i've used for one project only with i've used for one project only with birth I've used for one project only with birth. most mostly mostly what purpose mostly what purpose you use it Mostly what purpose you use it.
2024-08-27 12:01:51.279070: i've used for one i've used for one project i've used for one project only i've used for one project only with i've used for one project only with birth I've used for one project only with birth. most mostly mostly what purpose mostly what purpose you use it Mostly what purpose you use it.
