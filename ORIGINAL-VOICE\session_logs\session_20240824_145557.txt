2024-08-24 15:05:21.336101: can you can you ask me Can you ask me? right Right. we'll pick up we'll pick up a we'll pick up a solution We'll pick up a solution. but but i know in the same but i know in the same call But I know in the same call. some of these model some of these models some of these models was used some of these models was used done on cloud Some of these models was used done on cloud. where you put where you put batch of imag where you put batch of images where you put batch of images what is the where you put batch of images what is the solution where you put batch of images what is the solution for where you put batch of images what is the solution for in a product where you put batch of images what is the solution for in a production system Where you put batch of images. What is the solution for in a production system?
2024-08-24 15:05:21.339775: can you can you ask me Can you ask me? right Right. we'll pick up we'll pick up a we'll pick up a solution We'll pick up a solution. but but i know in the same but i know in the same call But I know in the same call. some of these model some of these models some of these models was used some of these models was used done on cloud Some of these models was used done on cloud. where you put where you put batch of imag where you put batch of images where you put batch of images what is the where you put batch of images what is the solution where you put batch of images what is the solution for where you put batch of images what is the solution for in a product where you put batch of images what is the solution for in a production system Where you put batch of images. What is the solution for in a production system?
2024-08-24 15:07:18.429469: okay Okay. production production system Production system. like sudden like suddenly handling like suddenly handling image like suddenly handling image process like suddenly handling image processes like like suddenly handling image processes like based like suddenly handling image processes like based on like suddenly handling image processes like based on the like suddenly handling image processes like based on the detection and like suddenly handling image processes like based on the detection and classification like suddenly handling image processes like based on the detection and classification the product like suddenly handling image processes like based on the detection and classification the production like suddenly handling image processes like based on the detection and classification the production around like suddenly handling image processes like based on the detection and classification the production around we have like suddenly handling image processes like based on the detection and classification the production around we have user cloud like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large bat like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of imag like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficient like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficiently like like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficiently like we create like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficiently like we created Like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution, mostly based on the large batch of images, efficiently, like we created. the imag the images The images. actually Actually. so like so like to so like to handle the bad so like to handle the badges of imag so like to handle the badges of images so like to handle the badges of images and production system So like to handle the badges of images and production system. we have built we have built the pipel we have built the pipelines actually we have built the pipelines actually processing we have built the pipelines actually processing the pipelines we have built the pipelines actually processing the pipelines combined we have built the pipelines actually processing the pipelines combined with the real time we have built the pipelines actually processing the pipelines combined with the real time imag we have built the pipelines actually processing the pipelines combined with the real time images we have built the pipelines actually processing the pipelines combined with the real time images direction we have built the pipelines actually processing the pipelines combined with the real time images direction models we have built the pipelines actually processing the pipelines combined with the real time images direction models to deploy we have built the pipelines actually processing the pipelines combined with the real time images direction models to deploy it we have built the pipelines actually processing the pipelines combined with the real time images direction models to deploy it and when We have built the pipelines. Actually processing the pipelines combined with the real time images, direction models to deploy it and when. mainly we mainly we'll get mainly we'll get kind of mainly we'll get kind of we use the mainly we'll get kind of we use the data brick mainly we'll get kind of we use the data bricks so mainly we'll get kind of we use the data bricks so as we get our brick mainly we'll get kind of we use the data bricks so as we get our bricks to manage mainly we'll get kind of we use the data bricks so as we get our bricks to manage that mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data process mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workf mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney select mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clar mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the data mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the data is mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the data is in Mainly we'll get kind of. We use the data bricks, so as we get our bricks. To manage that data processing workflow. This ion was a kidney selection and improving the clarity and we written the notes. So before coming it. Before that, actually, the data is in. hadoop Hadoop. when we have when we have to access when we have to access the data when we have to access the data and when we have to access the data and we have used when we have to access the data and we have used by sp when we have to access the data and we have used by spark when we have to access the data and we have used by spark link when we have to access the data and we have used by spark links and then When we have to access the data and we have used by spark links and then. we directly we directly get we directly get the data we directly get the data from there we directly get the data from there and then we import we directly get the data from there and then we imported we directly get the data from there and then we imported that we directly get the data from there and then we imported that into our we directly get the data from there and then we imported that into our blob st we directly get the data from there and then we imported that into our blob storage we directly get the data from there and then we imported that into our blob storage and everything we directly get the data from there and then we imported that into our blob storage and everything after we directly get the data from there and then we imported that into our blob storage and everything after that We directly get the data from there, and then we imported that into our blob storage. And everything after that. we built we built the pipel we built the pipelines and then we built the pipelines and then it we built the pipelines and then it will automatically we built the pipelines and then it will automatically take the we built the pipelines and then it will automatically take the things like we built the pipelines and then it will automatically take the things like every we built the pipelines and then it will automatically take the things like every one hour we built the pipelines and then it will automatically take the things like every one hour like that we built the pipelines and then it will automatically take the things like every one hour like that it will we built the pipelines and then it will automatically take the things like every one hour like that it will take we built the pipelines and then it will automatically take the things like every one hour like that it will take the we built the pipelines and then it will automatically take the things like every one hour like that it will take the data we built the pipelines and then it will automatically take the things like every one hour like that it will take the data and then we we built the pipelines and then it will automatically take the things like every one hour like that it will take the data and then we refresh it We built the pipelines, and then it will automatically take the things, like, every 1 hour, like. That it will take the data and then we refresh it. on that on that the on that the model select on that the model select after on that the model select after being on that the model select after being trying and on that the model select after being trying and fine tun on that the model select after being trying and fine tuned on that the model select after being trying and fine tuned they are on that the model select after being trying and fine tuned they are deployed on that the model select after being trying and fine tuned they are deployed into real on that the model select after being trying and fine tuned they are deployed into real environment on that the model select after being trying and fine tuned they are deployed into real environmental on that the model select after being trying and fine tuned they are deployed into real environmental cloud on that the model select after being trying and fine tuned they are deployed into real environmental cloud to on that the model select after being trying and fine tuned they are deployed into real environmental cloud to set up on that the model select after being trying and fine tuned they are deployed into real environmental cloud to set up for On that the model select after being trying and fine tuned they are deployed into real environmental cloud to set up for. processing the classification processing the classification imag processing the classification images and processing the classification images and everything like processing the classification images and everything like that processing the classification images and everything like that we have processing the classification images and everything like that we have but most processing the classification images and everything like that we have but mostly we processing the classification images and everything like that we have but mostly we handle processing the classification images and everything like that we have but mostly we handle batch processing the classification images and everything like that we have but mostly we handle batches for bat processing the classification images and everything like that we have but mostly we handle batches for batches Processing the classification images and everything like that we have, but mostly we handle batches for batches. in canada in canada based in canada based on the in canada based on the volume of the in canada based on the volume of the efficiency in canada based on the volume of the efficiency so in canada based on the volume of the efficiency so we mostly in canada based on the volume of the efficiency so we mostly go in canada based on the volume of the efficiency so we mostly go with In Canada based on the volume of the efficiency. So we mostly go with. them them in the beginning them in the beginning days them in the beginning days we have them in the beginning days we have capture them in the beginning days we have capture only kind of them in the beginning days we have capture only kind of like them in the beginning days we have capture only kind of like five hundred them in the beginning days we have capture only kind of like five hundred images them in the beginning days we have capture only kind of like five hundred images thousand images them in the beginning days we have capture only kind of like five hundred images thousand images like that after them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that oper them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have done that upper them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have done that upper witch them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have done that upper witch actually Them. In the beginning days we have capture only kind of like 500 images, thousand images like that. After that we had tried to increase the size. Then we have checked the each and everything that operation is coming down. Or it's okay to do that like that. We have done that. Upper witch, actually. got it so got it so you mentioned got it so you mentioned pipel got it so you mentioned pipelines got it so you mentioned pipelines what are you using got it so you mentioned pipelines what are you using is got it so you mentioned pipelines what are you using is your pipeline got it so you mentioned pipelines what are you using is your pipeline tool got it so you mentioned pipelines what are you using is your pipeline tool actually Got it. So, you mentioned pipelines. What are you using? Is your pipeline tool, actually.
2024-08-24 15:07:18.430476: okay Okay. production production system Production system. like sudden like suddenly handling like suddenly handling image like suddenly handling image process like suddenly handling image processes like like suddenly handling image processes like based like suddenly handling image processes like based on like suddenly handling image processes like based on the like suddenly handling image processes like based on the detection and like suddenly handling image processes like based on the detection and classification like suddenly handling image processes like based on the detection and classification the product like suddenly handling image processes like based on the detection and classification the production like suddenly handling image processes like based on the detection and classification the production around like suddenly handling image processes like based on the detection and classification the production around we have like suddenly handling image processes like based on the detection and classification the production around we have user cloud like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large bat like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of imag like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficient like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficiently like like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficiently like we create like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution mostly based on the large batch of images efficiently like we created Like suddenly handling image processes like based on the detection and classification the production around we have user cloud solution, mostly based on the large batch of images, efficiently, like we created. the imag the images The images. actually Actually. so like so like to so like to handle the bad so like to handle the badges of imag so like to handle the badges of images so like to handle the badges of images and production system So like to handle the badges of images and production system. we have built we have built the pipel we have built the pipelines actually we have built the pipelines actually processing we have built the pipelines actually processing the pipelines we have built the pipelines actually processing the pipelines combined we have built the pipelines actually processing the pipelines combined with the real time we have built the pipelines actually processing the pipelines combined with the real time imag we have built the pipelines actually processing the pipelines combined with the real time images we have built the pipelines actually processing the pipelines combined with the real time images direction we have built the pipelines actually processing the pipelines combined with the real time images direction models we have built the pipelines actually processing the pipelines combined with the real time images direction models to deploy we have built the pipelines actually processing the pipelines combined with the real time images direction models to deploy it we have built the pipelines actually processing the pipelines combined with the real time images direction models to deploy it and when We have built the pipelines. Actually processing the pipelines combined with the real time images, direction models to deploy it and when. mainly we mainly we'll get mainly we'll get kind of mainly we'll get kind of we use the mainly we'll get kind of we use the data brick mainly we'll get kind of we use the data bricks so mainly we'll get kind of we use the data bricks so as we get our brick mainly we'll get kind of we use the data bricks so as we get our bricks to manage mainly we'll get kind of we use the data bricks so as we get our bricks to manage that mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data process mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workf mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney select mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clar mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the data mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the data is mainly we'll get kind of we use the data bricks so as we get our bricks to manage that data processing workflow this ion was a kidney selection and improving the clarity and we written the notes so before coming it before that actually the data is in Mainly we'll get kind of. We use the data bricks, so as we get our bricks. To manage that data processing workflow. This ion was a kidney selection and improving the clarity and we written the notes. So before coming it. Before that, actually, the data is in. hadoop Hadoop. when we have when we have to access when we have to access the data when we have to access the data and when we have to access the data and we have used when we have to access the data and we have used by sp when we have to access the data and we have used by spark when we have to access the data and we have used by spark link when we have to access the data and we have used by spark links and then When we have to access the data and we have used by spark links and then. we directly we directly get we directly get the data we directly get the data from there we directly get the data from there and then we import we directly get the data from there and then we imported we directly get the data from there and then we imported that we directly get the data from there and then we imported that into our we directly get the data from there and then we imported that into our blob st we directly get the data from there and then we imported that into our blob storage we directly get the data from there and then we imported that into our blob storage and everything we directly get the data from there and then we imported that into our blob storage and everything after we directly get the data from there and then we imported that into our blob storage and everything after that We directly get the data from there, and then we imported that into our blob storage. And everything after that. we built we built the pipel we built the pipelines and then we built the pipelines and then it we built the pipelines and then it will automatically we built the pipelines and then it will automatically take the we built the pipelines and then it will automatically take the things like we built the pipelines and then it will automatically take the things like every we built the pipelines and then it will automatically take the things like every one hour we built the pipelines and then it will automatically take the things like every one hour like that we built the pipelines and then it will automatically take the things like every one hour like that it will we built the pipelines and then it will automatically take the things like every one hour like that it will take we built the pipelines and then it will automatically take the things like every one hour like that it will take the we built the pipelines and then it will automatically take the things like every one hour like that it will take the data we built the pipelines and then it will automatically take the things like every one hour like that it will take the data and then we we built the pipelines and then it will automatically take the things like every one hour like that it will take the data and then we refresh it We built the pipelines, and then it will automatically take the things, like, every 1 hour, like. That it will take the data and then we refresh it. on that on that the on that the model select on that the model select after on that the model select after being on that the model select after being trying and on that the model select after being trying and fine tun on that the model select after being trying and fine tuned on that the model select after being trying and fine tuned they are on that the model select after being trying and fine tuned they are deployed on that the model select after being trying and fine tuned they are deployed into real on that the model select after being trying and fine tuned they are deployed into real environment on that the model select after being trying and fine tuned they are deployed into real environmental on that the model select after being trying and fine tuned they are deployed into real environmental cloud on that the model select after being trying and fine tuned they are deployed into real environmental cloud to on that the model select after being trying and fine tuned they are deployed into real environmental cloud to set up on that the model select after being trying and fine tuned they are deployed into real environmental cloud to set up for On that the model select after being trying and fine tuned they are deployed into real environmental cloud to set up for. processing the classification processing the classification imag processing the classification images and processing the classification images and everything like processing the classification images and everything like that processing the classification images and everything like that we have processing the classification images and everything like that we have but most processing the classification images and everything like that we have but mostly we processing the classification images and everything like that we have but mostly we handle processing the classification images and everything like that we have but mostly we handle batch processing the classification images and everything like that we have but mostly we handle batches for bat processing the classification images and everything like that we have but mostly we handle batches for batches Processing the classification images and everything like that we have, but mostly we handle batches for batches. in canada in canada based in canada based on the in canada based on the volume of the in canada based on the volume of the efficiency in canada based on the volume of the efficiency so in canada based on the volume of the efficiency so we mostly in canada based on the volume of the efficiency so we mostly go in canada based on the volume of the efficiency so we mostly go with In Canada based on the volume of the efficiency. So we mostly go with. them them in the beginning them in the beginning days them in the beginning days we have them in the beginning days we have capture them in the beginning days we have capture only kind of them in the beginning days we have capture only kind of like them in the beginning days we have capture only kind of like five hundred them in the beginning days we have capture only kind of like five hundred images them in the beginning days we have capture only kind of like five hundred images thousand images them in the beginning days we have capture only kind of like five hundred images thousand images like that after them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that oper them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have done that upper them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have done that upper witch them in the beginning days we have capture only kind of like five hundred images thousand images like that after that we had tried to increase the size then we have checked the each and everything that operation is coming down or it's okay to do that like that we have done that upper witch actually Them. In the beginning days we have capture only kind of like 500 images, thousand images like that. After that we had tried to increase the size. Then we have checked the each and everything that operation is coming down. Or it's okay to do that like that. We have done that. Upper witch, actually. got it so got it so you mentioned got it so you mentioned pipel got it so you mentioned pipelines got it so you mentioned pipelines what are you using got it so you mentioned pipelines what are you using is got it so you mentioned pipelines what are you using is your pipeline got it so you mentioned pipelines what are you using is your pipeline tool got it so you mentioned pipelines what are you using is your pipeline tool actually Got it. So, you mentioned pipelines. What are you using? Is your pipeline tool, actually.
2024-08-24 15:08:23.789173: we have we have used like we have used like a basic we have used like a basic grammar we have used like a basic grammar mostly we have used like a basic grammar mostly like We have used, like, a basic grammar, mostly like, mostly mostly meaning with mostly meaning with the mostly meaning with the workflows mostly meaning with the workflows actually mostly meaning with the workflows actually for handling mostly meaning with the workflows actually for handling the large mostly meaning with the workflows actually for handling the large scale mostly meaning with the workflows actually for handling the large scale images process mostly meaning with the workflows actually for handling the large scale images processing and mostly meaning with the workflows actually for handling the large scale images processing and everything Mostly meaning with the workflows actually for handling the large scale images, processing and everything. like like while going like while going with the like while going with the data like while going with the data process like while going with the data process the workflow Like while going with the data process, the workflow. server server steps like first server steps like first we have server steps like first we have goal with the server steps like first we have goal with the data inject server steps like first we have goal with the data injection server steps like first we have goal with the data injection like we have server steps like first we have goal with the data injection like we have to database server steps like first we have goal with the data injection like we have to database to server steps like first we have goal with the data injection like we have to database to build the data from server steps like first we have goal with the data injection like we have to database to build the data from hadoo server steps like first we have goal with the data injection like we have to database to build the data from hadoop server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using price server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like enabling server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like enabling syncware server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like enabling syncware integration Server steps like first we have goal with the data injection like we have to database to build. The data from Hadoop and then prospect using Pricepark like enabling syncware integration. with With. data implement data implementation data implementation those kind of things data implementation those kind of things after that data implementation those kind of things after that we have done data implementation those kind of things after that we have done with data implementation those kind of things after that we have done with the major data implementation those kind of things after that we have done with the major enhancement data implementation those kind of things after that we have done with the major enhancement process data implementation those kind of things after that we have done with the major enhancement process and then data implementation those kind of things after that we have done with the major enhancement process and then we go data implementation those kind of things after that we have done with the major enhancement process and then we go along with data implementation those kind of things after that we have done with the major enhancement process and then we go along with the model Data implementation, those kind of things. After that, we have done with the major enhancement process, and then we go along with the model? so so you so you mentioned so you mentioned you're using so you mentioned you're using pipe so you mentioned you're using pipe let's say so you mentioned you're using pipe let's say i have so you mentioned you're using pipe let's say i have a python so you mentioned you're using pipe let's say i have a python function So you mentioned you're using pipe. Let's say I have a python function. that That. 's 's. right right on a pixel right on a pixel or something right on a pixel or something like right on a pixel or something like that Right on a pixel or something like that. now now this function now this function is writt now this function is written in now this function is written in python now this function is written in python i don't want now this function is written in python i don't want it to move now this function is written in python i don't want it to move to python now this function is written in python i don't want it to move to python but now this function is written in python i don't want it to move to python but how can i now this function is written in python i don't want it to move to python but how can i get now this function is written in python i don't want it to move to python but how can i get some now this function is written in python i don't want it to move to python but how can i get some distributed now this function is written in python i don't want it to move to python but how can i get some distributed performance now this function is written in python i don't want it to move to python but how can i get some distributed performance from now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data points to now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data points to a pipeline now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data points to a pipeline function
2024-08-24 15:08:23.789173: we have we have used like we have used like a basic we have used like a basic grammar we have used like a basic grammar mostly we have used like a basic grammar mostly like We have used, like, a basic grammar, mostly like, mostly mostly meaning with mostly meaning with the mostly meaning with the workflows mostly meaning with the workflows actually mostly meaning with the workflows actually for handling mostly meaning with the workflows actually for handling the large mostly meaning with the workflows actually for handling the large scale mostly meaning with the workflows actually for handling the large scale images process mostly meaning with the workflows actually for handling the large scale images processing and mostly meaning with the workflows actually for handling the large scale images processing and everything Mostly meaning with the workflows actually for handling the large scale images, processing and everything. like like while going like while going with the like while going with the data like while going with the data process like while going with the data process the workflow Like while going with the data process, the workflow. server server steps like first server steps like first we have server steps like first we have goal with the server steps like first we have goal with the data inject server steps like first we have goal with the data injection server steps like first we have goal with the data injection like we have server steps like first we have goal with the data injection like we have to database server steps like first we have goal with the data injection like we have to database to server steps like first we have goal with the data injection like we have to database to build the data from server steps like first we have goal with the data injection like we have to database to build the data from hadoo server steps like first we have goal with the data injection like we have to database to build the data from hadoop server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using price server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like enabling server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like enabling syncware server steps like first we have goal with the data injection like we have to database to build the data from hadoop and then prospect using pricepark like enabling syncware integration Server steps like first we have goal with the data injection like we have to database to build. The data from Hadoop and then prospect using Pricepark like enabling syncware integration. with With. data implement data implementation data implementation those kind of things data implementation those kind of things after that data implementation those kind of things after that we have done data implementation those kind of things after that we have done with data implementation those kind of things after that we have done with the major data implementation those kind of things after that we have done with the major enhancement data implementation those kind of things after that we have done with the major enhancement process data implementation those kind of things after that we have done with the major enhancement process and then data implementation those kind of things after that we have done with the major enhancement process and then we go data implementation those kind of things after that we have done with the major enhancement process and then we go along with data implementation those kind of things after that we have done with the major enhancement process and then we go along with the model Data implementation, those kind of things. After that, we have done with the major enhancement process, and then we go along with the model? so so you so you mentioned so you mentioned you're using so you mentioned you're using pipe so you mentioned you're using pipe let's say so you mentioned you're using pipe let's say i have so you mentioned you're using pipe let's say i have a python so you mentioned you're using pipe let's say i have a python function So you mentioned you're using pipe. Let's say I have a python function. that That. 's 's. right right on a pixel right on a pixel or something right on a pixel or something like right on a pixel or something like that Right on a pixel or something like that. now now this function now this function is writt now this function is written in now this function is written in python now this function is written in python i don't want now this function is written in python i don't want it to move now this function is written in python i don't want it to move to python now this function is written in python i don't want it to move to python but now this function is written in python i don't want it to move to python but how can i now this function is written in python i don't want it to move to python but how can i get now this function is written in python i don't want it to move to python but how can i get some now this function is written in python i don't want it to move to python but how can i get some distributed now this function is written in python i don't want it to move to python but how can i get some distributed performance now this function is written in python i don't want it to move to python but how can i get some distributed performance from now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data points to now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data points to a pipeline now this function is written in python i don't want it to move to python but how can i get some distributed performance from a python function basically how many pass data points to a pipeline function
2024-08-24 15:09:19.952437: Now, this function is written in Python. I don't want it to move to Python, but. How can I get some distributed performance from a Python function? Basically, how many pass data points to a pipeline function. without without using without using pass without using passpath without using passpath right without using passpath right like without using passpath right like we can without using passpath right like we can use without using passpath right like we can use like without using passpath right like we can use like import Without using passpath, right? Like we can use like, import. and and by the park and by the park session and by the park session right and by the park session right like and by the park session right like that and by the park session right like that we don't want and by the park session right like that we don't want we have and by the park session right like that we don't want we have done and by the park session right like that we don't want we have done this like and by the park session right like that we don't want we have done this like by sp and by the park session right like that we don't want we have done this like by spark like and by the park session right like that we don't want we have done this like by spark like sq and by the park session right like that we don't want we have done this like by spark like sql import and by the park session right like that we don't want we have done this like by spark like sql importer and then and by the park session right like that we don't want we have done this like by spark like sql importer and then spark and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sq and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql function and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing will happen and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing will happen after and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing will happen after that And by the park session, right like that. We don't want. We have done this like. By spark, like SQL importer and then spark session, and then SQL functions. That's the thing will happen after that. we have to we have to implement we have to implement the we have to implement the skill we have to implement the skill types we have to implement the skill types also we have to implement the skill types also import we have to implement the skill types also import the int we have to implement the skill types also import the integer type we have to implement the skill types also import the integer types or we have to implement the skill types also import the integer types or some these kind of type we have to implement the skill types also import the integer types or some these kind of types we we have to implement the skill types also import the integer types or some these kind of types we need to we have to implement the skill types also import the integer types or some these kind of types we need to ensure after we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initialize we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after that we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after that we have to we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after that we have to define We have to implement the skill types. Also import the integer types or some these kind of types. We need to ensure after that go with the initializer spark session like how it is going. With it. After that, we have to define. help help deploy help deploy the mod help deploy the modules help deploy the modules also Help deploy the modules. Also. sorry sorry no sorry no have sorry no have you done sorry no have you done some sort of sorry no have you done some sort of model dep sorry no have you done some sort of model deployment sorry no have you done some sort of model deployment so let's say sorry no have you done some sort of model deployment so let's say you have your sorry no have you done some sort of model deployment so let's say you have your pipel sorry no have you done some sort of model deployment so let's say you have your pipelines and all sorry no have you done some sort of model deployment so let's say you have your pipelines and all type Sorry. No. Have you done some sort of model deployment? So let's say you have your. Pipelines and all type. typeline and typeline and everything typeline and everything but how typeline and everything but how do you deploy typeline and everything but how do you deploy to typeline and everything but how do you deploy to a production typeline and everything but how do you deploy to a production or typeline and everything but how do you deploy to a production or are you building typeline and everything but how do you deploy to a production or are you building it in the product typeline and everything but how do you deploy to a production or are you building it in the production typeline and everything but how do you deploy to a production or are you building it in the production environment typeline and everything but how do you deploy to a production or are you building it in the production environment itself
2024-08-24 15:09:19.952437: Now, this function is written in Python. I don't want it to move to Python, but. How can I get some distributed performance from a Python function? Basically, how many pass data points to a pipeline function. without without using without using pass without using passpath without using passpath right without using passpath right like without using passpath right like we can without using passpath right like we can use without using passpath right like we can use like without using passpath right like we can use like import Without using passpath, right? Like we can use like, import. and and by the park and by the park session and by the park session right and by the park session right like and by the park session right like that and by the park session right like that we don't want and by the park session right like that we don't want we have and by the park session right like that we don't want we have done and by the park session right like that we don't want we have done this like and by the park session right like that we don't want we have done this like by sp and by the park session right like that we don't want we have done this like by spark like and by the park session right like that we don't want we have done this like by spark like sq and by the park session right like that we don't want we have done this like by spark like sql import and by the park session right like that we don't want we have done this like by spark like sql importer and then and by the park session right like that we don't want we have done this like by spark like sql importer and then spark and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sq and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql function and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing will happen and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing will happen after and by the park session right like that we don't want we have done this like by spark like sql importer and then spark session and then sql functions that's the thing will happen after that And by the park session, right like that. We don't want. We have done this like. By spark, like SQL importer and then spark session, and then SQL functions. That's the thing will happen after that. we have to we have to implement we have to implement the we have to implement the skill we have to implement the skill types we have to implement the skill types also we have to implement the skill types also import we have to implement the skill types also import the int we have to implement the skill types also import the integer type we have to implement the skill types also import the integer types or we have to implement the skill types also import the integer types or some these kind of type we have to implement the skill types also import the integer types or some these kind of types we we have to implement the skill types also import the integer types or some these kind of types we need to we have to implement the skill types also import the integer types or some these kind of types we need to ensure after we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initialize we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after that we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after that we have to we have to implement the skill types also import the integer types or some these kind of types we need to ensure after that go with the initializer spark session like how it is going with it after that we have to define We have to implement the skill types. Also import the integer types or some these kind of types. We need to ensure after that go with the initializer spark session like how it is going. With it. After that, we have to define. help help deploy help deploy the mod help deploy the modules help deploy the modules also Help deploy the modules. Also. sorry sorry no sorry no have sorry no have you done sorry no have you done some sort of sorry no have you done some sort of model dep sorry no have you done some sort of model deployment sorry no have you done some sort of model deployment so let's say sorry no have you done some sort of model deployment so let's say you have your sorry no have you done some sort of model deployment so let's say you have your pipel sorry no have you done some sort of model deployment so let's say you have your pipelines and all sorry no have you done some sort of model deployment so let's say you have your pipelines and all type Sorry. No. Have you done some sort of model deployment? So let's say you have your. Pipelines and all type. typeline and typeline and everything typeline and everything but how typeline and everything but how do you deploy typeline and everything but how do you deploy to typeline and everything but how do you deploy to a production typeline and everything but how do you deploy to a production or typeline and everything but how do you deploy to a production or are you building typeline and everything but how do you deploy to a production or are you building it in the product typeline and everything but how do you deploy to a production or are you building it in the production typeline and everything but how do you deploy to a production or are you building it in the production environment typeline and everything but how do you deploy to a production or are you building it in the production environment itself
2024-08-24 15:10:15.532039: Typeline and everything. But how do you deploy to a production? Or are you building it in? The production environment itself. no No. first First. the model the model first the model first we the model first we deployed the model first we deployed in the model first we deployed in cs the model first we deployed in cscd the model first we deployed in cscd by transact the model first we deployed in cscd by transaction The model first. We deployed in CSCD by transaction. so like so like in the michel so like in the michelin so like in the michelin while going to the so like in the michelin while going to the deployment so like in the michelin while going to the deployment models so like in the michelin while going to the deployment models first we so like in the michelin while going to the deployment models first we deployed and so like in the michelin while going to the deployment models first we deployed and tested so like in the michelin while going to the deployment models first we deployed and tested locally so like in the michelin while going to the deployment models first we deployed and tested locally and we so like in the michelin while going to the deployment models first we deployed and tested locally and we can control so like in the michelin while going to the deployment models first we deployed and tested locally and we can control the environ so like in the michelin while going to the deployment models first we deployed and tested locally and we can control the environment So like in the Michelin, while going to the deployment models first, we deployed and tested locally. And we can control the environment. of that of that data of that database of that database after that of that database after that we Of that database. After that, we. gone with Gone with. docker docker and then orchest docker and then orchestration docker and then orchestration with docker and then orchestration with kubernetes docker and then orchestration with kubernetes and then after docker and then orchestration with kubernetes and then after that docker and then orchestration with kubernetes and then after that we docker and then orchestration with kubernetes and then after that we build the docker and then orchestration with kubernetes and then after that we build the tc docker and then orchestration with kubernetes and then after that we build the tcd pip docker and then orchestration with kubernetes and then after that we build the tcd pipelines docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud dep docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud deployment those docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud deployment those kind of things docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud deployment those kind of things like that Docker and then orchestration with kubernetes. And then after that we build the TCD pipelines and then that cloud deployment, those kind of things like that. yeah can yeah can you explain yeah can you explain the cs yeah can you explain the cs eighty pipel yeah can you explain the cs eighty pipeline yeah can you explain the cs eighty pipeline that was built Yeah. Can you explain the CS 80 pipeline that was built? and you and you are asking only and you are asking only particular and you are asking only particular about and you are asking only particular about that And you are asking only particular about that. in that project in that project how in that project how we done in that project how we done cc in that project how we done cc yeah In that project. How we done, CC? Yeah. that project That project. used in the used in the pipeline used in the pipeline what were the used in the pipeline what were the steps in used in the pipeline what were the steps in the pipel used in the pipeline what were the steps in the pipeline used in the pipeline what were the steps in the pipeline the cip used in the pipeline what were the steps in the pipeline the cipher used in the pipeline what were the steps in the pipeline the cipher can you explain used in the pipeline what were the steps in the pipeline the cipher can you explain that
2024-08-24 15:10:15.533590: Typeline and everything. But how do you deploy to a production? Or are you building it in? The production environment itself. no No. first First. the model the model first the model first we the model first we deployed the model first we deployed in the model first we deployed in cs the model first we deployed in cscd the model first we deployed in cscd by transact the model first we deployed in cscd by transaction The model first. We deployed in CSCD by transaction. so like so like in the michel so like in the michelin so like in the michelin while going to the so like in the michelin while going to the deployment so like in the michelin while going to the deployment models so like in the michelin while going to the deployment models first we so like in the michelin while going to the deployment models first we deployed and so like in the michelin while going to the deployment models first we deployed and tested so like in the michelin while going to the deployment models first we deployed and tested locally so like in the michelin while going to the deployment models first we deployed and tested locally and we so like in the michelin while going to the deployment models first we deployed and tested locally and we can control so like in the michelin while going to the deployment models first we deployed and tested locally and we can control the environ so like in the michelin while going to the deployment models first we deployed and tested locally and we can control the environment So like in the Michelin, while going to the deployment models first, we deployed and tested locally. And we can control the environment. of that of that data of that database of that database after that of that database after that we Of that database. After that, we. gone with Gone with. docker docker and then orchest docker and then orchestration docker and then orchestration with docker and then orchestration with kubernetes docker and then orchestration with kubernetes and then after docker and then orchestration with kubernetes and then after that docker and then orchestration with kubernetes and then after that we docker and then orchestration with kubernetes and then after that we build the docker and then orchestration with kubernetes and then after that we build the tc docker and then orchestration with kubernetes and then after that we build the tcd pip docker and then orchestration with kubernetes and then after that we build the tcd pipelines docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud dep docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud deployment those docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud deployment those kind of things docker and then orchestration with kubernetes and then after that we build the tcd pipelines and then that cloud deployment those kind of things like that Docker and then orchestration with kubernetes. And then after that we build the TCD pipelines and then that cloud deployment, those kind of things like that. yeah can yeah can you explain yeah can you explain the cs yeah can you explain the cs eighty pipel yeah can you explain the cs eighty pipeline yeah can you explain the cs eighty pipeline that was built Yeah. Can you explain the CS 80 pipeline that was built? and you and you are asking only and you are asking only particular and you are asking only particular about and you are asking only particular about that And you are asking only particular about that. in that project in that project how in that project how we done in that project how we done cc in that project how we done cc yeah In that project. How we done, CC? Yeah. that project That project. used in the used in the pipeline used in the pipeline what were the used in the pipeline what were the steps in used in the pipeline what were the steps in the pipel used in the pipeline what were the steps in the pipeline used in the pipeline what were the steps in the pipeline the cip used in the pipeline what were the steps in the pipeline the cipher used in the pipeline what were the steps in the pipeline the cipher can you explain used in the pipeline what were the steps in the pipeline the cipher can you explain that
2024-08-24 15:12:15.280117: Used in the pipeline. What were the steps in the pipeline? The cipher. Can you explain that? okay Okay. so like so like in the so like in the first we so like in the first we go with the so like in the first we go with the source code integr so like in the first we go with the source code integration like so like in the first we go with the source code integration like this so like in the first we go with the source code integration like this channel we so like in the first we go with the source code integration like this channel we go to so like in the first we go with the source code integration like this channel we go to the git and then after so like in the first we go with the source code integration like this channel we go to the git and then after that so like in the first we go with the source code integration like this channel we go to the git and then after that automatic so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and function so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development deploy so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development deploy the application so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development deploy the application staging So, like, in the first, we go with the source code integration, like this channel we go to. The git. And then after that, automatic testing. We execute test the pass to verify the code. Integrity and functionality, then built on the packages like compile and package the application for development. After that development, deploy the application staging. particular particular environment particular environment like dev particular environment like dev environment particular environment like dev environment or particular environment like dev environment or stag particular environment like dev environment or staging particular environment like dev environment or staging after particular environment like dev environment or staging after that particular environment like dev environment or staging after that environment particular environment like dev environment or staging after that environment after particular environment like dev environment or staging after that environment after that particular environment like dev environment or staging after that environment after that production particular environment like dev environment or staging after that environment after that production after particular environment like dev environment or staging after that environment after that production after using particular environment like dev environment or staging after that environment after that production after using a dock particular environment like dev environment or staging after that environment after that production after using a docker and particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the mon particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring and particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring and those kind of particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring and those kind of things Particular environment like dev environment or staging after that environment, after that production after using a docker. And kubernetes. Like after that we go with the monitoring and those kind of things. i like the source i like the source code i like the source code like we i like the source code like we start with i like the source code like we start with integrated i like the source code like we start with integrated with source code i like the source code like we start with integrated with source code like i like the source code like we start with integrated with source code like question i like the source code like we start with integrated with source code like question control system i like the source code like we start with integrated with source code like question control system that is i like the source code like we start with integrated with source code like question control system that is like i like the source code like we start with integrated with source code like question control system that is like click and change i like the source code like we start with integrated with source code like question control system that is like click and change the automatically i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pull i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and check i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conf i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confir i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the ut i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the utility data i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the utility data is like most i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the utility data is like mostly I like the source code. Like we start with, integrated with source code, like question control system that. Is like click and change. The automatically pulled and checked for an image conflict is there like confirms the code integrity and everything. After that, go the test. Test part, like using the utility data is like, mostly. code mostly code mostly code with code mostly code with py code mostly code with python code mostly code with python and code mostly code with python and the code mostly code with python and the model Code, mostly code with Python and the model. meets meets this meets this kind of meets this kind of specific accur meets this kind of specific accuracy meets this kind of specific accuracy and then meets this kind of specific accuracy and then we have to meets this kind of specific accuracy and then we have to go with the compile meets this kind of specific accuracy and then we have to go with the compile checks meets this kind of specific accuracy and then we have to go with the compile checks with the meets this kind of specific accuracy and then we have to go with the compile checks with the benchmark meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward like that meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward like that after meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward like that after that Meets this kind of specific accuracy, and then we have to go with the compile checks with the benchmarks, like moving forward like that, after that. build and build and build the packages Build and build the packages. like like we build like we build the application like we build the application which like we build the application which are for like we build the application which are for a machine learning like we build the application which are for a machine learning model like we build the application which are for a machine learning model and like we build the application which are for a machine learning model and we like creating like we build the application which are for a machine learning model and we like creating docker imag like we build the application which are for a machine learning model and we like creating docker images and like we build the application which are for a machine learning model and we like creating docker images and then like we build the application which are for a machine learning model and we like creating docker images and then helps like we build the application which are for a machine learning model and we like creating docker images and then helps to Like, we build the application, which are for a machine learning model, and we like creating Docker. Images and then helps to. all all dependencies all dependencies like kind of all dependencies like kind of from making the application All dependencies, like, kind of from making the application. like like portability like portability kind of kind like portability kind of kind of things like like portability kind of kind of things like a different like portability kind of kind of things like a different environ like portability kind of kind of things like a different environment based like portability kind of kind of things like a different environment based on that like portability kind of kind of things like a different environment based on that so like portability kind of kind of things like a different environment based on that so after that like portability kind of kind of things like a different environment based on that so after that we go like portability kind of kind of things like a different environment based on that so after that we go the deployment Like portability kind of. Kind of things like a different environment based on that. So after that. We go the deployment. mostly mostly with the mostly with the kubernetes mostly with the kubernetes we mostly with the kubernetes we used mostly with the kubernetes we used right mostly with the kubernetes we used right so mostly with the kubernetes we used right so i'm not mostly with the kubernetes we used right so i'm not working for mostly with the kubernetes we used right so i'm not working for expertise mostly with the kubernetes we used right so i'm not working for expertise in kube mostly with the kubernetes we used right so i'm not working for expertise in kubernetes mostly with the kubernetes we used right so i'm not working for expertise in kubernetes so mostly with the kubernetes we used right so i'm not working for expertise in kubernetes so i can't mostly with the kubernetes we used right so i'm not working for expertise in kubernetes so i can't explain Mostly with the Kubernetes we used. Right. So I'm not working for expertise. In kubernetes, so I can't explain. again so again so you just again so you just haven't mentioned Again. So you just haven't mentioned. what what were some of the what were some of the requ what were some of the requirements what were some of the requirements fil what were some of the requirements files what were some of the requirements files in building what were some of the requirements files in building a phd what were some of the requirements files in building a phd file what were some of the requirements files in building a phd file this could be like what were some of the requirements files in building a phd file this could be like the what were some of the requirements files in building a phd file this could be like the generic step what were some of the requirements files in building a phd file this could be like the generic steps to build what were some of the requirements files in building a phd file this could be like the generic steps to build a csv what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipel what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me about what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me about what was what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me about what was in What were some of the requirements files? In building a PhD file, this could be like the generic steps to build a CSV pipeline. Can you tell me about what was in. the the configur the configurations that the configurations that you have the configurations that you have heard The configurations that you have heard. sometimes sometimes any interaction sometimes any interaction tax level sometimes any interaction tax level you can explain sometimes any interaction tax level you can explain what sometimes any interaction tax level you can explain what you have sometimes any interaction tax level you can explain what you have actually built sometimes any interaction tax level you can explain what you have actually built in
2024-08-24 15:12:15.280117: Used in the pipeline. What were the steps in the pipeline? The cipher. Can you explain that? okay Okay. so like so like in the so like in the first we so like in the first we go with the so like in the first we go with the source code integr so like in the first we go with the source code integration like so like in the first we go with the source code integration like this so like in the first we go with the source code integration like this channel we so like in the first we go with the source code integration like this channel we go to so like in the first we go with the source code integration like this channel we go to the git and then after so like in the first we go with the source code integration like this channel we go to the git and then after that so like in the first we go with the source code integration like this channel we go to the git and then after that automatic so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and function so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development deploy so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development deploy the application so like in the first we go with the source code integration like this channel we go to the git and then after that automatic testing we execute test the pass to verify the code integrity and functionality then built on the packages like compile and package the application for development after that development deploy the application staging So, like, in the first, we go with the source code integration, like this channel we go to. The git. And then after that, automatic testing. We execute test the pass to verify the code. Integrity and functionality, then built on the packages like compile and package the application for development. After that development, deploy the application staging. particular particular environment particular environment like dev particular environment like dev environment particular environment like dev environment or particular environment like dev environment or stag particular environment like dev environment or staging particular environment like dev environment or staging after particular environment like dev environment or staging after that particular environment like dev environment or staging after that environment particular environment like dev environment or staging after that environment after particular environment like dev environment or staging after that environment after that particular environment like dev environment or staging after that environment after that production particular environment like dev environment or staging after that environment after that production after particular environment like dev environment or staging after that environment after that production after using particular environment like dev environment or staging after that environment after that production after using a dock particular environment like dev environment or staging after that environment after that production after using a docker and particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the mon particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring and particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring and those kind of particular environment like dev environment or staging after that environment after that production after using a docker and kubernetes like after that we go with the monitoring and those kind of things Particular environment like dev environment or staging after that environment, after that production after using a docker. And kubernetes. Like after that we go with the monitoring and those kind of things. i like the source i like the source code i like the source code like we i like the source code like we start with i like the source code like we start with integrated i like the source code like we start with integrated with source code i like the source code like we start with integrated with source code like i like the source code like we start with integrated with source code like question i like the source code like we start with integrated with source code like question control system i like the source code like we start with integrated with source code like question control system that is i like the source code like we start with integrated with source code like question control system that is like i like the source code like we start with integrated with source code like question control system that is like click and change i like the source code like we start with integrated with source code like question control system that is like click and change the automatically i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pull i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and check i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conf i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confir i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the ut i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the utility data i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the utility data is like most i like the source code like we start with integrated with source code like question control system that is like click and change the automatically pulled and checked for an image conflict is there like confirms the code integrity and everything after that go the test test part like using the utility data is like mostly I like the source code. Like we start with, integrated with source code, like question control system that. Is like click and change. The automatically pulled and checked for an image conflict is there like confirms the code integrity and everything. After that, go the test. Test part, like using the utility data is like, mostly. code mostly code mostly code with code mostly code with py code mostly code with python code mostly code with python and code mostly code with python and the code mostly code with python and the model Code, mostly code with Python and the model. meets meets this meets this kind of meets this kind of specific accur meets this kind of specific accuracy meets this kind of specific accuracy and then meets this kind of specific accuracy and then we have to meets this kind of specific accuracy and then we have to go with the compile meets this kind of specific accuracy and then we have to go with the compile checks meets this kind of specific accuracy and then we have to go with the compile checks with the meets this kind of specific accuracy and then we have to go with the compile checks with the benchmark meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward like that meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward like that after meets this kind of specific accuracy and then we have to go with the compile checks with the benchmarks like moving forward like that after that Meets this kind of specific accuracy, and then we have to go with the compile checks with the benchmarks, like moving forward like that, after that. build and build and build the packages Build and build the packages. like like we build like we build the application like we build the application which like we build the application which are for like we build the application which are for a machine learning like we build the application which are for a machine learning model like we build the application which are for a machine learning model and like we build the application which are for a machine learning model and we like creating like we build the application which are for a machine learning model and we like creating docker imag like we build the application which are for a machine learning model and we like creating docker images and like we build the application which are for a machine learning model and we like creating docker images and then like we build the application which are for a machine learning model and we like creating docker images and then helps like we build the application which are for a machine learning model and we like creating docker images and then helps to Like, we build the application, which are for a machine learning model, and we like creating Docker. Images and then helps to. all all dependencies all dependencies like kind of all dependencies like kind of from making the application All dependencies, like, kind of from making the application. like like portability like portability kind of kind like portability kind of kind of things like like portability kind of kind of things like a different like portability kind of kind of things like a different environ like portability kind of kind of things like a different environment based like portability kind of kind of things like a different environment based on that like portability kind of kind of things like a different environment based on that so like portability kind of kind of things like a different environment based on that so after that like portability kind of kind of things like a different environment based on that so after that we go like portability kind of kind of things like a different environment based on that so after that we go the deployment Like portability kind of. Kind of things like a different environment based on that. So after that. We go the deployment. mostly mostly with the mostly with the kubernetes mostly with the kubernetes we mostly with the kubernetes we used mostly with the kubernetes we used right mostly with the kubernetes we used right so mostly with the kubernetes we used right so i'm not mostly with the kubernetes we used right so i'm not working for mostly with the kubernetes we used right so i'm not working for expertise mostly with the kubernetes we used right so i'm not working for expertise in kube mostly with the kubernetes we used right so i'm not working for expertise in kubernetes mostly with the kubernetes we used right so i'm not working for expertise in kubernetes so mostly with the kubernetes we used right so i'm not working for expertise in kubernetes so i can't mostly with the kubernetes we used right so i'm not working for expertise in kubernetes so i can't explain Mostly with the Kubernetes we used. Right. So I'm not working for expertise. In kubernetes, so I can't explain. again so again so you just again so you just haven't mentioned Again. So you just haven't mentioned. what what were some of the what were some of the requ what were some of the requirements what were some of the requirements fil what were some of the requirements files what were some of the requirements files in building what were some of the requirements files in building a phd what were some of the requirements files in building a phd file what were some of the requirements files in building a phd file this could be like what were some of the requirements files in building a phd file this could be like the what were some of the requirements files in building a phd file this could be like the generic step what were some of the requirements files in building a phd file this could be like the generic steps to build what were some of the requirements files in building a phd file this could be like the generic steps to build a csv what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipel what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me about what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me about what was what were some of the requirements files in building a phd file this could be like the generic steps to build a csv pipeline can you tell me about what was in What were some of the requirements files? In building a PhD file, this could be like the generic steps to build a CSV pipeline. Can you tell me about what was in. the the configur the configurations that the configurations that you have the configurations that you have heard The configurations that you have heard. sometimes sometimes any interaction sometimes any interaction tax level sometimes any interaction tax level you can explain sometimes any interaction tax level you can explain what sometimes any interaction tax level you can explain what you have sometimes any interaction tax level you can explain what you have actually built sometimes any interaction tax level you can explain what you have actually built in
2024-08-24 15:14:08.104394: okay Okay. solution solution in cloud solution in cloud other solution in cloud other than solution in cloud other than this Solution in cloud other than this. data view Data view. the the development the development deployment the development deployment autom the development deployment automation the development deployment automation mon the development deployment automation monitor
2024-08-24 15:14:08.104394: okay Okay. solution solution in cloud solution in cloud other solution in cloud other than solution in cloud other than this Solution in cloud other than this. data view Data view. the the development the development deployment the development deployment autom the development deployment automation the development deployment automation mon the development deployment automation monitor
2024-08-24 15:15:16.519528: The development deployment automation monitor. yes yes actually Yes, actually. the develop the development part The development part. like like mostly like mostly i like mostly i worked with the like mostly i worked with the aws like mostly i worked with the aws also like mostly i worked with the aws also like like mostly i worked with the aws also like with like mostly i worked with the aws also like with elastic Like. Mostly I worked with the aws. Also like with elastic. beans beans track like beans track like that Beans track like that. mostly mostly in mostly in amazon mostly in amazon i worked with mostly in amazon i worked with in mostly in amazon i worked with in the previous mostly in amazon i worked with in the previous things mostly in amazon i worked with in the previous things like Mostly in Amazon I worked with in the previous things like. my starting my starting career my starting career i worked as my starting career i worked as a my starting career i worked as a cloud admin my starting career i worked as a cloud admin so i know my starting career i worked as a cloud admin so i know there are things my starting career i worked as a cloud admin so i know there are things actually how my starting career i worked as a cloud admin so i know there are things actually how it works My starting career, I worked as a cloud admin, so I know there are things actually how it works. like that like that only i know like that only i know but Like that only I know, but. in this In this. production production environ production environment part production environment part and everything Production environment part and everything. still staging still staging we are taking still staging we are taking care of it still staging we are taking care of it and then still staging we are taking care of it and then ml ops still staging we are taking care of it and then ml ops guys still staging we are taking care of it and then ml ops guys are the production still staging we are taking care of it and then ml ops guys are the production guy and of still staging we are taking care of it and then ml ops guys are the production guy and of course still staging we are taking care of it and then ml ops guys are the production guy and of course we are going still staging we are taking care of it and then ml ops guys are the production guy and of course we are going to still staging we are taking care of it and then ml ops guys are the production guy and of course we are going to use Still staging. We are taking care of it, and then ML ops guys are the production guy and. Of course we are going to use. the part of it the part of it but The part of it, but. we will keep on monitoring we will keep on monitoring this we will keep on monitoring this jenkins we will keep on monitoring this jenkins and then We will keep on monitoring this, Jenkins, and then. the testing part the testing part and everything The testing part and everything. we have we have done we have done so we have done so this model we have done so this model evolution we have done so this model evolution those we have done so this model evolution those kind of we have done so this model evolution those kind of even includ we have done so this model evolution those kind of even including we have done so this model evolution those kind of even including with the promises we have done so this model evolution those kind of even including with the promises grafan we have done so this model evolution those kind of even including with the promises grafana We have done. So this model evolution, those kind of even including with the promises grafana. to collect to collect the things to collect the things and give the to collect the things and give the alerts to collect the things and give the alerts those kind of things to collect the things and give the alerts those kind of things also To collect the things and give the alerts, those kind of things also. we have to we have to check we have to check the we have to check the higher health we have to check the higher health status we have to check the higher health status and everything we have to check the higher health status and everything we we have to check the higher health status and everything we only we have to check the higher health status and everything we only dig it like that We have to check the higher health status and everything. We only dig it like that. some of the some of the metrics some of the metrics that some of the metrics that you some of the metrics that you took in some of the metrics that you took in case some of the metrics that you took in case and some of the metrics that you took in case and took it out some of the metrics that you took in case and took it out and it's some of the metrics that you took in case and took it out and it's what some of the metrics that you took in case and took it out and it's what were your notes some of the metrics that you took in case and took it out and it's what were your notes that you gave some of the metrics that you took in case and took it out and it's what were your notes that you gave out
2024-08-24 15:16:24.887467: Some of the metrics that you took in case and took it out, and it's what were your notes that you gave out? in the graf in the grafan in the grafana In the Grafana. system system like application system like application docum system like application documents System, like application documents. like like kind of any like kind of any late response like kind of any late response it is coming like kind of any late response it is coming it like kind of any late response it is coming it is stuck like kind of any late response it is coming it is stuck any like kind of any late response it is coming it is stuck anywhere like kind of any late response it is coming it is stuck anywhere like error like kind of any late response it is coming it is stuck anywhere like error rate like kind of any late response it is coming it is stuck anywhere like error rates like kind of any late response it is coming it is stuck anywhere like error rates resource like kind of any late response it is coming it is stuck anywhere like error rates resource replace like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disrupt like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this also can like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this also can check like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this also can check actually Like kind of any late response. It is coming. It is stuck anywhere, like error rates, resource replacement. Like monitoring CPU, those kind of things. And deployment disruptors are not like this. Also can check actually. mostly system mostly system health mostly system health and then application mostly system health and then application performance mostly system health and then application performance those mostly system health and then application performance those things mostly system health and then application performance those things we mostly mostly system health and then application performance those things we mostly have mostly system health and then application performance those things we mostly have checked mostly system health and then application performance those things we mostly have checked that mostly system health and then application performance those things we mostly have checked that so mostly system health and then application performance those things we mostly have checked that so there's a thing mostly system health and then application performance those things we mostly have checked that so there's a thing most mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance also like mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance also like to mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance also like to identify Mostly system health and then application performance. Those things. We mostly have checked that. So there's a thing. Mostly we go on with performance. Also like to identify. of of resource Of resource. efficiency efficiency and everything efficiency and everything to optim efficiency and everything to optimize efficiency and everything to optimize that also Efficiency and everything to optimize that also. sometimes the sometimes the deployment sometimes the deployment is keep on Sometimes the deployment is. Keep on. i mean i mean it won't deploy I mean, it won't deploy. these are only these are only very gener these are only very generic defin these are only very generic definitions these are only very generic definitions of metrics these are only very generic definitions of metrics and things these are only very generic definitions of metrics and things that you can these are only very generic definitions of metrics and things that you can implement these are only very generic definitions of metrics and things that you can implement i want these are only very generic definitions of metrics and things that you can implement i want to these are only very generic definitions of metrics and things that you can implement i want to understand these are only very generic definitions of metrics and things that you can implement i want to understand a little more these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the met these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specific these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specifically to that these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specifically to that i do these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specifically to that i do definition These are only very generic definitions of metrics and things that you can implement. I want to understand a little more in detail about what you have implemented in your project, then what are the metrics that you have done in your project. Let's talk about specifically to that. I do? Definition. okay
2024-08-24 15:16:24.887467: Some of the metrics that you took in case and took it out, and it's what were your notes that you gave out? in the graf in the grafan in the grafana In the Grafana. system system like application system like application docum system like application documents System, like application documents. like like kind of any like kind of any late response like kind of any late response it is coming like kind of any late response it is coming it like kind of any late response it is coming it is stuck like kind of any late response it is coming it is stuck any like kind of any late response it is coming it is stuck anywhere like kind of any late response it is coming it is stuck anywhere like error like kind of any late response it is coming it is stuck anywhere like error rate like kind of any late response it is coming it is stuck anywhere like error rates like kind of any late response it is coming it is stuck anywhere like error rates resource like kind of any late response it is coming it is stuck anywhere like error rates resource replace like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disrupt like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this also can like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this also can check like kind of any late response it is coming it is stuck anywhere like error rates resource replacement like monitoring cpu those kind of things and deployment disruptors are not like this also can check actually Like kind of any late response. It is coming. It is stuck anywhere, like error rates, resource replacement. Like monitoring CPU, those kind of things. And deployment disruptors are not like this. Also can check actually. mostly system mostly system health mostly system health and then application mostly system health and then application performance mostly system health and then application performance those mostly system health and then application performance those things mostly system health and then application performance those things we mostly mostly system health and then application performance those things we mostly have mostly system health and then application performance those things we mostly have checked mostly system health and then application performance those things we mostly have checked that mostly system health and then application performance those things we mostly have checked that so mostly system health and then application performance those things we mostly have checked that so there's a thing mostly system health and then application performance those things we mostly have checked that so there's a thing most mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance also like mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance also like to mostly system health and then application performance those things we mostly have checked that so there's a thing mostly we go on with performance also like to identify Mostly system health and then application performance. Those things. We mostly have checked that. So there's a thing. Mostly we go on with performance. Also like to identify. of of resource Of resource. efficiency efficiency and everything efficiency and everything to optim efficiency and everything to optimize efficiency and everything to optimize that also Efficiency and everything to optimize that also. sometimes the sometimes the deployment sometimes the deployment is keep on Sometimes the deployment is. Keep on. i mean i mean it won't deploy I mean, it won't deploy. these are only these are only very gener these are only very generic defin these are only very generic definitions these are only very generic definitions of metrics these are only very generic definitions of metrics and things these are only very generic definitions of metrics and things that you can these are only very generic definitions of metrics and things that you can implement these are only very generic definitions of metrics and things that you can implement i want these are only very generic definitions of metrics and things that you can implement i want to these are only very generic definitions of metrics and things that you can implement i want to understand these are only very generic definitions of metrics and things that you can implement i want to understand a little more these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the met these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specific these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specifically to that these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specifically to that i do these are only very generic definitions of metrics and things that you can implement i want to understand a little more in detail about what you have implemented in your project then what are the metrics that you have done in your project let's talk about specifically to that i do definition These are only very generic definitions of metrics and things that you can implement. I want to understand a little more in detail about what you have implemented in your project, then what are the metrics that you have done in your project. Let's talk about specifically to that. I do? Definition. okay
2024-08-24 15:17:49.108774: Okay. yeah yeah like yeah like in my project Yeah, like in my project. we have done We have done. like like error rate like error rates like error rates that's the same like error rates that's the same thing like error rates that's the same thing and Like error rates. That's the same thing. And. how much How much? image image while giving that image while giving that image image while giving that image the clarity image while giving that image the clarity of the imag image while giving that image the clarity of the image image while giving that image the clarity of the image and then image while giving that image the clarity of the image and then we have image while giving that image the clarity of the image and then we have done image while giving that image the clarity of the image and then we have done like image while giving that image the clarity of the image and then we have done like object image while giving that image the clarity of the image and then we have done like object in object image while giving that image the clarity of the image and then we have done like object in object that is Image while giving that image the clarity of the image. And then we have done like object in object that is. what what is the met what is the metric for what is the metric for clarity what is the metric for clarity of imag what is the metric for clarity of image What is the metric for clarity of image? like like metric Like metric. pixel pixel size and three pixel size and three by three pixel size and three by three and then pixel size and three by three and then like that pixel size and three by three and then like that we pixel size and three by three and then like that we are wrong with pixel size and three by three and then like that we are wrong with that pixel size and three by three and then like that we are wrong with that the pixel size and three by three and then like that we are wrong with that the response pixel size and three by three and then like that we are wrong with that the response la pixel size and three by three and then like that we are wrong with that the response latency pixel size and three by three and then like that we are wrong with that the response latency that's also we have pixel size and three by three and then like that we are wrong with that the response latency that's also we have used Pixel size and three by three and then like that. We are wrong with that. The response latency that's also we have used. like Like. response and response and weight and fix response and weight and fix not a real time response and weight and fix not a real time moderate Response and weight and fix not a real time moderate. i'm i'm sorry I'm sorry. batch Batch. you mentioned you mentioned it's a batch you mentioned it's a batch job you mentioned it's a batch job you you mentioned it's a batch job you get like you mentioned it's a batch job you get like a patch of you mentioned it's a batch job you get like a patch of finger you mentioned it's a batch job you get like a patch of fingers you mentioned it's a batch job you get like a patch of fingers or something like you mentioned it's a batch job you get like a patch of fingers or something like that you mentioned it's a batch job you get like a patch of fingers or something like that yeah so you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the respons you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or laten you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or latency matter you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or latency matter for you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or latency matter for this use case You mentioned. It's a batch job. You get like a patch of fingers or something like. That. Yeah. So how does the response or latency matter for this use case? sometimes sometimes actually Sometimes, actually. even even the even the images even the images come even the images come with the overl even the images come with the overlap even the images come with the overlap and then even the images come with the overlap and then when doing even the images come with the overlap and then when doing the quality even the images come with the overlap and then when doing the quality check even the images come with the overlap and then when doing the quality check so even the images come with the overlap and then when doing the quality check so we have even the images come with the overlap and then when doing the quality check so we have implemented a job even the images come with the overlap and then when doing the quality check so we have implemented a job like even the images come with the overlap and then when doing the quality check so we have implemented a job like if even the images come with the overlap and then when doing the quality check so we have implemented a job like if the even the images come with the overlap and then when doing the quality check so we have implemented a job like if the images even the images come with the overlap and then when doing the quality check so we have implemented a job like if the images are Even the images come with the overlap and then when doing the quality check. So we have implemented a job. Like if the images are. in in low pict in low picture rate in low picture rates in low picture rates it automatically in low picture rates it automatically should in low picture rates it automatically should give the in low picture rates it automatically should give the pixel in low picture rates it automatically should give the pixelate high in low picture rates it automatically should give the pixelate high and in low picture rates it automatically should give the pixelate high and then go in low picture rates it automatically should give the pixelate high and then go with in low picture rates it automatically should give the pixelate high and then go with the model in low picture rates it automatically should give the pixelate high and then go with the model actually in low picture rates it automatically should give the pixelate high and then go with the model actually so in low picture rates it automatically should give the pixelate high and then go with the model actually so in in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time actually in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time actually what in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time actually what happens when In low picture rates, it automatically should give the pixelate high and then go with the model. Actually. So in that time, actually, what happens when. three x three x three two three x three two somewhere Three x three two somewhere.
2024-08-24 15:17:49.110300: Okay. yeah yeah like yeah like in my project Yeah, like in my project. we have done We have done. like like error rate like error rates like error rates that's the same like error rates that's the same thing like error rates that's the same thing and Like error rates. That's the same thing. And. how much How much? image image while giving that image while giving that image image while giving that image the clarity image while giving that image the clarity of the imag image while giving that image the clarity of the image image while giving that image the clarity of the image and then image while giving that image the clarity of the image and then we have image while giving that image the clarity of the image and then we have done image while giving that image the clarity of the image and then we have done like image while giving that image the clarity of the image and then we have done like object image while giving that image the clarity of the image and then we have done like object in object image while giving that image the clarity of the image and then we have done like object in object that is Image while giving that image the clarity of the image. And then we have done like object in object that is. what what is the met what is the metric for what is the metric for clarity what is the metric for clarity of imag what is the metric for clarity of image What is the metric for clarity of image? like like metric Like metric. pixel pixel size and three pixel size and three by three pixel size and three by three and then pixel size and three by three and then like that pixel size and three by three and then like that we pixel size and three by three and then like that we are wrong with pixel size and three by three and then like that we are wrong with that pixel size and three by three and then like that we are wrong with that the pixel size and three by three and then like that we are wrong with that the response pixel size and three by three and then like that we are wrong with that the response la pixel size and three by three and then like that we are wrong with that the response latency pixel size and three by three and then like that we are wrong with that the response latency that's also we have pixel size and three by three and then like that we are wrong with that the response latency that's also we have used Pixel size and three by three and then like that. We are wrong with that. The response latency that's also we have used. like Like. response and response and weight and fix response and weight and fix not a real time response and weight and fix not a real time moderate Response and weight and fix not a real time moderate. i'm i'm sorry I'm sorry. batch Batch. you mentioned you mentioned it's a batch you mentioned it's a batch job you mentioned it's a batch job you you mentioned it's a batch job you get like you mentioned it's a batch job you get like a patch of you mentioned it's a batch job you get like a patch of finger you mentioned it's a batch job you get like a patch of fingers you mentioned it's a batch job you get like a patch of fingers or something like you mentioned it's a batch job you get like a patch of fingers or something like that you mentioned it's a batch job you get like a patch of fingers or something like that yeah so you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the respons you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or laten you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or latency matter you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or latency matter for you mentioned it's a batch job you get like a patch of fingers or something like that yeah so how does the response or latency matter for this use case You mentioned. It's a batch job. You get like a patch of fingers or something like. That. Yeah. So how does the response or latency matter for this use case? sometimes sometimes actually Sometimes, actually. even even the even the images even the images come even the images come with the overl even the images come with the overlap even the images come with the overlap and then even the images come with the overlap and then when doing even the images come with the overlap and then when doing the quality even the images come with the overlap and then when doing the quality check even the images come with the overlap and then when doing the quality check so even the images come with the overlap and then when doing the quality check so we have even the images come with the overlap and then when doing the quality check so we have implemented a job even the images come with the overlap and then when doing the quality check so we have implemented a job like even the images come with the overlap and then when doing the quality check so we have implemented a job like if even the images come with the overlap and then when doing the quality check so we have implemented a job like if the even the images come with the overlap and then when doing the quality check so we have implemented a job like if the images even the images come with the overlap and then when doing the quality check so we have implemented a job like if the images are Even the images come with the overlap and then when doing the quality check. So we have implemented a job. Like if the images are. in in low pict in low picture rate in low picture rates in low picture rates it automatically in low picture rates it automatically should in low picture rates it automatically should give the in low picture rates it automatically should give the pixel in low picture rates it automatically should give the pixelate high in low picture rates it automatically should give the pixelate high and in low picture rates it automatically should give the pixelate high and then go in low picture rates it automatically should give the pixelate high and then go with in low picture rates it automatically should give the pixelate high and then go with the model in low picture rates it automatically should give the pixelate high and then go with the model actually in low picture rates it automatically should give the pixelate high and then go with the model actually so in low picture rates it automatically should give the pixelate high and then go with the model actually so in in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time actually in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time actually what in low picture rates it automatically should give the pixelate high and then go with the model actually so in that time actually what happens when In low picture rates, it automatically should give the pixelate high and then go with the model. Actually. So in that time, actually, what happens when. three x three x three two three x three two somewhere Three x three two somewhere.
2024-08-24 15:18:13.198941: yeah yeah can you define yeah can you define what yeah can you define what is latency yeah can you define what is latency and how yeah can you define what is latency and how it yeah can you define what is latency and how it is important yeah can you define what is latency and how it is important in this yeah can you define what is latency and how it is important in this project Yeah. Can you define what is latency and how it is important in this project? the agency the agency is like the agency is like what is the laten the agency is like what is the latency The agency is like, what is the latency? laten latency latency means generally latency means generally it latency means generally it will latency means generally it will give Latency means generally it will give. the The. sometimes Sometimes. exactly exactly means which Exactly means, which? okay
2024-08-24 15:18:13.198941: yeah yeah can you define yeah can you define what yeah can you define what is latency yeah can you define what is latency and how yeah can you define what is latency and how it yeah can you define what is latency and how it is important yeah can you define what is latency and how it is important in this yeah can you define what is latency and how it is important in this project Yeah. Can you define what is latency and how it is important in this project? the agency the agency is like the agency is like what is the laten the agency is like what is the latency The agency is like, what is the latency? laten latency latency means generally latency means generally it latency means generally it will latency means generally it will give Latency means generally it will give. the The. sometimes Sometimes. exactly exactly means which Exactly means, which? okay
2024-08-24 15:18:21.298049: Okay. overall i'm good overall i'm good from my end overall i'm good from my end do overall i'm good from my end do you have any overall i'm good from my end do you have any questions overall i'm good from my end do you have any questions no Overall, I'm good from my end. Do you have any questions? No. all right all right thank
2024-08-24 15:18:21.298049: Okay. overall i'm good overall i'm good from my end overall i'm good from my end do overall i'm good from my end do you have any overall i'm good from my end do you have any questions overall i'm good from my end do you have any questions no Overall, I'm good from my end. Do you have any questions? No. all right all right thank
