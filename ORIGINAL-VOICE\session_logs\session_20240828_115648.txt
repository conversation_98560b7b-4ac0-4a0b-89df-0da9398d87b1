2024-08-28 11:57:57.927170: can you can you explain can you explain what can you explain what is can you explain what is hypothes can you explain what is hypothesis
2024-08-28 11:57:57.930246: can you can you explain can you explain what can you explain what is can you explain what is hypothes can you explain what is hypothesis
2024-08-28 11:58:45.989314: can you explain what is hypothesis testing Can you explain what is hypothesis testing? can you can you provide can you provide me Can you provide me? a a brief a brief of your a brief of your first a brief of your first project a brief of your first project and a brief of your first project and how did a brief of your first project and how did you ident a brief of your first project and how did you identify a brief of your first project and how did you identify anom a brief of your first project and how did you identify anomalies a brief of your first project and how did you identify anomalies and A brief of your first project, and how did you identify anomalies and. how did you how did you fine tun how did you fine tune them How did you fine tune them? and and how and how did you and how did you find and how did you find out and how did you find outlayers And how did you find outlayers?
2024-08-28 11:58:45.991332: can you explain what is hypothesis testing Can you explain what is hypothesis testing? can you can you provide can you provide me Can you provide me? a a brief a brief of your a brief of your first a brief of your first project a brief of your first project and a brief of your first project and how did a brief of your first project and how did you ident a brief of your first project and how did you identify a brief of your first project and how did you identify anom a brief of your first project and how did you identify anomalies a brief of your first project and how did you identify anomalies and A brief of your first project, and how did you identify anomalies and. how did you how did you fine tun how did you fine tune them How did you fine tune them? and and how and how did you and how did you find and how did you find out and how did you find outlayers And how did you find outlayers?
2024-08-28 11:59:18.416862: i i asked you I asked you. to to in your to in your project to in your project how did to in your project how did you identify To in your project, how did you identify.
2024-08-28 11:59:18.416862: i i asked you I asked you. to to in your to in your project to in your project how did to in your project how did you identify To in your project, how did you identify.
2024-08-28 12:06:22.620825: okay Okay. yes Yes. hello Hello. can you can you turn on can you turn on your camera can you turn on your camera yes please Can you turn on your camera? Yes, please. yeah yeah so yeah so how are yeah so how are you doing today yeah so how are you doing today good Yeah. So how are you doing today? Good. for you For you. yes sir yes sir good as well yes sir good as well so if you're ready yes sir good as well so if you're ready we'll get yes sir good as well so if you're ready we'll get started yes sir good as well so if you're ready we'll get started now yes sir good as well so if you're ready we'll get started now okay Yes, sir. Good as well. So if you're ready, we'll get started now. Okay. please tell please tell us about please tell us about a career journ please tell us about a career journey please tell us about a career journey and your technical please tell us about a career journey and your technical speaker please tell us about a career journey and your technical speaker okay please tell us about a career journey and your technical speaker okay fine Please tell us about a career journey and your technical speaker. Okay, fine. and i'm and i'm having and i'm having two late point one and i'm having two late point one years in and i'm having two late point one years in it And I'm having two late point one years in it. in my in my kb in my kb in korea in my kb in korea as a cloud in my kb in korea as a cloud admin after in my kb in korea as a cloud admin after that i moved in my kb in korea as a cloud admin after that i moved to this data in my kb in korea as a cloud admin after that i moved to this data science anal in my kb in korea as a cloud admin after that i moved to this data science analytics in my kb in korea as a cloud admin after that i moved to this data science analytics under In my KB in Korea as a cloud admin. After that, I moved to this data science. Analytics under. am aml aml about aml about with python aml about with python like aml about with python like library aml about with python like library select aml about with python like library selectors like numpy aml about with python like library selectors like numpy pan aml about with python like library selectors like numpy pandas aml about with python like library selectors like numpy pandas sci fi aml about with python like library selectors like numpy pandas sci fi tensor aml about with python like library selectors like numpy pandas sci fi tensorflow aml about with python like library selectors like numpy pandas sci fi tensorflow nlt aml about with python like library selectors like numpy pandas sci fi tensorflow nltk aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stac aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for data visuration aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for data visuration i aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for data visuration i use Aml about with python like library selectors, like numpy pandas, Sci-Fi tensorflow, nltk, Stacy and cyclone, those kind of things I worked on and for data. Visuration I use. like Like. mapr mapreduce mapreduce cbo mapreduce cbo and plot mapreduce cbo and plotlib and mapreduce cbo and plotlib and power bi mapreduce cbo and plotlib and power bi those things mapreduce cbo and plotlib and power bi those things i have used mapreduce cbo and plotlib and power bi those things i have used on Mapreduce, CBO and plotlib and power bi those things I have used on. experience experience with experience with static anal experience with static analysis experience with static analysis like including experience with static analysis like including the hypoth experience with static analysis like including the hypothesis experience with static analysis like including the hypothesis like prob experience with static analysis like including the hypothesis like probability theory experience with static analysis like including the hypothesis like probability theory and experience with static analysis like including the hypothesis like probability theory and regression analys experience with static analysis like including the hypothesis like probability theory and regression analysis experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assess experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment those experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment those things experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment those things are available Experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment. Those things are available. in machine learning in machine learning i in machine learning i worked on in machine learning i worked on project in machine learning i worked on project like in machine learning i worked on project like reg in machine learning i worked on project like regression in machine learning i worked on project like regression classification in machine learning i worked on project like regression classifications In machine learning. I worked on project like regression classifications. clust clustering and clustering and recommendation clustering and recommendation system clustering and recommendation systems clustering and recommendation systems ensem clustering and recommendation systems ensemble met clustering and recommendation systems ensemble methods those things clustering and recommendation systems ensemble methods those things i have clustering and recommendation systems ensemble methods those things i have overdone clustering and recommendation systems ensemble methods those things i have overdone i have clustering and recommendation systems ensemble methods those things i have overdone i have hands on clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nl clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensor clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow space clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch open clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch openc clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of work clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of work on most clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of work on mostly Clustering and recommendation systems, ensemble methods. Those things I have overdone, I have hands on. Experience with NLP as well, like in frameworks like Keras Tensorflow, Spacey Bird and bright orch opencv those kind of work on mostly. and and i and i worked on and i worked on one and i worked on one ap and i worked on one api develop and i worked on one api development part and i worked on one api development part of the project and i worked on one api development part of the project as and i worked on one api development part of the project as well And I worked on one API development part of the project as well. and in and in data and in database and in database i and in database i work more and in database i work more with and in database i work more with mysq and in database i work more with mysql and and in database i work more with mysql and sqls and in database i work more with mysql and sqls most and in database i work more with mysql and sqls mostly and in database i work more with mysql and sqls mostly and then And in database. I work more with MySQL and sqls mostly and then. in in noise skill in noise skill database in noise skill database i have in noise skill database i have worked on with in noise skill database i have worked on with cassand in noise skill database i have worked on with cassandra in noise skill database i have worked on with cassandra how In noise skill database I have worked on with Cassandra. How. this part this part and this part and coming to this part and coming to my this part and coming to my recent project This part and coming to my recent project. our our goal was to our goal was to develop our goal was to develop a our goal was to develop a system our goal was to develop a system to our goal was to develop a system to that our goal was to develop a system to that could accurately our goal was to develop a system to that could accurately identify Our goal was to develop a system to that could accurately identify. and separ and separate and separate the plast and separate the plastic type and separate the plastic types actually when and separate the plastic types actually when they're mix and separate the plastic types actually when they're mixed together and separate the plastic types actually when they're mixed together and to and separate the plastic types actually when they're mixed together and to identify and separate the plastic types actually when they're mixed together and to identify the and separate the plastic types actually when they're mixed together and to identify the chemical and separate the plastic types actually when they're mixed together and to identify the chemical contamina and separate the plastic types actually when they're mixed together and to identify the chemical contamination and separate the plastic types actually when they're mixed together and to identify the chemical contamination and and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic object and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distrib and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distribution between and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distribution between these plastic and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distribution between these plastic types And separate the plastic types actually when they're mixed together, and to identify the chemical contamination. And the plastic objects also, and to find the features and patterns from the distribution between these plastic types. what what it will do is what it will do is it will what it will do is it will give what it will do is it will give the what it will do is it will give the more efficient what it will do is it will give the more efficient recycling what it will do is it will give the more efficient recycling and what it will do is it will give the more efficient recycling and waste what it will do is it will give the more efficient recycling and waste management what it will do is it will give the more efficient recycling and waste management in what it will do is it will give the more efficient recycling and waste management in the part and what it will do is it will give the more efficient recycling and waste management in the part and it will what it will do is it will give the more efficient recycling and waste management in the part and it will decrease what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second part so what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second part so that's the thing What it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second part. So that's the thing. if you want if you want to if you want to explain if you want to explain further if you want to explain further i can go if you want to explain further i can go ahead If you want to explain further, I can go ahead. talk about talk about specific talk about specific technolog talk about specific technologies now so talk about specific technologies now so mr talk about specific technologies now so mr water talk about specific technologies now so mr water expense talk about specific technologies now so mr water expense is gener talk about specific technologies now so mr water expense is generative talk about specific technologies now so mr water expense is generative air Talk about specific technologies now. So, Mr. Water expense is generative air. generative generative a generative ai generative ai i generative ai i work for generative ai i work for only generative ai i work for only one project generative ai i work for only one project actually generative ai i work for only one project actually like generative ai i work for only one project actually like with Generative AI. I work for only one project actually, like with. when when going with the when going with the text analys when going with the text analysis when going with the text analysis part When going with the text analysis part. like Like. previously Previously. in genetic in genetic way in genetic way i don in genetic way i don't have much in genetic way i don't have much experience in genetic way i don't have much experience actually in genetic way i don't have much experience actually so i in genetic way i don't have much experience actually so i worked only in genetic way i don't have much experience actually so i worked only for one in genetic way i don't have much experience actually so i worked only for one llm project in genetic way i don't have much experience actually so i worked only for one llm project with in genetic way i don't have much experience actually so i worked only for one llm project with y by using in genetic way i don't have much experience actually so i worked only for one llm project with y by using the in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ib in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so we need to in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so we need to classify in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so we need to classify the In genetic way. I don't have much experience, actually, so I worked only for one LLM. Project with y by using the bulk while doing with the IBM we have instant management system. There. So we need to classify the. ident identify the identify the incident identify the incident type and then identify the incident type and then we need to Identify the incident type, and then we need to. autom automate automate the automate the incident Automate the incident. class classification Classification. which which kind of which
2024-08-28 12:06:22.620825: okay Okay. yes Yes. hello Hello. can you can you turn on can you turn on your camera can you turn on your camera yes please Can you turn on your camera? Yes, please. yeah yeah so yeah so how are yeah so how are you doing today yeah so how are you doing today good Yeah. So how are you doing today? Good. for you For you. yes sir yes sir good as well yes sir good as well so if you're ready yes sir good as well so if you're ready we'll get yes sir good as well so if you're ready we'll get started yes sir good as well so if you're ready we'll get started now yes sir good as well so if you're ready we'll get started now okay Yes, sir. Good as well. So if you're ready, we'll get started now. Okay. please tell please tell us about please tell us about a career journ please tell us about a career journey please tell us about a career journey and your technical please tell us about a career journey and your technical speaker please tell us about a career journey and your technical speaker okay please tell us about a career journey and your technical speaker okay fine Please tell us about a career journey and your technical speaker. Okay, fine. and i'm and i'm having and i'm having two late point one and i'm having two late point one years in and i'm having two late point one years in it And I'm having two late point one years in it. in my in my kb in my kb in korea in my kb in korea as a cloud in my kb in korea as a cloud admin after in my kb in korea as a cloud admin after that i moved in my kb in korea as a cloud admin after that i moved to this data in my kb in korea as a cloud admin after that i moved to this data science anal in my kb in korea as a cloud admin after that i moved to this data science analytics in my kb in korea as a cloud admin after that i moved to this data science analytics under In my KB in Korea as a cloud admin. After that, I moved to this data science. Analytics under. am aml aml about aml about with python aml about with python like aml about with python like library aml about with python like library select aml about with python like library selectors like numpy aml about with python like library selectors like numpy pan aml about with python like library selectors like numpy pandas aml about with python like library selectors like numpy pandas sci fi aml about with python like library selectors like numpy pandas sci fi tensor aml about with python like library selectors like numpy pandas sci fi tensorflow aml about with python like library selectors like numpy pandas sci fi tensorflow nlt aml about with python like library selectors like numpy pandas sci fi tensorflow nltk aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stac aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for data visuration aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for data visuration i aml about with python like library selectors like numpy pandas sci fi tensorflow nltk stacy and cyclone those kind of things i worked on and for data visuration i use Aml about with python like library selectors, like numpy pandas, Sci-Fi tensorflow, nltk, Stacy and cyclone, those kind of things I worked on and for data. Visuration I use. like Like. mapr mapreduce mapreduce cbo mapreduce cbo and plot mapreduce cbo and plotlib and mapreduce cbo and plotlib and power bi mapreduce cbo and plotlib and power bi those things mapreduce cbo and plotlib and power bi those things i have used mapreduce cbo and plotlib and power bi those things i have used on Mapreduce, CBO and plotlib and power bi those things I have used on. experience experience with experience with static anal experience with static analysis experience with static analysis like including experience with static analysis like including the hypoth experience with static analysis like including the hypothesis experience with static analysis like including the hypothesis like prob experience with static analysis like including the hypothesis like probability theory experience with static analysis like including the hypothesis like probability theory and experience with static analysis like including the hypothesis like probability theory and regression analys experience with static analysis like including the hypothesis like probability theory and regression analysis experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assess experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment those experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment those things experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment those things are available Experience with static analysis like including the hypothesis like probability theory and regression analysis like risk assessment. Those things are available. in machine learning in machine learning i in machine learning i worked on in machine learning i worked on project in machine learning i worked on project like in machine learning i worked on project like reg in machine learning i worked on project like regression in machine learning i worked on project like regression classification in machine learning i worked on project like regression classifications In machine learning. I worked on project like regression classifications. clust clustering and clustering and recommendation clustering and recommendation system clustering and recommendation systems clustering and recommendation systems ensem clustering and recommendation systems ensemble met clustering and recommendation systems ensemble methods those things clustering and recommendation systems ensemble methods those things i have clustering and recommendation systems ensemble methods those things i have overdone clustering and recommendation systems ensemble methods those things i have overdone i have clustering and recommendation systems ensemble methods those things i have overdone i have hands on clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nl clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensor clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow space clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch open clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch openc clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of work clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of work on most clustering and recommendation systems ensemble methods those things i have overdone i have hands on experience with nlp as well like in frameworks like keras tensorflow spacey bird and bright orch opencv those kind of work on mostly Clustering and recommendation systems, ensemble methods. Those things I have overdone, I have hands on. Experience with NLP as well, like in frameworks like Keras Tensorflow, Spacey Bird and bright orch opencv those kind of work on mostly. and and i and i worked on and i worked on one and i worked on one ap and i worked on one api develop and i worked on one api development part and i worked on one api development part of the project and i worked on one api development part of the project as and i worked on one api development part of the project as well And I worked on one API development part of the project as well. and in and in data and in database and in database i and in database i work more and in database i work more with and in database i work more with mysq and in database i work more with mysql and and in database i work more with mysql and sqls and in database i work more with mysql and sqls most and in database i work more with mysql and sqls mostly and in database i work more with mysql and sqls mostly and then And in database. I work more with MySQL and sqls mostly and then. in in noise skill in noise skill database in noise skill database i have in noise skill database i have worked on with in noise skill database i have worked on with cassand in noise skill database i have worked on with cassandra in noise skill database i have worked on with cassandra how In noise skill database I have worked on with Cassandra. How. this part this part and this part and coming to this part and coming to my this part and coming to my recent project This part and coming to my recent project. our our goal was to our goal was to develop our goal was to develop a our goal was to develop a system our goal was to develop a system to our goal was to develop a system to that our goal was to develop a system to that could accurately our goal was to develop a system to that could accurately identify Our goal was to develop a system to that could accurately identify. and separ and separate and separate the plast and separate the plastic type and separate the plastic types actually when and separate the plastic types actually when they're mix and separate the plastic types actually when they're mixed together and separate the plastic types actually when they're mixed together and to and separate the plastic types actually when they're mixed together and to identify and separate the plastic types actually when they're mixed together and to identify the and separate the plastic types actually when they're mixed together and to identify the chemical and separate the plastic types actually when they're mixed together and to identify the chemical contamina and separate the plastic types actually when they're mixed together and to identify the chemical contamination and separate the plastic types actually when they're mixed together and to identify the chemical contamination and and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic object and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distrib and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distribution between and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distribution between these plastic and separate the plastic types actually when they're mixed together and to identify the chemical contamination and the plastic objects also and to find the features and patterns from the distribution between these plastic types And separate the plastic types actually when they're mixed together, and to identify the chemical contamination. And the plastic objects also, and to find the features and patterns from the distribution between these plastic types. what what it will do is what it will do is it will what it will do is it will give what it will do is it will give the what it will do is it will give the more efficient what it will do is it will give the more efficient recycling what it will do is it will give the more efficient recycling and what it will do is it will give the more efficient recycling and waste what it will do is it will give the more efficient recycling and waste management what it will do is it will give the more efficient recycling and waste management in what it will do is it will give the more efficient recycling and waste management in the part and what it will do is it will give the more efficient recycling and waste management in the part and it will what it will do is it will give the more efficient recycling and waste management in the part and it will decrease what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second part so what it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second part so that's the thing What it will do is it will give the more efficient recycling and waste management in the part and it will decrease operating cost while doing the second part. So that's the thing. if you want if you want to if you want to explain if you want to explain further if you want to explain further i can go if you want to explain further i can go ahead If you want to explain further, I can go ahead. talk about talk about specific talk about specific technolog talk about specific technologies now so talk about specific technologies now so mr talk about specific technologies now so mr water talk about specific technologies now so mr water expense talk about specific technologies now so mr water expense is gener talk about specific technologies now so mr water expense is generative talk about specific technologies now so mr water expense is generative air Talk about specific technologies now. So, Mr. Water expense is generative air. generative generative a generative ai generative ai i generative ai i work for generative ai i work for only generative ai i work for only one project generative ai i work for only one project actually generative ai i work for only one project actually like generative ai i work for only one project actually like with Generative AI. I work for only one project actually, like with. when when going with the when going with the text analys when going with the text analysis when going with the text analysis part When going with the text analysis part. like Like. previously Previously. in genetic in genetic way in genetic way i don in genetic way i don't have much in genetic way i don't have much experience in genetic way i don't have much experience actually in genetic way i don't have much experience actually so i in genetic way i don't have much experience actually so i worked only in genetic way i don't have much experience actually so i worked only for one in genetic way i don't have much experience actually so i worked only for one llm project in genetic way i don't have much experience actually so i worked only for one llm project with in genetic way i don't have much experience actually so i worked only for one llm project with y by using in genetic way i don't have much experience actually so i worked only for one llm project with y by using the in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ib in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so we need to in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so we need to classify in genetic way i don't have much experience actually so i worked only for one llm project with y by using the bulk while doing with the ibm we have instant management system there so we need to classify the In genetic way. I don't have much experience, actually, so I worked only for one LLM. Project with y by using the bulk while doing with the IBM we have instant management system. There. So we need to classify the. ident identify the identify the incident identify the incident type and then identify the incident type and then we need to Identify the incident type, and then we need to. autom automate automate the automate the incident Automate the incident. class classification Classification. which which kind of which
2024-08-28 12:06:42.554560: but But. it's it's bi direct it's bi directional It's bi directional. from the From the. screen Screen. path path and everything Path and everything.
2024-08-28 12:06:42.557083: but But. it's it's bi direct it's bi directional It's bi directional. from the From the. screen Screen. path path and everything Path and everything.
2024-08-28 12:07:02.418899: transformers Transformers. i'm not i'm not expert i'm not expertise in i'm not expertise in janae i'm not expertise in janae i think I'm not expertise in Janae, I think. glue know what glue know what gans glue know what gans yeah glue know what gans yeah i know gans glue know what gans yeah i know gans actually Glue. Know what? Gans. Yeah, I know gans, actually.
2024-08-28 12:07:02.421476: transformers Transformers. i'm not i'm not expert i'm not expertise in i'm not expertise in janae i'm not expertise in janae i think I'm not expertise in Janae, I think. glue know what glue know what gans glue know what gans yeah glue know what gans yeah i know gans glue know what gans yeah i know gans actually Glue. Know what? Gans. Yeah, I know gans, actually.
2024-08-28 12:07:19.556366: like Like. in gams in gams we in gams we have done in gams we have done with In gams we have done with. nlp nlp process nlp process we have used nlp process we have used this nlp process we have used this gans process nlp process we have used this gans process like nlp process we have used this gans process like self nlp process we have used this gans process like self adoration nlp process we have used this gans process like self adoration and nlp process we have used this gans process like self adoration and like with the nlp process we have used this gans process like self adoration and like with the allowing nlp process we have used this gans process like self adoration and like with the allowing the const nlp process we have used this gans process like self adoration and like with the allowing the constraint nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of words nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of words like nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of words like incentives
2024-08-28 12:07:19.557372: like Like. in gams in gams we in gams we have done in gams we have done with In gams we have done with. nlp nlp process nlp process we have used nlp process we have used this nlp process we have used this gans process nlp process we have used this gans process like nlp process we have used this gans process like self nlp process we have used this gans process like self adoration nlp process we have used this gans process like self adoration and nlp process we have used this gans process like self adoration and like with the nlp process we have used this gans process like self adoration and like with the allowing nlp process we have used this gans process like self adoration and like with the allowing the const nlp process we have used this gans process like self adoration and like with the allowing the constraint nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of words nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of words like nlp process we have used this gans process like self adoration and like with the allowing the constraint the entire sequence of words like incentives
